{"openapi": "3.0.1", "info": {"title": "Srv-company API", "version": "5.0.0"}, "servers": [{"url": ""}, {"url": "/api/srv-company"}], "security": [{"bearerAuth": []}], "paths": {"/migration/folder/replace-special-characters-informations-and-revisions": {"put": {"tags": ["migration-folder-structure-in-cadr-template-controller"], "operationId": "replaceSpecialCharactersInInformationsAndRevisions", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/migration/folder/company-template": {"put": {"tags": ["migration-folder-structure-in-cadr-template-controller"], "operationId": "migrateFolderStructureInCompany", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MigrationResult"}}}}}}}, "/migration/folder/cadr-template": {"put": {"tags": ["migration-folder-structure-in-cadr-template-controller"], "operationId": "migrateFolderStructureInTemplate", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MigrationResult"}}}}}}}, "/information": {"get": {"tags": ["information-controller"], "operationId": "getAllInformation", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "includeDeleted", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Information"}}}}}}}, "put": {"tags": ["information-controller"], "operationId": "updateInformationValue", "parameters": [{"name": "informationId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Information"}}}}}}}}, "/information/{companyId}/edit-field/{fieldKey}": {"put": {"tags": ["information-controller"], "operationId": "editCompanyField", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldEditRequestBody"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldDto"}}}}}}}, "/information/visibility": {"put": {"tags": ["information-controller"], "operationId": "updateInformationVisibility", "parameters": [{"name": "informationId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "boolean"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Information"}}}}}}}, "/contact-person/{contactPersonId}": {"get": {"tags": ["company-contact-person-controller"], "operationId": "getContact<PERSON>erson", "parameters": [{"name": "contactPersonId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactPerson"}}}}}, "deprecated": true}, "put": {"tags": ["company-contact-person-controller"], "operationId": "editCont<PERSON><PERSON><PERSON>", "parameters": [{"name": "contactPersonId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactPersonDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactPerson"}}}}}, "deprecated": true}, "delete": {"tags": ["company-contact-person-controller"], "operationId": "deleteContact<PERSON>erson", "parameters": [{"name": "contactPersonId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}, "deprecated": true}}, "/company/{id}": {"get": {"tags": ["company-controller"], "operationId": "getCompanyById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "showInactiveCompanies", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Company"}}}}}}, "put": {"tags": ["company-controller"], "operationId": "updateCompany", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Company"}}}}}}, "delete": {"tags": ["company-controller"], "operationId": "deleteCompany", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/company/{companyId}/add-field-and-edit-groups": {"put": {"tags": ["company-controller"], "operationId": "addFieldAndEditCompanyGroups", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddFieldToGroupRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Company"}}}}}}}, "/company-profile/{companyId}/network/remove-node": {"put": {"tags": ["company-profile-controller"], "operationId": "removeCompanyNetworkNode", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyNetworkNode"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/company-profile/{companyId}/network/remove-edge": {"put": {"tags": ["company-profile-controller"], "operationId": "removeCompanyNetworkEdge", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Edge"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/company-profile/{companyId}/network/modify-node": {"put": {"tags": ["company-profile-controller"], "operationId": "modifyCompanyNetworkNode", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyNetworkNodeChangeDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/company-profile/{companyId}/network/modify-edge": {"put": {"tags": ["company-profile-controller"], "operationId": "modifyCompanyNetworkEdge", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyNetworkEdgeChangeDto"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/company-profile/{companyId}/network/add-node/{galleryNodeId}": {"put": {"tags": ["company-profile-controller"], "operationId": "addCompanyNetworkNode", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "galleryNodeId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/company-profile/{companyId}/network/add-edge": {"put": {"tags": ["company-profile-controller"], "operationId": "addCompanyNetworkEdge", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Edge"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/company-branch/{branchId}": {"get": {"tags": ["company-branch-controller"], "operationId": "getBranchById", "parameters": [{"name": "branchId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyBranch"}}}}}}, "put": {"tags": ["company-branch-controller"], "operationId": "updateBranch", "parameters": [{"name": "branchId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyBranchDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyBranch"}}}}}}, "delete": {"tags": ["company-branch-controller"], "operationId": "deleteBranch", "parameters": [{"name": "branchId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/cadr-template": {"get": {"tags": ["cadr-template-controller"], "operationId": "getTemplate", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CADRTemplate"}}}}}}, "put": {"tags": ["cadr-template-controller"], "operationId": "editTemplate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CADRTemplateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CADRTemplate"}}}}}}, "post": {"tags": ["cadr-template-controller"], "operationId": "createTemplate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CADRTemplateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CADRTemplate"}}}}}}}, "/cadr-template/account-manager/get-all-cadr-templates/{customerKey}": {"get": {"tags": ["cadr-template-controller"], "operationId": "getCadrTemplateByAccountManager", "parameters": [{"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CADRTemplate"}}}}}}, "put": {"tags": ["cadr-template-controller"], "operationId": "editTemplateByAccountManager", "parameters": [{"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CADRTemplateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CADRTemplate"}}}}}}}, "/migration/company/person-company-portal-access-false": {"post": {"tags": ["migration-contact-person"], "operationId": "migrateUpdateContactPersonCompanyPortalAccessFalse", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/internal/company/{baseCompanyId}/get-or-create-company": {"post": {"tags": ["company-internal-controller"], "operationId": "getOrCreateCompanyForCustomerInternal", "parameters": [{"name": "baseCompanyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "baseCustomerKey", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "newCustomerKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Company"}}}}}}}, "/internal/company/get-by-ids": {"post": {"tags": ["company-internal-controller"], "operationId": "getCompaniesByIds", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Company"}}}}}}}}, "/internal/cadr-sharing": {"post": {"tags": ["cadr-sharing-internal-controller"], "summary": "INTER-MICROSERVICE CALL. Create sharing object for customer", "operationId": "internallyCreateSharingObject", "parameters": [{"name": "baseCompanyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["cadr-sharing-internal-controller"], "summary": "INTER-MICROSERVICE CALL. Remove sharing object for customer", "operationId": "internallyRemoveSharingObject", "parameters": [{"name": "baseCompanyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "customerKeyToRemove", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/internal/cadr-sharing/explicit": {"post": {"tags": ["cadr-sharing-internal-controller"], "summary": "INTER-MICROSERVICE CALL. Create explicit sharing object for customer", "operationId": "internallyCreateExplicitSharingObject", "parameters": [{"name": "baseCompanyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/information/restore": {"post": {"tags": ["information-controller"], "operationId": "restoreRevision", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "revisionId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/demo/recall": {"post": {"tags": ["single-cluster-demo-controller"], "operationId": "recall", "parameters": [{"name": "uniqueIdentifier", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/demo/deploy": {"post": {"tags": ["single-cluster-demo-controller"], "operationId": "deploy", "parameters": [{"name": "uniqueIdentifier", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyData"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"type": "string"}}}}}}}, "/contact-person/{companyId}": {"post": {"tags": ["company-contact-person-controller"], "operationId": "addContactPerson", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactPersonDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactPerson"}}}}}, "deprecated": true}}, "/company": {"get": {"tags": ["company-controller"], "operationId": "getCompanies", "parameters": [{"name": "showInactiveCompanies", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Company"}}}}}}}, "post": {"tags": ["company-controller"], "operationId": "createCompany", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Company"}}}}}}}, "/company/{companyId}/attach-template": {"post": {"tags": ["company-controller"], "operationId": "attachTemplate", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttachCompanyTemplateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttachCompanyTemplateResponse"}}}}}}}, "/company/{companyId}/add-field": {"post": {"tags": ["company-controller"], "operationId": "addFieldToCompany", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Company"}}}}}, "deprecated": true}}, "/company/reactivate/{companyId}": {"post": {"tags": ["company-controller"], "operationId": "reactivateDeletedCompany", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Company"}}}}}}}, "/company-profile/{companyId}/network/gallery": {"get": {"tags": ["company-profile-controller"], "operationId": "getCompanyNetworkGalleryNodes", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyNetworkGalleryNode"}}}}}}}, "post": {"tags": ["company-profile-controller"], "operationId": "addNodeToNetworkGallery", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyNetworkNode"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyNetworkGalleryNode"}}}}}}, "delete": {"tags": ["company-profile-controller"], "operationId": "deleteCompanyNetworkGalleryNodes", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/company-profile/{companyId}/network/apply-updates": {"post": {"tags": ["company-profile-controller"], "operationId": "applyCompanyNetworkUpdate", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/company-graph-state": {"post": {"tags": ["company-graph-state-controller"], "operationId": "saveCompanyGraphState", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyGraphStateDto"}}}, "required": true}, "responses": {"201": {"description": "Created"}}}}, "/company-data/search-companies": {"post": {"tags": ["palturai-connect-api-controller"], "operationId": "search", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchCompanyRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PalturaiPageableResponseCompanyEntry"}}}}}}}, "/company-branch/{companyId}": {"post": {"tags": ["company-branch-controller"], "operationId": "createBranch", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyBranchDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyBranch"}}}}}}}, "/cadr-template/account-manager/create-case-related-template/{customerKey}": {"post": {"tags": ["cadr-template-controller"], "operationId": "createTemplateByAccountManager", "parameters": [{"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CADRTemplateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CADRTemplate"}}}}}}}, "/cadr-sharing": {"get": {"tags": ["cadr-sharing-controller"], "summary": "Get shared CADRs", "operationId": "getSharedCADRs", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SharedCADRDTO"}}}}}}}, "post": {"tags": ["cadr-sharing-controller"], "summary": "Create sharing object for customer", "operationId": "createSharingObject", "parameters": [{"name": "baseCompanyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["cadr-sharing-controller"], "summary": "Remove sharing object for customer", "operationId": "removeSharingObject", "parameters": [{"name": "baseCompanyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "customerKeyToRemove", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/migration/folder/complex-fields": {"get": {"tags": ["migration-folder-structure-in-cadr-template-controller"], "operationId": "checkComplexFields", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MigrationResult"}}}}}}}, "/migration/folder/company-fetch-fields-without-groups": {"get": {"tags": ["migration-folder-structure-in-cadr-template-controller"], "operationId": "fetchFieldsWithoutGroupsForCompany", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MigrationResult"}}}}}}}, "/migration/folder/cadr-template-fetch-fields-without-groups": {"get": {"tags": ["migration-folder-structure-in-cadr-template-controller"], "operationId": "fetchFieldsWithoutGroupsForCADRTemplate", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MigrationResult"}}}}}}}, "/migration/company-register-city-code": {"get": {"tags": ["company-registry-city-code-migration-controller"], "operationId": "migrate", "parameters": [{"name": "securityCode", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"$ref": "#/components/schemas/Summary"}}}}}}}, "/internal/palturai/get-company-network": {"get": {"tags": ["palturai-internal-controller"], "operationId": "getCompanyNetworkWithDetails", "parameters": [{"name": "palturaiCompanyId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"$ref": "#/components/schemas/CompanyNetwork"}}}}}}}, "/internal/palturai/get-company-details": {"get": {"tags": ["palturai-internal-controller"], "operationId": "getCompanyDetails", "parameters": [{"name": "palturaiCompanyId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"$ref": "#/components/schemas/PalturaiCompanyDetailsResponse"}}}}}}}, "/internal/palturai/find-company": {"get": {"tags": ["palturai-internal-controller"], "operationId": "getCompanyNetworkWithDetails_1", "parameters": [{"name": "registrationNumber", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "registrationCourtCity", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"$ref": "#/components/schemas/CompanyEntry"}}}}}}}, "/internal/information": {"get": {"tags": ["information-internal-controller"], "operationId": "getAllInformationInternal", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "includeDeleted", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Information"}}}}}}}}, "/internal/contact-person/remove/{customerKey}/email/{contactPersonEmail}": {"get": {"tags": ["company-contact-person-internal-controller"], "operationId": "internalRemoveContactPersonByEmail", "parameters": [{"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "contactPersonEmail", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}, "deprecated": true}}, "/internal/contact-person/all/{companyId}": {"get": {"tags": ["company-contact-person-internal-controller"], "operationId": "getContactPersonsForCompanyInternal", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ContactPerson"}}}}}}}}, "/internal/company": {"get": {"tags": ["company-internal-controller"], "operationId": "getCompaniesInternal", "parameters": [{"name": "showInactiveCompanies", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Company"}}}}}}}}, "/internal/company/{originCompanyId}/corresponding-company-for-customer/{customerKey}": {"get": {"tags": ["company-internal-controller"], "operationId": "getCorrespondingCompanyForCustomer", "parameters": [{"name": "originCompanyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Company"}}}}}}}, "/internal/company/{originCompanyId}/corresponding-companies": {"get": {"tags": ["company-internal-controller"], "operationId": "getCorrespondingCompaniesInternal", "parameters": [{"name": "originCompanyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Company"}}}}}}}}, "/internal/company/{id}": {"get": {"tags": ["company-internal-controller"], "operationId": "getCompanyByIdInternal", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "showInactiveCompanies", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Company"}}}}}}}, "/internal/company-data/company-search": {"get": {"tags": ["north-data-internal-controller"], "operationId": "searchCompany", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "domain", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "countries", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NorthDataCompany"}}}}}}}}, "/internal/company-data/company-by-registration": {"get": {"tags": ["north-data-internal-controller"], "operationId": "getCompanyByRegistration", "parameters": [{"name": "registrationId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "registrationCity", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "relations", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JsonNode"}}}}}}}, "/information/{informationKey}": {"get": {"tags": ["information-controller"], "operationId": "getInformation", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "informationKey", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Information"}}}}}}}, "/information/revisions": {"get": {"tags": ["information-controller"], "operationId": "getAllRevisions", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "informationIdentifierKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/InformationRevision"}}}}}}}}, "/information/revision/{id}": {"get": {"tags": ["information-controller"], "operationId": "getRevision", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InformationRevision"}}}}}}}, "/information/by-group": {"get": {"tags": ["information-controller"], "operationId": "getByGroup", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "group", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "includeDeleted", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}, {"name": "includeWithoutGroup", "in": "query", "required": false, "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Information"}}}}}}}, "deprecated": true}}, "/industry": {"get": {"tags": ["industry-controller"], "operationId": "getAllIndustries", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}}}}}}, "/industry/top-level": {"get": {"tags": ["industry-controller"], "operationId": "getIndustriesTopLevel", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}}}}}}, "/industry/sectors": {"get": {"tags": ["industry-controller"], "operationId": "getSectorForIndustry", "parameters": [{"name": "industry", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/demo/information": {"get": {"tags": ["single-cluster-demo-controller"], "operationId": "getCompanyInformationDemo", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Information"}}}}}}}}, "/demo/contact-person/{customerKey}/email/{contactPersonEmail}": {"get": {"tags": ["single-cluster-demo-controller"], "operationId": "getContactPersonByEmailDemo", "parameters": [{"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "contactPersonEmail", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"$ref": "#/components/schemas/ContactPersonCompanyDto"}}}}}, "deprecated": true}}, "/demo/contact-person/all/{companyId}": {"get": {"tags": ["single-cluster-demo-controller"], "operationId": "getContactPersonsForCompanyDemo", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ContactPerson"}}}}}}}}, "/demo/company/customer/": {"get": {"tags": ["single-cluster-demo-controller"], "operationId": "getCompaniesForCustomerDemo", "parameters": [{"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "showInactiveCompanies", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Company"}}}}}}}}, "/demo/company-data/search/{customerKey}": {"get": {"tags": ["single-cluster-demo-controller"], "operationId": "getCompaniesFromNorthDataDemo", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NorthDataCompany"}}}}}}}}, "/demo/company-data/get-company/by-unique-key": {"get": {"tags": ["single-cluster-demo-controller"], "operationId": "getCompaniesByUniqueKeyDemo", "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"$ref": "#/components/schemas/NorthDataCompanyInfo"}}}}}}}, "/demo/cadr-template": {"get": {"tags": ["single-cluster-demo-controller"], "operationId": "getCADRTemplateByCustomerKeyDemo", "parameters": [{"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"$ref": "#/components/schemas/CADRTemplate"}}}}}}}, "/demo/all-by-unique-identifier/{uniqueIdentifier}": {"get": {"tags": ["single-cluster-demo-controller"], "operationId": "getAllCompanyIdsByUniqueIdentifier", "parameters": [{"name": "uniqueIdentifier", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/hal+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/contact-person/{customerKey}/email/{contactPersonEmail}": {"get": {"tags": ["company-contact-person-controller"], "operationId": "getContactPersonByEmail", "parameters": [{"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "contactPersonEmail", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContactPersonCompanyDto"}}}}}, "deprecated": true}}, "/contact-person/all/{companyId}": {"get": {"tags": ["company-contact-person-controller"], "operationId": "getContactPersonsForCompany", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ContactPerson"}}}}}}, "deprecated": true}}, "/company/{originCompanyId}/corresponding-company-for-customer/{customerKey}": {"get": {"tags": ["company-controller"], "operationId": "getCorrespondingCompanyForCustomer_1", "parameters": [{"name": "originCompanyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Company"}}}}}}}, "/company/{companyId}/companies-with-same-register": {"get": {"tags": ["company-controller"], "operationId": "getCompaniesWithSameRegister", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Company"}}}}}}}}, "/company/search": {"get": {"tags": ["company-controller"], "operationId": "findCompany", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "showInactiveCompanies", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Company"}}}}}}}}, "/company/search-for-internal-customer-type": {"get": {"tags": ["company-controller"], "operationId": "findCompanyForInternalType", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "showInactiveCompanies", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Company"}}}}}}}}, "/company/customer": {"get": {"tags": ["company-controller"], "operationId": "getCompaniesForCustomer", "parameters": [{"name": "showInactiveCompanies", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Company"}}}}}}}}, "/company/company-name-search/{id}": {"get": {"tags": ["company-controller"], "operationId": "getCompanyByIdForCompanyContactPerson", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "showInactiveCompanies", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Company"}}}}}, "deprecated": true}}, "/company-profile/{companyId}": {"get": {"tags": ["company-profile-controller"], "operationId": "getCompanyProfile", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "isMockCall", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyProfile"}}}}}}}, "/company-profile/{companyId}/ubos": {"get": {"tags": ["company-profile-controller"], "operationId": "getCompanyUbos", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyUbosDto"}}}}}}}, "/company-profile/{companyId}/ubos-calculated": {"get": {"tags": ["company-profile-controller"], "operationId": "getCompanyUbosCalculated", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SharesPercentageData"}}}}}}}}, "/company-profile/{companyId}/network": {"get": {"tags": ["company-profile-controller"], "operationId": "getCompanyNetwork", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyNetworkDto"}}}}}}}, "/company-profile/{companyId}/network/updates": {"get": {"tags": ["company-profile-controller"], "operationId": "getCompanyNetworkUpdates", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyNetworkUpdateDto"}}}}}}}, "/company-profile/{companyId}/network/revisions": {"get": {"tags": ["company-profile-controller"], "operationId": "modifyCompanyNetworkEdge_1", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageCompanyNetworkRevisionWithChangeDto"}}}}}}}, "/company-profile/{companyId}/network/download-document": {"get": {"tags": ["company-profile-controller"], "operationId": "downloadNetworkDocument", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "nodeId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "documentKey", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/octet-stream": {"schema": {"$ref": "#/components/schemas/StreamingResponseBody"}}}}}}}, "/company-profile/{companyId}/network/apply-revision": {"get": {"tags": ["company-profile-controller"], "operationId": "applyRevision", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "revisionId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/company-profile/{companyId}/network-documents": {"get": {"tags": ["company-profile-controller"], "operationId": "getCompanyNetworkDocuments", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": false, "schema": {"minimum": 0, "type": "integer", "format": "int32", "default": 0}}, {"name": "size", "in": "query", "required": false, "schema": {"minimum": 1, "type": "integer", "format": "int32", "default": 10}}, {"name": "orderBy", "in": "query", "required": false, "schema": {"type": "string", "default": "companyName"}}, {"name": "direction", "in": "query", "required": false, "schema": {"type": "string", "enum": ["ASC", "DESC"], "default": "ASC"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageCompanyNetworkDocumentDto"}}}}}}}, "/company-graph-state/{companyId}": {"get": {"tags": ["company-graph-state-controller"], "operationId": "getCompanyGraphState", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyGraphStateDto"}}}}}}}, "/company-data/search/{customerKey}": {"get": {"tags": ["north-data-controller"], "operationId": "getCompaniesFromNorthData", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "customerKey", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NorthDataCompany"}}}}}}}}, "/company-data/get-company/by-unique-key": {"get": {"tags": ["north-data-controller"], "operationId": "getCompaniesByUniqueKey", "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NorthDataCompanyInfo"}}}}}}}, "/company-data/get-company/by-name": {"get": {"tags": ["north-data-controller"], "operationId": "getCompaniesByNameAndAddress", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NorthDataCompanyInfo"}}}}}}}, "/company-data/get-company/by-name-and-address": {"get": {"tags": ["north-data-controller"], "operationId": "getCompaniesByNameAndAddress_1", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "address", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NorthDataCompanyInfo"}}}}}}}, "/company-data/company/{northDataId}": {"get": {"tags": ["north-data-controller"], "operationId": "getCompanyDetails_1", "parameters": [{"name": "northDataId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JsonNode"}}}}}}}, "/company-data/company/{northDataId}/directors": {"get": {"tags": ["north-data-controller"], "operationId": "getCompanyDirectors", "parameters": [{"name": "northDataId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JsonNode"}}}}}}}, "/company-data/company-ubos": {"get": {"tags": ["palturai-connect-api-controller"], "operationId": "getUbos", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PalturaiUbosResponse"}}}}}}}, "/company-data/company-search": {"get": {"tags": ["north-data-controller"], "operationId": "searchCompany_1", "parameters": [{"name": "query", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "domain", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "countries", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NorthDataCompany"}}}}}}}}, "/company-data/company-relation-details": {"get": {"tags": ["palturai-connect-api-controller"], "operationId": "getCompanyRelationDetails", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PalturaiPageableResponseRelationDetails"}}}}}}}, "/company-data/company-publications": {"get": {"tags": ["palturai-connect-api-controller"], "operationId": "publications", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "ids", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PalturaiPageableResponsePublication"}}}}}}}, "/company-data/company-network": {"get": {"tags": ["palturai-connect-api-controller"], "operationId": "getCompanyNetwork_1", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PalturaiPageableResponseCompanyNetwork"}}}}}}}, "/company-data/company-details": {"get": {"tags": ["palturai-connect-api-controller"], "operationId": "details", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PalturaiCompanyDetailsResponse"}}}}}}}, "/company-data/company-annual-reports": {"get": {"tags": ["palturai-connect-api-controller"], "operationId": "getAnnualReports", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "ids", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PalturaiPageableResponseAnnualReport"}}}}}}}, "/company-data/company-annual-reports/single": {"get": {"tags": ["palturai-connect-api-controller"], "operationId": "getSignleAnnualReport", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "annualReportId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AnnualReport"}}}}}}}, "/company-data/companies-on-same-address": {"get": {"tags": ["north-data-controller"], "operationId": "getCompaniesOnSameAddress", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompaniesOnSameAddressResponseDTO"}}}}}}}, "/company-data/companies-on-same-address/branch": {"get": {"tags": ["north-data-controller"], "operationId": "getCompaniesOnSameAddressForBranch", "parameters": [{"name": "branchId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompaniesOnSameAddressResponseDTO"}}}}}}}, "/company-branch/{companyId}/all": {"get": {"tags": ["company-branch-controller"], "operationId": "listAllBranchesForCompany", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyBranch"}}}}}}}}, "/internal/company/physical-delete-companies": {"delete": {"tags": ["company-internal-controller"], "operationId": "physicalDeleteCompanyAndData", "parameters": [{"name": "ids", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhysicalRemovalResult"}}}}}}}, "/information/by-key": {"delete": {"tags": ["information-controller"], "operationId": "deleteByKey", "parameters": [{"name": "companyId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "key", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/company-profile/{companyId}/network/gallery/all": {"delete": {"tags": ["company-profile-controller"], "operationId": "deleteAllCompanyNetworkGalleryNodes", "parameters": [{"name": "companyId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"MigrationResult": {"type": "object", "properties": {"hasErrors": {"type": "boolean"}, "templateFieldsdErrorList": {"type": "array", "items": {"$ref": "#/components/schemas/TemplateErrorResult"}}, "exceptionMessages": {"type": "array", "items": {"type": "string"}}}}, "TemplateErrorResult": {"type": "object", "properties": {"templateId": {"type": "string"}, "customerKey": {"type": "string"}, "fieldKey": {"type": "string"}, "groupKey": {"type": "string"}, "errorMessage": {"type": "string"}, "companyId": {"type": "string"}, "companyName": {"type": "string"}}}, "FieldDto": {"type": "object", "properties": {"key": {"type": "string"}, "fieldType": {"type": "string", "enum": ["SHORT_TEXT", "LONG_TEXT", "INTEGER", "DECIMAL", "MONETARY", "DATE", "DOCUMENT", "MONTHS", "PERCENT", "BOOLEAN", "LOCATION", "SELECT", "TABLE", "DATE_RANGE", "COMPOSITE", "MULTI_SELECT"]}, "isRequired": {"type": "boolean"}, "isPublic": {"type": "boolean"}, "isPromoted": {"type": "boolean"}, "priority": {"type": "integer", "format": "int32"}, "label": {"type": "string"}, "visibilityExpression": {"type": "string"}, "expression": {"type": "string"}, "value": {"type": "object"}, "description": {"type": "string"}, "isHidden": {"type": "boolean"}, "fieldMetaData": {"type": "object"}, "fieldOwner": {"$ref": "#/components/schemas/FieldOwner"}, "categoryId": {"type": "string"}, "portalVisibility": {"type": "string", "enum": ["VISIBLE", "REQUESTED", "NOT_SET"]}, "dependantFields": {"type": "array", "items": {"type": "string"}}}}, "FieldOwner": {"type": "object", "properties": {"customerKey": {"type": "string"}}}, "Information": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "key": {"type": "string"}, "companyId": {"type": "string"}, "userId": {"type": "string"}, "value": {"type": "object"}, "field": {"$ref": "#/components/schemas/FieldDto"}, "revisionId": {"type": "string"}, "isPublic": {"type": "boolean"}, "informationState": {"type": "string", "enum": ["ACTIVE", "DELETED"]}}}, "FieldEditRequestBody": {"type": "object", "properties": {"label": {"type": "string"}, "description": {"type": "string"}, "fieldMetaData": {"type": "object"}, "isPublic": {"type": "boolean"}, "fieldType": {"type": "string", "enum": ["SHORT_TEXT", "LONG_TEXT", "INTEGER", "DECIMAL", "MONETARY", "DATE", "DOCUMENT", "MONTHS", "PERCENT", "BOOLEAN", "LOCATION", "SELECT", "TABLE", "DATE_RANGE", "COMPOSITE", "MULTI_SELECT"]}, "categoryId": {"type": "string"}}}, "ContactPersonDto": {"type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "role": {"type": "string"}, "phoneNumber": {"type": "string"}, "email": {"type": "string"}, "companyPortalAccess": {"type": "boolean"}}}, "ContactPerson": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "role": {"type": "string"}, "phoneNumber": {"type": "string"}, "email": {"type": "string"}, "companyId": {"type": "string"}, "companyPortalAccess": {"type": "boolean"}}}, "AddressDTO": {"type": "object", "properties": {"street": {"type": "string"}, "postalCode": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "coordinates": {"$ref": "#/components/schemas/CoordinatesDTO"}}}, "CompanyDTO": {"type": "object", "properties": {"customerKey": {"type": "string"}, "isClient": {"type": "boolean"}, "companyInfo": {"$ref": "#/components/schemas/CompanyInfoDTO"}, "address": {"$ref": "#/components/schemas/AddressDTO"}, "companyState": {"type": "string", "enum": ["ACTIVE", "DELETED"]}}}, "CompanyInfoDTO": {"type": "object", "properties": {"legalName": {"type": "string"}, "legalForm": {"type": "string"}, "register": {"$ref": "#/components/schemas/RegisterDTO"}, "industryInfo": {"$ref": "#/components/schemas/IndustryInfoDTO"}, "foundingDate": {"type": "string", "format": "date"}, "taxId": {"type": "string"}, "registeredOn": {"type": "string", "format": "date"}, "extractFrom": {"type": "string", "format": "date"}, "taxNumber": {"type": "string"}, "vatId": {"type": "string"}}}, "CoordinatesDTO": {"type": "object", "properties": {"lat": {"type": "number"}, "lng": {"type": "number"}}}, "IndustryInfoDTO": {"type": "object", "properties": {"industry": {"type": "string"}, "sector": {"type": "string"}}}, "RegisterDTO": {"type": "object", "properties": {"city": {"type": "string"}, "id": {"type": "string"}, "uniqueKey": {"type": "string"}}}, "Address": {"type": "object", "properties": {"street": {"type": "string"}, "postalCode": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "coordinates": {"$ref": "#/components/schemas/Coordinates"}}}, "CADRGroup": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "string"}, "fields": {"type": "array", "items": {"type": "string"}}, "groupVisibility": {"$ref": "#/components/schemas/GroupVisibility"}, "visibility": {"type": "string", "deprecated": true, "enum": ["PUBLIC", "PRIVATE", "PARTICIPANT", "INVITEE_AND_APPLICANT"]}, "rootFolder": {"$ref": "#/components/schemas/Folder"}}}, "CADRShareObject": {"type": "object", "properties": {"customerKey": {"type": "string"}, "companyId": {"type": "string"}, "type": {"type": "string", "enum": ["EXPLICIT", "IMPLICIT"]}}}, "CADRTemplate": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "customerKey": {"type": "string"}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/FieldDto"}}, "groupsOrdered": {"type": "array", "items": {"$ref": "#/components/schemas/CADRGroup"}}, "version": {"type": "integer", "format": "int32"}, "versionDescription": {"type": "string"}, "isLatest": {"type": "boolean"}}}, "Company": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "customerKey": {"type": "string"}, "isClient": {"type": "boolean"}, "companyInfo": {"$ref": "#/components/schemas/CompanyInfo"}, "address": {"$ref": "#/components/schemas/Address"}, "companyTemplate": {"$ref": "#/components/schemas/CompanyTemplate"}, "companyState": {"type": "string", "enum": ["ACTIVE", "DELETED"]}, "companyBranches": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyBranch"}}}}, "CompanyBranch": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "branchType": {"type": "string"}, "address": {"$ref": "#/components/schemas/CompanyBranchAddress"}}}, "CompanyBranchAddress": {"type": "object", "properties": {"street": {"type": "string"}, "postalCode": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "coordinates": {"$ref": "#/components/schemas/CompanyBranchCoordinates"}}}, "CompanyBranchCoordinates": {"type": "object", "properties": {"lat": {"type": "string"}, "lng": {"type": "string"}}}, "CompanyInfo": {"type": "object", "properties": {"legalName": {"type": "string"}, "legalForm": {"type": "string"}, "register": {"$ref": "#/components/schemas/Register"}, "industryInfo": {"$ref": "#/components/schemas/IndustryInfo"}, "foundingDate": {"type": "string", "format": "date"}, "taxId": {"type": "string"}, "registeredOn": {"type": "string", "format": "date"}, "extractFrom": {"type": "string", "format": "date"}, "taxNumber": {"type": "string"}, "vatId": {"type": "string"}}}, "CompanyTemplate": {"type": "object", "properties": {"template": {"$ref": "#/components/schemas/CADRTemplate"}, "isCustom": {"type": "boolean"}, "isLocked": {"type": "boolean"}, "masterTemplateId": {"type": "string"}, "shareObjects": {"type": "array", "items": {"$ref": "#/components/schemas/CADRShareObject"}}}}, "Coordinates": {"type": "object", "properties": {"lat": {"type": "number"}, "lng": {"type": "number"}}}, "Folder": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "parentId": {"type": "string"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/Folder"}}, "fields": {"type": "array", "items": {"type": "string"}}, "ordinal": {"type": "integer", "format": "int64"}, "createdById": {"type": "string"}, "updatedById": {"type": "string"}}}, "GroupVisibility": {"type": "object", "properties": {"visibility": {"type": "string", "enum": ["PUBLIC", "PRIVATE", "PARTICIPANT", "INVITEE_AND_APPLICANT"]}, "visibleForNewParticipants": {"type": "boolean"}, "visibleForNewExplicitlyShared": {"type": "boolean"}, "currentParticipantsWithVisibility": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "explicitlySharedCustomerKeys": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}}, "IndustryInfo": {"type": "object", "properties": {"industry": {"type": "string"}, "sector": {"type": "string"}}}, "Register": {"type": "object", "properties": {"city": {"type": "string"}, "cityCode": {"type": "string"}, "id": {"type": "string"}, "uniqueKey": {"type": "string"}}}, "AddFieldToGroupRequest": {"type": "object", "properties": {"groupsOrdered": {"type": "array", "items": {"$ref": "#/components/schemas/CADRGroup"}}, "fieldDto": {"$ref": "#/components/schemas/FieldDto"}}}, "CompanyNetworkNode": {"type": "object", "properties": {"id": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "city": {"type": "string"}, "countryCode": {"type": "string"}, "deceased": {"type": "boolean"}, "dateOfBirth": {"type": "string"}, "wealthIndex": {"type": "string"}, "userRelations": {"type": "array", "items": {"$ref": "#/components/schemas/UserRelation"}}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "troubleDistanceValue": {"type": "string"}, "relationsCount": {"type": "integer", "format": "int64"}, "type": {"type": "string", "enum": ["PERSON", "COMPANY"]}, "name": {"type": "string"}, "status": {"type": "string"}, "officialRegistrationNumber": {"type": "string"}, "officialRegistrationCity": {"type": "string"}, "insolvent": {"type": "boolean"}, "address": {"type": "string"}, "originalId": {"type": "string"}}}, "Tag": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["LEAD", "CUSTOMER", "OUT_OF_SCOPE", "TROUBLE_SPOT", "SUPPLIER", "FREE_TEXT"]}, "description": {"type": "string"}, "ownerId": {"type": "string"}, "ownerName": {"type": "string"}}}, "UserRelation": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string", "enum": ["ACADEMIC_DEAN", "ACADEMIC_DEANS", "ADVISOR", "ADVISORY_BOARD_MEMBER", "APPRENTICE_SUPERVISOR", "ASSOCIATE", "ASSOCIATION", "AUDITOR", "AUTHORIZED_OFFICER", "BOARD", "BOARD_MEMBER", "BRANCH", "BUSINESS_ADDRESS", "CEO", "CFO", "CHAIRMAN", "CHAIRMAN_BOD", "CHAIRMAN_OF_THE_ADVISORY_BOARD", "CHAIRMAN_OF_THE_BOARD", "CHAIRMAN_OF_THE_SUPERVISORY_BOARD", "CHANCELLOR", "CHIEF_MASTER", "CITY_COUNCIL", "CO_TRAINER", "COACH", "COMMANDER", "COMMITTEE", "COMMITTEE_CHAIRMAN", "COMMITTEE_EXECUTIVE_DIRECTOR", "CONTACT_PERSON", "COORDINATOR", "DEAN", "DEAN_OF_RESEARCH", "DEFENDING_PLAYER", "DEPARTMENT_HEAD", "DEPUTY", "DIRECTOR", "DIRECTOR_BOD", "DIVISION_MANAGER", "DONOR", "EMPLOYEE", "EXECUTIVE_DIRECTOR", "EXECUTIVE_MEMBER", "EXECUTIVE_OFFICER", "EXECUTOR", "FEDERAL_EMPLOYEE", "FINANCIAL_DEAN", "FOUNDERFOUNDER", "GENERAL_COUNSEL", "GENERAL_PARTNER", "GENERAL_SECRETARY", "GOALKEEPER", "GOALKEEPER_COACH", "GOVERNOR", "GOVERNOR_BOG", "GUEST", "HONORARY_CHAIRMAN", "HONORARY_MEMBER", "HONORARY_PRESIDENT", "HONORARY_SENATOR", "INCORPORATOR", "INTENDANT", "INVESTMENT_MANAGER", "LEADER", "LEADER_SPECIALIST_GROUP", "LEGAL_ADDRESS", "LEGAL_ADVISOR", "LIMITED_PARTNER", "LIQUIDATOR", "MANAGER", "MANAGER_EXEC", "MANAGING_DIRECTOR", "MANAGING_PARTNER", "MASTER", "MAYOR", "MEMBER", "MERGER", "MIDFIELD_PLAYER", "MINISTER", "OFFICER", "OTHER_ADDRESS", "OWNER", "PARENT_COMPANY", "PARTNER_WITH_UNLIMITED_LIABILITY", "PARTNERSHIP", "PATRON", "POLITICAL_GROUP_CHAIRMAN", "PRESIDENT", "PRESIDENT_EXEC", "PRIME_MINISTER", "PRINCIPAL", "PROFESSORSHIP", "PROFIT_TRANSFER_AGREEMENT", "PRORECTOR", "RECTOR", "REGISTERED_AGENT", "REPRESENTATIVE", "SECRETARY", "SECRETARY_BOD", "SECRETARY_TREASURER_BOD", "SENATOR", "SHAREHOLDER", "SIGNATORY", "SPIN_OFF", "SPOKESPERSON", "SPONSOR", "SPORTS_DIRECTOR", "STATE_GUILD_DIRECTOR", "STATE_PROFESSIONAL_HUNTSMAN", "STATE_REPRESENTATIVE", "STATE_SECRETARY", "STRIKER", "SUPERVISORY_BOARD_MEMBER", "TEAM_MANAGER", "TENANT", "TOPSPONSOR", "TREASURER", "TRUSTEE", "ULTIMATE_HOLDING_COMPANY", "ULTIMATE_PARENT_COMPANY", "UNDEFINED_TYPE", "UNKNOWN_ADDRESS", "VICE_DEAN", "UBO", "KNOWS", "CONTACT"]}, "description": {"type": "string"}, "ownerUUID": {"type": "string"}, "ownerName": {"type": "string"}}}, "Edge": {"type": "object", "properties": {"id": {"type": "string"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}, "fromId": {"type": "string"}, "fromType": {"type": "string", "enum": ["PERSON", "COMPANY"]}, "toId": {"type": "string"}, "toType": {"type": "string", "enum": ["PERSON", "COMPANY"]}, "relationGroups": {"type": "array", "items": {"type": "string", "enum": ["ADDRESS", "ADVISORY", "AUDITOR", "BUSINESS", "CONTRACT", "CONTROL", "CORPORATE_HIERARCHY", "FIRST_LEVEL", "FULL_OWNER", "GOV", "KNOWS", "LTD_OWNER", "MEMBER", "MERGER_SPIN", "NGO_NPO", "PRIV_COMP", "SECOND_LEVEL", "SERVICE_PROVIDER", "UBO", "PARENT_COMPANY"]}}, "description": {"type": "string"}, "sharesPercent": {"type": "number", "format": "double"}, "originalId": {"type": "string"}, "originalFromId": {"type": "string"}, "originalToId": {"type": "string"}, "levelOfDepth": {"type": "integer", "format": "int32"}, "sourceDocument": {"type": "string"}, "sourceDocumentTime": {"type": "string", "format": "date-time"}}}, "CompanyNetworkNodeChangeDto": {"type": "object", "properties": {"oldNode": {"$ref": "#/components/schemas/CompanyNetworkNode"}, "newNode": {"$ref": "#/components/schemas/CompanyNetworkNode"}}}, "CompanyNetworkEdgeChangeDto": {"type": "object", "properties": {"oldEdge": {"$ref": "#/components/schemas/Edge"}, "newEdge": {"$ref": "#/components/schemas/Edge"}}}, "CompanyBranchAddressDTO": {"type": "object", "properties": {"street": {"type": "string"}, "postalCode": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "coordinates": {"$ref": "#/components/schemas/CompanyBranchCoordinatesDTO"}}}, "CompanyBranchCoordinatesDTO": {"type": "object", "properties": {"lat": {"type": "number"}, "lng": {"type": "number"}}}, "CompanyBranchDTO": {"type": "object", "properties": {"name": {"type": "string"}, "branchType": {"type": "string"}, "address": {"$ref": "#/components/schemas/CompanyBranchAddressDTO"}}}, "CADRGroupDto": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": "string"}, "fields": {"type": "array", "items": {"type": "string"}}, "groupVisibility": {"$ref": "#/components/schemas/GroupVisibilityDto"}, "visibility": {"type": "string", "deprecated": true, "enum": ["PUBLIC", "PRIVATE", "PARTICIPANT", "INVITEE_AND_APPLICANT"]}}}, "CADRTemplateDto": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "customerKey": {"type": "string"}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/FieldDto"}}, "groupsOrdered": {"type": "array", "items": {"$ref": "#/components/schemas/CADRGroupDto"}}, "version": {"type": "integer", "format": "int32"}, "versionDescription": {"type": "string"}, "isLatest": {"type": "boolean"}}}, "GroupVisibilityDto": {"type": "object", "properties": {"visibility": {"type": "string", "enum": ["PUBLIC", "PRIVATE", "PARTICIPANT", "INVITEE_AND_APPLICANT"]}, "visibleForNewParticipants": {"type": "boolean"}, "visibleForNewExplicitlyShared": {"type": "boolean"}}}, "CompanyData": {"type": "object", "properties": {"company": {"$ref": "#/components/schemas/Company"}, "companyContactPersons": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "information": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyInformation"}}, "cadrTemplate": {"$ref": "#/components/schemas/CADRTemplate"}, "documents": {"type": "array", "items": {"$ref": "#/components/schemas/Document"}}}}, "CompanyInformation": {"required": ["companyId", "key", "value"], "type": "object", "properties": {"id": {"type": "string"}, "key": {"type": "string"}, "companyId": {"type": "string"}, "userId": {"type": "string"}, "value": {"type": "object"}, "field": {"$ref": "#/components/schemas/FieldDto"}, "isPublic": {"type": "boolean"}, "informationState": {"type": "string", "enum": ["ACTIVE", "DELETED"]}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "ContentReference": {"type": "object", "properties": {"contentReferenceId": {"type": "string"}, "bucketName": {"type": "string"}, "documentKey": {"type": "string"}, "fileName": {"type": "string"}, "contentType": {"type": "string"}, "contentSize": {"type": "integer", "format": "int64"}, "isIndexed": {"type": "boolean"}, "isNotIndexable": {"type": "boolean"}, "indexRetryCount": {"type": "integer", "format": "int32"}}}, "Document": {"type": "object", "properties": {"id": {"type": "string"}, "createdOn": {"type": "string"}, "realmKey": {"type": "string"}, "ownerReference": {"$ref": "#/components/schemas/OwnerReference"}, "businessCaseId": {"type": "string"}, "companyId": {"type": "string"}, "userId": {"type": "string"}, "userCustomerKey": {"type": "string"}, "state": {"type": "string", "enum": ["DELETED", "TEMPORARY", "CONTENT_UPLOAD_ONGOING", "CONTENT_UPLOAD_FAILED", "FINAL"]}, "contentReference": {"$ref": "#/components/schemas/ContentReference"}, "contentBase64": {"type": "string"}, "digest": {"type": "string"}, "hasError": {"type": "boolean"}, "isContainedInInbox": {"type": "boolean"}, "isContainedInBusinessCase": {"type": "boolean"}, "isWarningIgnored": {"type": "boolean"}}}, "OwnerReference": {"type": "object", "properties": {"id": {"type": "string"}, "chatId": {"type": "string"}, "uploaderCustomerKey": {"type": "string"}, "businessCaseId": {"type": "string"}, "companyId": {"type": "string"}, "userId": {"type": "string"}, "documentType": {"type": "string", "enum": ["COMMUNICATION", "CONTRACT", "BUSINESS_CASE", "INBOX", "BASE", "COMPANY", "EMAIL_ATTACHMENT", "PROFILE_PICTURE", "ORGANIZATION_LOGO"]}}}, "User": {"type": "object", "properties": {"id": {"type": "string"}, "userType": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "username": {"type": "string"}, "customerKey": {"type": "string"}, "enabled": {"type": "boolean"}, "email": {"type": "string"}, "attributes": {"$ref": "#/components/schemas/UserAttributes"}, "initialPassword": {"type": "boolean"}, "userState": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "userRoles": {"type": "array", "items": {"$ref": "#/components/schemas/UserRole"}}, "mfaEnabled": {"type": "boolean"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "accountNonExpired": {"type": "boolean"}, "accountNonLocked": {"type": "boolean"}, "credentialsNonExpired": {"type": "boolean"}}}, "UserAttributes": {"type": "object", "properties": {"salutation": {"type": "string"}, "academicTitle": {"type": "string"}, "department": {"type": "string"}, "position": {"type": "string"}, "mobileNumber": {"type": "string"}, "landlineNumber": {"type": "string"}, "createdByUserId": {"type": "string"}, "acceptedTerms": {"type": "boolean"}, "companyUserId": {"type": "string"}, "companyName": {"type": "string"}, "locale": {"type": "string"}, "profilePictureId": {"type": "string"}, "lastUserSessionLocale": {"type": "string"}}}, "UserRole": {"type": "object", "properties": {"name": {"type": "string"}}}, "AttachCompanyTemplateRequest": {"type": "object", "properties": {"parameters": {"type": "array", "items": {"$ref": "#/components/schemas/Param"}}}}, "Param": {"type": "object", "properties": {"name": {"type": "string"}, "val": {"type": "object"}}}, "AttachCompanyTemplateResponse": {"type": "object", "properties": {"company": {"$ref": "#/components/schemas/Company"}, "hasError": {"type": "boolean"}, "evaluationFailures": {"type": "array", "items": {"$ref": "#/components/schemas/ValidationFailureReason"}}}}, "ValidationFailureReason": {"type": "object", "properties": {"failedField": {"type": "string"}, "message": {"type": "string"}, "failedDependencies": {"type": "array", "items": {"type": "string"}}}}, "CompanyNetworkGalleryNode": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "companyId": {"type": "string"}, "userId": {"type": "string"}, "node": {"$ref": "#/components/schemas/CompanyNetworkNode"}}}, "CompanyGraphStateDto": {"type": "object", "properties": {"companyId": {"type": "string"}, "companyGraphState": {"type": "object"}}}, "SearchCompanyRequest": {"type": "object", "properties": {"name": {"type": "string"}, "officialRegistrationNumber": {"type": "string"}, "countryCode": {"type": "string"}, "state": {"type": "string"}, "zipCode": {"type": "string"}, "city": {"type": "string"}, "streetHouseNumber": {"type": "string"}, "registrationCourtCity": {"type": "string"}, "registrationCourtZip": {"type": "string"}, "insolvencyCourtFileNumber": {"type": "string"}, "insolvencyCourtCity": {"type": "string"}, "insolvencyCourtZip": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "IN_LIQUIDATION"]}, "enrichWithTags": {"type": "boolean"}, "page": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}}}, "CompanyEntry": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "countryCode": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "IN_LIQUIDATION"]}, "officialRegistrationNumber": {"type": "string"}, "registrationCourtCity": {"type": "string"}, "insolvent": {"type": "boolean"}, "userRelations": {"type": "array", "items": {"$ref": "#/components/schemas/UserRelation"}}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}}}, "JsonNode": {"type": "object"}, "Pageable": {"type": "object", "properties": {"sort": {"$ref": "#/components/schemas/JsonNode"}, "paged": {"type": "boolean"}, "unpaged": {"type": "boolean"}, "pageNumber": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}}}, "PalturaiPageableResponseCompanyEntry": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/JsonNode"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/Pageable"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyEntry"}}, "empty": {"type": "boolean"}}}, "Summary": {"type": "object", "properties": {"total": {"type": "integer", "format": "int64"}, "success": {"type": "integer", "format": "int32"}, "skipped": {"type": "integer", "format": "int32"}, "failed": {"type": "array", "items": {"type": "string"}}}}, "CompanyNetwork": {"type": "object", "properties": {"edges": {"type": "array", "items": {"$ref": "#/components/schemas/Edge"}}, "nodes": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyNetworkNode"}}}}, "CurrentAddress": {"type": "object", "properties": {"id": {"type": "string"}, "city": {"type": "string"}, "countryCode": {"type": "string"}, "zipCode": {"type": "string"}, "streetHouseNumber": {"type": "string"}, "street": {"type": "string"}, "houseNumber": {"type": "string"}, "federalState": {"type": "string"}, "geo": {"type": "string"}}}, "Employee": {"type": "object", "properties": {"id": {"type": "string"}, "year": {"type": "integer", "format": "int64"}, "numberOfEmployees": {"type": "integer", "format": "int64"}, "employeeClass": {"type": "string", "enum": ["EC_0_5", "EC_6_50", "EC_6_24", "EC_25_50", "EC_51_500", "EC_501_5000", "EC_5001_50000", "EC_GR_50000"]}}}, "ExternalId": {"type": "object", "properties": {"type": {"type": "string"}, "id": {"type": "string"}}}, "PalturaiCompanyDetailsResponse": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "IN_LIQUIDATION"]}, "capital": {"type": "number", "format": "double"}, "capitalCurrency": {"type": "string"}, "officialRegistrationCountry": {"type": "string"}, "officialRegisterName": {"type": "string"}, "officialRegistrationNumber": {"type": "string"}, "registryCity": {"type": "string"}, "registryZip": {"type": "string"}, "taxId": {"type": "string"}, "legalEntityIdentifier": {"type": "string"}, "foundingDate": {"type": "string"}, "deletionDate": {"type": "string"}, "liquidationStartDate": {"type": "string"}, "liquidationEndDate": {"type": "string"}, "purpose": {"type": "string"}, "lastPublicationDate": {"type": "string"}, "riskInfo": {"type": "string", "enum": ["F1_R1", "F1_R2", "F1_R3", "F1_R4", "F1_R5", "F2_R1", "F3_R1", "F3_R2", "F3_R3", "F3_R4", "F3_R5", "F3_R6", "F3_R7", "F3_R8", "F3_R9"]}, "riskInfoDate": {"type": "string"}, "insolvent": {"type": "boolean"}, "industryCodes": {"type": "array", "items": {"type": "string"}}, "tradeRegisterNumbers": {"type": "array", "items": {"type": "string"}}, "phoneNumbers": {"type": "array", "items": {"type": "string"}}, "webAddresses": {"type": "array", "items": {"type": "string"}}, "currentAddress": {"$ref": "#/components/schemas/CurrentAddress"}, "riskInfoHistory": {"$ref": "#/components/schemas/JsonNode"}, "externalIds": {"type": "array", "items": {"$ref": "#/components/schemas/ExternalId"}}, "turnovers": {"type": "array", "items": {"$ref": "#/components/schemas/Turnover"}}, "employees": {"type": "array", "items": {"$ref": "#/components/schemas/Employee"}}, "scores": {"type": "array", "items": {"$ref": "#/components/schemas/Score"}}}}, "Score": {"type": "object", "properties": {"type": {"type": "string", "enum": ["COMPANIES_2_SELL", "OTHER", "FRAUD_INDEX", "CAPRISE_INDEX"]}, "value": {"type": "string"}, "text": {"type": "string"}, "comment": {"type": "string"}, "calculationDate": {"type": "string"}}}, "Turnover": {"type": "object", "properties": {"id": {"type": "string"}, "year": {"type": "integer", "format": "int64"}, "value": {"type": "number", "format": "double"}, "currency": {"type": "string"}, "turnoverClass": {"type": "string", "enum": ["TC_0_99", "TC_100_499", "TC_500_999", "TC_1000_9999", "TC_1000_2499", "TC_2500_9999", "TC_10000_99999", "TC_10000_24999", "TC_25000_99999", "TC_100000_999999", "TC_GE_1000000"]}}}, "NorthDataAddress": {"type": "object", "properties": {"street": {"type": "string"}, "postalCode": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "lat": {"type": "number", "format": "double"}, "lng": {"type": "number", "format": "double"}, "formattedValue": {"type": "string"}}}, "NorthDataCompany": {"type": "object", "properties": {"company": {"$ref": "#/components/schemas/NorthDataCompanyInfo"}, "matchedName": {"type": "string"}}}, "NorthDataCompanyInfo": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"$ref": "#/components/schemas/NorthDataName"}, "address": {"$ref": "#/components/schemas/NorthDataAddress"}, "register": {"$ref": "#/components/schemas/NorthDataRegister"}, "elfCode": {"type": "string"}, "subject": {"type": "string"}, "segmentCodes": {"type": "object"}, "northDataUrl": {"type": "string"}, "status": {"type": "string"}, "lei": {"type": "string"}, "terminated": {"type": "boolean"}, "history": {"$ref": "#/components/schemas/JsonNode"}, "capital": {"$ref": "#/components/schemas/JsonNode"}, "financials": {"$ref": "#/components/schemas/JsonNode"}, "mktgTechIndicators": {"$ref": "#/components/schemas/JsonNode"}, "events": {"$ref": "#/components/schemas/JsonNode"}, "extras": {"$ref": "#/components/schemas/JsonNode"}, "sheets": {"$ref": "#/components/schemas/JsonNode"}, "relatedCompanies": {"$ref": "#/components/schemas/JsonNode"}, "relatedPersons": {"$ref": "#/components/schemas/JsonNode"}, "blocked": {"type": "boolean"}}}, "NorthDataName": {"type": "object", "properties": {"name": {"type": "string"}, "legalForm": {"type": "string"}}}, "NorthDataRegister": {"type": "object", "properties": {"country": {"type": "string"}, "city": {"type": "string"}, "id": {"type": "string"}, "uniqueKey": {"type": "string"}}}, "InformationRevision": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "companyId": {"type": "string"}, "informationIdentifierKey": {"type": "string"}, "userId": {"type": "string"}, "information": {"$ref": "#/components/schemas/Information"}, "informationRevisionState": {"type": "string", "enum": ["ACTIVE", "DELETED"]}, "masterRevisionId": {"type": "string"}}}, "ContactPersonCompanyDto": {"type": "object", "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "companyLegalName": {"type": "string"}}}, "CompanyProfile": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "companyId": {"type": "string"}, "registerNumber": {"type": "string"}, "registerCity": {"type": "string"}, "companyProfileGeneralInformation": {"$ref": "#/components/schemas/CompanyProfileGeneralInformation"}, "companyProfileRegistration": {"$ref": "#/components/schemas/CompanyProfileRegistration"}, "companyProfileAdditionalInformation": {"$ref": "#/components/schemas/CompanyProfileAdditionalInformation"}, "companyProfileContactDetails": {"$ref": "#/components/schemas/CompanyProfileContactDetails"}, "companyProfileLiquidation": {"$ref": "#/components/schemas/CompanyProfileLiquidation"}}}, "CompanyProfileAdditionalInformation": {"type": "object", "properties": {"capital": {"type": "number", "format": "double"}, "capitalCurrency": {"type": "string"}, "foundingDate": {"type": "string"}, "corporatePurpose": {"type": "string"}, "turnovers": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyProfileTurnover"}}, "scores": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyProfileScore"}}, "employees": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyProfileEmployee"}}}}, "CompanyProfileAddress": {"type": "object", "properties": {"street": {"type": "string"}, "postalCode": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "country": {"type": "string"}, "lat": {"type": "number", "format": "double"}, "lng": {"type": "number", "format": "double"}, "formattedValue": {"type": "string"}}}, "CompanyProfileContactDetails": {"type": "object", "properties": {"phoneNumbers": {"type": "string"}, "webAddresses": {"type": "string"}, "lastPublicationDate": {"type": "string"}}}, "CompanyProfileEmployee": {"type": "object", "properties": {"externalId": {"type": "string"}, "year": {"type": "integer", "format": "int64"}, "numberOfEmployees": {"type": "integer", "format": "int64"}, "employeeClass": {"type": "string", "enum": ["EC_0_5", "EC_6_50", "EC_6_24", "EC_25_50", "EC_51_500", "EC_501_5000", "EC_5001_50000", "EC_GR_50000"]}}}, "CompanyProfileGeneralInformation": {"type": "object", "properties": {"companyName": {"type": "string"}, "legalForm": {"type": "string"}, "registerCity": {"type": "string"}, "registerNumber": {"type": "string"}, "address": {"$ref": "#/components/schemas/CompanyProfileAddress"}}}, "CompanyProfileLiquidation": {"type": "object", "properties": {"liquidationStartDate": {"type": "string"}, "liquidationStatus": {"type": "string"}, "riskInfoDate": {"type": "string"}, "riskInfoHistory": {"type": "string"}, "insolvent": {"type": "boolean"}, "liquidationEndDate": {"type": "string"}}}, "CompanyProfileRegistration": {"type": "object", "properties": {"status": {"type": "string"}, "additionalRegistrationNumber": {"type": "string"}, "taxId": {"type": "string"}, "legalEntityIdentifier": {"type": "string"}, "deletionDate": {"type": "string"}}}, "CompanyProfileScore": {"type": "object", "properties": {"type": {"type": "string", "enum": ["COMPANIES_2_SELL", "OTHER", "FRAUD_INDEX", "CAPRISE_INDEX"]}, "value": {"type": "string"}, "text": {"type": "string"}, "comment": {"type": "string"}, "calculationDate": {"type": "string"}}}, "CompanyProfileTurnover": {"type": "object", "properties": {"externalId": {"type": "string"}, "year": {"type": "integer", "format": "int64"}, "value": {"type": "number", "format": "double"}, "currency": {"type": "string"}, "turnoverClass": {"type": "string", "enum": ["TC_0_99", "TC_100_499", "TC_500_999", "TC_1000_9999", "TC_1000_2499", "TC_2500_9999", "TC_10000_99999", "TC_10000_24999", "TC_25000_99999", "TC_100000_999999", "TC_GE_1000000"]}}}, "CompanyUbosDto": {"type": "object", "properties": {"companyId": {"type": "string"}, "generatedCompanyId": {"type": "string"}, "edges": {"type": "array", "items": {"$ref": "#/components/schemas/Edge"}}, "nodes": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyNetworkNode"}}}}, "SharesPercentageData": {"type": "object", "properties": {"nodeId": {"type": "string"}, "sharesPercent": {"type": "number", "format": "double"}, "pathEdgeIds": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}}}}, "CompanyNetworkDto": {"type": "object", "properties": {"companyId": {"type": "string"}, "generatedCompanyId": {"type": "string"}, "originalCompanyId": {"type": "string"}, "edges": {"type": "array", "items": {"$ref": "#/components/schemas/Edge"}}, "nodes": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyNetworkNode"}}, "source": {"type": "string", "enum": ["PALTURAI", "FINCLOUD", "USER"]}}}, "CompanyNetworkChange": {"type": "object", "properties": {"id": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}, "companyId": {"type": "string"}, "userId": {"type": "string"}, "changeType": {"type": "string", "enum": ["ADD_NODE", "REMOVE_NODE", "MODIFY_NODE", "ADD_EDGE", "REMOVE_EDGE", "MODIFY_EDGE"]}, "changeValue": {"$ref": "#/components/schemas/CompanyNetworkChangeValue"}, "edgeMetadata": {"$ref": "#/components/schemas/EdgeChangeMetadata"}, "changeValueHash": {"type": "string"}}}, "CompanyNetworkChangeValue": {"type": "object", "properties": {"oldValue": {"type": "object"}, "newValue": {"type": "object"}}}, "CompanyNetworkUpdateDto": {"type": "object", "properties": {"canReject": {"type": "boolean"}, "changes": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyNetworkChange"}}}}, "EdgeChangeMetadata": {"type": "object", "properties": {"nodeFrom": {"$ref": "#/components/schemas/CompanyNetworkNode"}, "nodeTo": {"$ref": "#/components/schemas/CompanyNetworkNode"}}}, "CompanyNetworkRevisionChangeDto": {"type": "object", "properties": {"companyNetworkChanges": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyNetworkChange"}}, "revisionId": {"type": "string"}}}, "CompanyNetworkRevisionDto": {"type": "object", "properties": {"id": {"type": "string"}, "companyId": {"type": "string"}, "userId": {"type": "string"}, "generatedCompanyId": {"type": "string"}, "originalCompanyId": {"type": "string"}, "source": {"type": "string", "enum": ["PALTURAI", "FINCLOUD", "USER"]}, "state": {"type": "string", "enum": ["APPLIED", "ARCHIVED"]}, "nodes": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyNetworkNode"}}, "edges": {"type": "array", "items": {"$ref": "#/components/schemas/Edge"}}, "creationDate": {"type": "string", "format": "date-time"}, "lastModifiedDate": {"type": "string", "format": "date-time"}}}, "CompanyNetworkRevisionWithChangeDto": {"type": "object", "properties": {"companyNetworkRevisionDto": {"$ref": "#/components/schemas/CompanyNetworkRevisionDto"}, "companyNetworkRevisionChangeDto": {"$ref": "#/components/schemas/CompanyNetworkRevisionChangeDto"}}}, "PageCompanyNetworkRevisionWithChangeDto": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "numberOfElements": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyNetworkRevisionWithChangeDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "PageableObject": {"type": "object", "properties": {"unpaged": {"type": "boolean"}, "pageNumber": {"type": "integer", "format": "int32"}, "paged": {"type": "boolean"}, "pageSize": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int64"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}}}, "SortObject": {"type": "object", "properties": {"direction": {"type": "string"}, "nullHandling": {"type": "string"}, "ascending": {"type": "boolean"}, "property": {"type": "string"}, "ignoreCase": {"type": "boolean"}}}, "StreamingResponseBody": {"type": "object"}, "CompanyNetworkDocumentDto": {"type": "object", "properties": {"companyId": {"type": "string"}, "companyName": {"type": "string"}, "companyRegisterId": {"type": "string"}, "companyRegisterCity": {"type": "string"}, "address": {"type": "string"}, "levelOfDepth": {"type": "integer", "format": "int32"}, "sourceDocument": {"type": "string"}, "sourceDocumentTime": {"type": "string", "format": "date-time"}}}, "PageCompanyNetworkDocumentDto": {"type": "object", "properties": {"totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "pageable": {"$ref": "#/components/schemas/PageableObject"}, "numberOfElements": {"type": "integer", "format": "int32"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyNetworkDocumentDto"}}, "number": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"$ref": "#/components/schemas/SortObject"}}, "first": {"type": "boolean"}, "last": {"type": "boolean"}, "empty": {"type": "boolean"}}}, "PalturaiUbosResponse": {"type": "object", "properties": {"nodes": {"type": "array", "items": {"$ref": "#/components/schemas/CompanyNetworkNode"}}, "edges": {"type": "array", "items": {"$ref": "#/components/schemas/Edge"}}, "fictional": {"type": "boolean"}}}, "PalturaiPageableResponseRelationDetails": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/JsonNode"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/Pageable"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/RelationDetails"}}, "empty": {"type": "boolean"}}}, "RelationDetails": {"type": "object", "properties": {"id": {"type": "string"}, "fromId": {"type": "string"}, "toId": {"type": "string"}, "fromType": {"type": "string", "enum": ["PERSON", "COMPANY"]}, "toType": {"type": "string", "enum": ["PERSON", "COMPANY"]}, "bidirected": {"type": "boolean"}, "description": {"type": "string"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}, "sharesPercentage": {"type": "number", "format": "double"}, "knowsProbability": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH"]}, "relationGroups": {"type": "array", "items": {"type": "string", "enum": ["ADDRESS", "ADVISORY", "AUDITOR", "BUSINESS", "CONTRACT", "CONTROL", "CORPORATE_HIERARCHY", "FIRST_LEVEL", "FULL_OWNER", "GOV", "KNOWS", "LTD_OWNER", "MEMBER", "MERGER_SPIN", "NGO_NPO", "PRIV_COMP", "SECOND_LEVEL", "SERVICE_PROVIDER", "UBO", "PARENT_COMPANY"]}}}}, "PalturaiPageableResponsePublication": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/JsonNode"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/Pageable"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/Publication"}}, "empty": {"type": "boolean"}}}, "Publication": {"type": "object", "properties": {"id": {"type": "string"}, "publicationType": {"type": "string", "enum": ["BALANCESHEET_PUBLICATION", "TR_MESSAGE_PUBLICATION", "OTHER_PUBLICATION"]}, "publicationDate": {"type": "string"}, "content": {"type": "string"}}}, "PalturaiPageableResponseCompanyNetwork": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/JsonNode"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/Pageable"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"type": "object"}}, "empty": {"type": "boolean"}, "network": {"$ref": "#/components/schemas/CompanyNetwork"}}}, "AnnualReport": {"type": "object", "properties": {"id": {"type": "string"}, "publicationId": {"type": "string"}, "year": {"type": "integer", "format": "int32"}, "startDate": {"type": "string"}, "endDate": {"type": "string"}, "positions": {"type": "array", "items": {"$ref": "#/components/schemas/Position"}}}}, "PalturaiPageableResponseAnnualReport": {"type": "object", "properties": {"totalPages": {"type": "integer", "format": "int32"}, "totalElements": {"type": "integer", "format": "int32"}, "first": {"type": "boolean"}, "sort": {"$ref": "#/components/schemas/JsonNode"}, "number": {"type": "integer", "format": "int32"}, "numberOfElements": {"type": "integer", "format": "int32"}, "last": {"type": "boolean"}, "pageable": {"$ref": "#/components/schemas/Pageable"}, "size": {"type": "integer", "format": "int32"}, "content": {"type": "array", "items": {"$ref": "#/components/schemas/AnnualReport"}}, "empty": {"type": "boolean"}}}, "Position": {"type": "object", "properties": {"xbrlClass": {"type": "string"}, "positionValue": {"type": "number", "format": "double"}, "currency": {"type": "string"}, "previousPeriodPositionValue": {"type": "number", "format": "double"}, "positionText": {"type": "string"}}}, "CompaniesOnSameAddressResponseDTO": {"type": "object", "properties": {"totalCompaniesOnGivenAddress": {"type": "integer", "format": "int64"}, "searchedAddress": {"type": "string"}, "companiesOnSearchedAddress": {"type": "array", "items": {"$ref": "#/components/schemas/SearchCompanyDTO"}}}}, "SearchCompanyDTO": {"type": "object", "properties": {"name": {"type": "string"}, "registerCity": {"type": "string"}, "registerNumber": {"type": "string"}}}, "SharedCADRDTO": {"type": "object", "properties": {"companyTemplate": {"$ref": "#/components/schemas/CompanyTemplate"}, "informations": {"type": "array", "items": {"$ref": "#/components/schemas/Information"}}, "baseCompanyId": {"type": "string"}, "baseCompanyLegalName": {"type": "string"}, "baseCompanyCustomerKey": {"type": "string"}}}, "PhysicalRemovalFailure": {"type": "object", "properties": {"companyId": {"type": "string"}, "reason": {"type": "string"}}}, "PhysicalRemovalResult": {"type": "object", "properties": {"successIds": {"type": "array", "items": {"type": "string"}}, "failedRemovals": {"type": "array", "items": {"$ref": "#/components/schemas/PhysicalRemovalFailure"}}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "name": "<PERSON><PERSON><PERSON>", "scheme": "bearer", "bearerFormat": "JWT"}}}}