import { Customer } from '@fincloud/swagger-generator/authorization-server';
import { Chat } from '@fincloud/swagger-generator/communication';
import { Company } from '@fincloud/swagger-generator/company';
import { BusinessCaseGroup } from '@fincloud/swagger-generator/exchange';
import {
  CaseFieldAccess,
  CaseFieldInputRequest,
} from '@fincloud/swagger-generator/portal';
import { FinErrorSpace } from '@fincloud/ui/types';
import { Dictionary } from './dictionary';
import { FolderStructureListViewDisplayOptions } from './folder-structure';
import { TemplateFieldActionsConfig } from './template-field-actions-config';
import { UploadFilesBreakdown } from './upload-files-breakdown';

export interface DataRoomTemplateFieldData {
  fieldActionsConfig?: TemplateFieldActionsConfig;
  dataRoomOwnerName?: string;
  dataRoomOwnerId?: string;
  isCADR?: boolean;
  company?: Company;
  hasDataRoomWriteAccess?: boolean;
  isSharedDataRoom?: boolean;
  existingTopicChats?: Dictionary<Chat>;
  existingArchivedTopicChats?: Dictionary<Chat>;
  caseFieldsParticipantAccessDict?: Dictionary<CaseFieldAccess>;
  isEmployeeOfLeadCustomer?: boolean;
  caseFieldsParticipantInputsReqsDict?: Dictionary<CaseFieldInputRequest>;
  userCustomerKey?: string;
  caseLeaderCustomerKey?: string;
  isCommissionFieldDisabled?: boolean;
  canSeeTopicChats?: boolean;
  isVisibilityForAllParticipantsOn?: boolean;
  isCustomerRealEstate?: boolean;
  isPartOfBusinessCase?: boolean;
  businessCaseParticipants?: Customer[];
  mirroredFieldKeys?: string[];
  mirroredCalculatableFieldKeys?: string[];
  globalRequiredFieldKeys?: string[];
  selectedAIAssistantSourceId?: string;
  dynamicErrorSpace?: FinErrorSpace;
  countOfOccupiedColumns?: number;
  uploadingFilesBreakdownList?: UploadFilesBreakdown[];
  groupsWithoutFolderStructure?: Omit<BusinessCaseGroup, 'rootFolder'>[];
  folderStructureListViewDisplayOptions?: FolderStructureListViewDisplayOptions;
}
