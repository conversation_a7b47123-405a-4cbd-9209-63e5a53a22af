import {
  Application,
  BulkOperationResultInvitation,
  Criteria,
  Invitation,
} from '@fincloud/swagger-generator/application';
import {
  Customer,
  User,
} from '@fincloud/swagger-generator/authorization-server';
import { Chat } from '@fincloud/swagger-generator/communication';
import {
  ActivityLogDto,
  ExchangeBusinessCase,
} from '@fincloud/swagger-generator/exchange';
import {
  CaseFieldAccess,
  CaseFieldInputRequest,
  UserCase,
} from '@fincloud/swagger-generator/portal';

import {
  CurrentUsersInBusinessCaseResponse,
  ParticipantCasePermissionSetEntity,
} from '@fincloud/swagger-generator/business-case-manager';
import { NorthDataCompany } from '@fincloud/swagger-generator/company';
import {
  DocumentCategoryDto,
  DocumentEntity,
} from '@fincloud/swagger-generator/document';
import {
  FinPartnerOverviewResponse,
  FinStructureField,
} from '@fincloud/swagger-generator/financing-details';
import { DataRoomDraggedItemType } from '@fincloud/types/enums';
import { DataRoomHighlight } from './data-room-highlight';
import { Dictionary } from './dictionary';
import { FacilityViewModel } from './facility-view-model';
import { NAVIGATION_ITEM } from './navigation-item';
import { UploadFilesBreakdown } from './upload-files-breakdown';
import { UserToken } from './user-token';

export interface BusinessCaseDashboardState {
  businessCase: ExchangeBusinessCase;
  isBusinessCaseLoading: boolean;
  businessCaseLoadingError: boolean;
  criteria: Criteria[];
  applications: Application[];
  applicationsLoaded: boolean;
  invitations: Invitation[];
  acceptedInvitations: BulkOperationResultInvitation;
  invitationsLoaded: boolean;
  isRealEstateTabOpened: boolean;
  selectedApplication: Application;
  selectedInvitation: Invitation;
  dataRoomDraggerItemType: DataRoomDraggedItemType;
  userToken: UserToken;
  customerNamesByKey: Dictionary<Customer>;
  commonFields: FinStructureField[];
  userNamesById: Dictionary<User>;
  editMode: boolean;
  activityLogs: ActivityLogDto[];
  collborationsInvitationsModalLoading: boolean;
  activeNavigationItem: NAVIGATION_ITEM;
  selectedOverviewGeneral: boolean;
  selectedOverviewKpi: boolean;
  selectedCollaborationMyPartners: boolean;
  selectedCollaborationInvitationsApplications: boolean;
  selectedCollaborationInvitations: boolean;
  selectedCollaborationApplications: boolean;
  selectedCollaborationTab: string;
  selectedTab: string;
  isCompanyDataRoomTabSelected: boolean;
  selectedDataRoomCase: boolean;
  selectedDataRoomCompany: boolean;
  selectedFinancingDetailsMyParticipation: boolean;
  selectedFinancingDetailsFinancingStructure: boolean;
  selectedFinancingDetailsSharedFinancingStructure: boolean;
  documentFieldCategories: DocumentCategoryDto[];
  existingChats: Chat[];
  mutedChatsIds: string[];
  globalRequiredFieldKeys: string[];
  lastVisitedUrl: string;
  facilities: FacilityViewModel[];
  caseAssignedUsersById: Dictionary<UserCase>;
  caseFieldsAccess: CaseFieldAccess[];
  caseFieldsAccessLoaded: boolean;
  caseFieldsInputsRequests: CaseFieldInputRequest[];
  caseFieldsInputsRequestsLoaded: boolean;
  masterSubChatInfo: { sendAlsoToId: string; chatId: string };
  isRevisionApplied: boolean;
  hasCustomerDataExportToken: boolean;
  hasDataExportBeenAllowed: boolean;
  areConditionForDataExportMet: boolean;
  allParticipantsCaseVisibility: boolean;
  userCorrespondingCompanyId: string;
  applicationLoaded: Application;
  customerContext: ParticipantCasePermissionSetEntity;
  participantsPermissions: ParticipantCasePermissionSetEntity[];
  businessCaseStatuses?: string[]; // this should be enum, fix later, also discuss adding it to the initial state
  reasonsForClosingBusinessCase?: string[];
  currentUsersFromMyOrganization: CurrentUsersInBusinessCaseResponse;
  chatHistoryRange: {
    newestChatMessageDateTime: string;
    oldestChatMessageDateTime: string;
  };
  chatExport: {
    selectedStartDate: string;
    selectedEndDate: string;
    hasAnyMessagesForSelectedPeriod: boolean;
    locale: string;
    timeZone: string;
  };
  selectedChat: Chat;
  chatHistoryHasError: boolean;
  documentsFiles: DocumentEntity[];
  uploadedFilesInChat: DocumentEntity[];
  isChatFileUploading: boolean;
  selectedChatId: string;
  failedChatUpload: boolean;
  failedUploadChatFile: File;
  mirroredFieldKeys: string[];
  calculatableMirroredFieldKeys: string[];
  singleCompanyNorthDataCompanies: NorthDataCompany[];
  multiCompanyNorthDataCompanies: NorthDataCompany[];
  fetchingNorthDataCompanies: boolean;
  highlighted: DataRoomHighlight;
  isCaseHeaderInView: boolean;
  extendedGroups: { [id: string]: boolean };
  highlightedTopGroupKey: string;
  uploadFilesBreakdownList: UploadFilesBreakdown[];
  financingPartners: FinPartnerOverviewResponse[];
}
