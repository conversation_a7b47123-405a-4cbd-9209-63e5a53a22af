import { BusinessCaseBasicInfoDto } from '@fincloud/swagger-generator/exchange';
import { NotificationPreferences } from '@fincloud/swagger-generator/platform-notification';
import {
  NotificationSystemTab,
  NotificationSystemView,
} from '@fincloud/types/enums';
import { PageNotificationWithEntities } from '../page-notification-with-entity';

export interface NotificationSystemState {
  isNotificationPanelOpen: boolean;
  currentView: NotificationSystemView;
  currentTab: NotificationSystemTab;

  settings: {
    isLoading: boolean;
    notificationPreferences: NotificationPreferences;
  };

  notifications: {
    isLoading: boolean;
    filters: {
      size: number;
    };
    notifications: PageNotificationWithEntities;
  };

  // TODO: refactor this
  unseenNotifications: number;
  businessCaseNames: BusinessCaseBasicInfoDto[];

  // old version

  // showAllEnabled: boolean;

  // incomingNotifications: Notification[];
  // isWebSocketConnected: boolean;
  // isWebSocketConnecting: boolean;

  // failedUpdatedSetting: { type: NotificationType; isEnabled: boolean };

  // businessCaseNames: BusinessCaseBasicInfoDto[];

  // unreadNotifications: Notification[];
}
