import { Folder } from '@fincloud/swagger-generator/business-case-manager';
import { FolderListViewSort, FolderWithBreadcrumbs } from '../folder-structure';

export interface FolderStructureState {
  currentFolderPerGroup: {
    [groupKey: string]: FolderWithBreadcrumbs;
  };
  rootFolderPerGroup: {
    [groupKey: string]: Folder;
  };
  sortingPerGroup: {
    [groupKey: string]: FolderListViewSort;
  };
  moveFolderLoading: boolean;
  moveDocumentLoading: boolean;
}
