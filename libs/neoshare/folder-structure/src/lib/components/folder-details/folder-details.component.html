@if (users$ | async; as users) {
  <fin-modal-header>
    <fin-header>
      <span i18n="@@details.label">Details</span>
      <button fin-button-action [size]="finSize.L" fin-modal-close>
        <fin-icon name="close"></fin-icon>
      </button>
    </fin-header>
  </fin-modal-header>
  <fin-modal-slots-container>
    <fin-modal-slot [size]="finSize.L">
      <div
        class="tw-flex tw-flex-col tw-gap-[0.4rem] tw-text-color-text-primary tw-p-[2.4rem]"
      >
        <div class="tw-grid tw-grid-cols-2 tw-gap-8">
          <span
            class="tw-text-left tw-text-[1.4rem] tw-leading-[2.4rem] tw-font-medium"
            i18n="@@fsnapshot.details.labels.name"
            >Name</span
          >
          <span
            class="heading4 tw-text-right tw-leading-[2.4rem]"
            finTruncateText
          >
            {{ folder?.name }}
          </span>
        </div>
        <div class="tw-grid tw-grid-cols-2 tw-gap-8">
          <span
            class="tw-text-left tw-text-[1.4rem] tw-leading-[2.4rem] tw-font-medium"
            i18n="@@snapshot.details.labels.createdBy"
          >
            Erstellt von
          </span>
          <span class="heading4 tw-text-right tw-leading-[2.4rem]">
            {{
              users[folder?.createdById]
                | executeFunc: getUserFullName : folder?.createdById
            }}
          </span>
        </div>
        <div class="tw-grid tw-grid-cols-2 tw-gap-8">
          <span
            class="tw-text-left tw-text-[1.4rem] tw-leading-[2.4rem] tw-font-medium"
            i18n="@@snapshot.details.labels.dateCreated"
          >
            Erstellungsdatum
          </span>
          <span class="heading4 tw-text-right tw-leading-[2.4rem]">
            {{ folder?.creationDate | date: dateTimeFormat }}
          </span>
        </div>
        <div class="tw-grid tw-grid-cols-2 tw-gap-8">
          <span
            class="tw-text-left tw-text-[1.4rem] tw-leading-[2.4rem] tw-font-medium"
            i18n="@@snapshot.details.labels.updatedBy"
          >
            Aktualisiert von</span
          >
          <span
            class="heading4 tw-text-right tw-leading-[2.4rem]"
            finTruncateText
          >
            {{
              users[folder?.updatedById]
                | executeFunc: getUserFullName : folder?.updatedById
            }}
          </span>
        </div>
        <div class="tw-grid tw-grid-cols-2 tw-gap-8">
          <span
            class="tw-text-left tw-text-[1.4rem] tw-leading-[2.4rem] tw-font-medium"
            i18n="@@snapshot.details.labels.dateUpdated"
          >
            Aktualisiert am
          </span>
          <span class="heading4 tw-text-right tw-leading-[2.4rem]">
            {{ folder?.lastModifiedDate | date: dateTimeFormat }}
          </span>
        </div>
      </div>
    </fin-modal-slot>
  </fin-modal-slots-container>
}
