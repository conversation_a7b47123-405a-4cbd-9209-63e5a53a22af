import {
  ChangeDetectionStrategy,
  Component,
  Inject,
  OnInit,
} from '@angular/core';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { getUserFullName } from '@fincloud/core/utils';
import { selectUsers } from '@fincloud/state/users';
import { Folder } from '@fincloud/swagger-generator/business-case-manager';
import { FIN_MODAL_DATA } from '@fincloud/ui/modal';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';

@Component({
  selector: 'app-folder-details',
  templateUrl: './folder-details.component.html',
  styleUrl: './folder-details.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FolderDetailsComponent implements OnInit {
  finSize = FinSize;
  folder: Folder;
  getUserFullName = getUserFullName;

  users$ = this.store.select(selectUsers);

  readonly dateTimeFormat = `${this.regionalSettingsService.dateFormat} HH:mm`;

  constructor(
    @Inject(FIN_MODAL_DATA) private folderData: Folder,
    private store: Store,
    private regionalSettingsService: RegionalSettingsService,
  ) {}

  ngOnInit(): void {
    this.folder = this.folderData;
  }
}
