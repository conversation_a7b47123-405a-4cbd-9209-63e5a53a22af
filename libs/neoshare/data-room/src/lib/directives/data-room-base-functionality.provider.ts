import {
  selectGroupIntoView,
  StateLibDataRoomPageActions,
} from '@fincloud/state/data-room';
import { AppState } from '@fincloud/types/models';

import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { DestroyRef, Directive } from '@angular/core';

import {
  FieldDto,
  Folder,
  Information,
} from '@fincloud/swagger-generator/business-case-manager';
import { Store } from '@ngrx/store';
import { filter, take } from 'rxjs';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import {
  DraggableTemplateField,
  GroupTemplateFields,
  TemplateFieldViewModel,
} from '@fincloud/core/business-case';
import { HotkeyService } from '@fincloud/core/hotkeys';
import { ScrollCommunicationService } from '@fincloud/core/scroll';
import { searchNestedValues } from '@fincloud/core/utils';
import { Information as CompanyInformation } from '@fincloud/swagger-generator/company';
import { FieldType } from '@fincloud/types/enums';
import { FinSize } from '@fincloud/ui/types';
import { DATA_ROOM_MIN_SEARCH_LENGTH } from '@fincloud/utils';
import { cloneDeep, flatten } from 'lodash-es';
import { auditTime } from 'rxjs/operators';

@Directive()
export abstract class DataRoomBaseFunctionalityProvider {
  abstract searchControl: FormControl<string>;
  hasScrolled: boolean;
  groupHovered: { [key: number]: boolean } = {};
  editMode: boolean;
  filterMode = false;
  readonly finSize = FinSize;

  isDocumentInboxUploadInProgress: boolean;
  isDocumentInboxOpen: boolean;
  searchResultsCount: number = null;
  visibleGroupKeys = new Set<string>();

  groupsTemplateFields: GroupTemplateFields[];
  groupsTemplateFieldsInitial: GroupTemplateFields[];

  constructor(
    public destroyRef: DestroyRef,
    protected store: Store<AppState>,
    protected scrollCommunicationService: ScrollCommunicationService,
    protected hotkeysService: HotkeyService,
  ) {}

  elementIntoView(
    groupKey: string,
    groupsTemplateFields: GroupTemplateFields[],
  ) {
    const firstGroupKey = groupsTemplateFields[0]?.key;
    const key = this.hasScrolled ? groupKey : firstGroupKey;

    this.store.dispatch(
      StateLibDataRoomPageActions.setGroupIntoView({ payload: key }),
    );
  }

  elementOutsideOfView(
    groupKey: string,
    groupIndex: number,
    groupsTemplateFields: GroupTemplateFields[],
  ) {
    this.store
      .select(selectGroupIntoView)
      .pipe(take(1), takeUntilDestroyed(this.destroyRef))
      .subscribe((currentGroupIntoView) => {
        const previousGroupKey = groupsTemplateFields[groupIndex - 1]?.key;
        const isCurrentGroupOutOfView = currentGroupIntoView === groupKey;

        if (isCurrentGroupOutOfView && previousGroupKey) {
          this.store.dispatch(
            StateLibDataRoomPageActions.setGroupIntoView({
              payload: previousGroupKey,
            }),
          );
        }
      });
  }

  dropListHover(hovered: boolean, groupIndex: number) {
    this.groupHovered[groupIndex] = hovered;
  }

  openCreateField(
    event: CdkDragDrop<unknown> | 'fileDropped',
    group: GroupTemplateFields,
    groupIndex: number,
    fileUploadEvent?: DragEvent,
  ) {
    this.groupHovered[groupIndex] = false;

    const cdkEvent =
      event === 'fileDropped' ? ({} as CdkDragDrop<unknown>) : event;

    if (
      (!cdkEvent.isPointerOverContainer && event !== 'fileDropped') ||
      !this.editMode
    ) {
      return;
    }

    const droppedFieldData = cdkEvent.item?.data as DraggableTemplateField;
    const fieldGroup = group;
    const field = {
      ...({
        isRequired: false,
        label: '',
        expression: '',
        fieldMetaData: '',
        key: '',
        priority: 1,
      } as FieldDto),
      fieldType: droppedFieldData?.fieldType || FieldType.DOCUMENT,
    };

    if (field.fieldType === FieldType.TABLE) {
      this.openManageTableModal(group);
      return;
    }

    if (field.fieldType === FieldType.FOLDER) {
      this.openManageFolderModal(group);
      return;
    }

    this.openManageFieldModal(field as FieldDto, fieldGroup, fileUploadEvent);
  }

  onSearch(searchTerm: string) {
    const groupsInitial = cloneDeep(this.groupsTemplateFieldsInitial);
    this.searchResultsCount = null;
    if (!searchTerm) {
      this.groupsTemplateFields = groupsInitial;
      return;
    }
    this.updateGroupView();
    this.groupsTemplateFields = this.filterGroups(searchTerm, groupsInitial);
  }

  private filterGroups(searchTerm: string, groups: GroupTemplateFields[]) {
    const searchTermLowerCase = searchTerm.toLowerCase();
    const fields = flatten(
      groups?.map((g) => g.templateFields.map((tf) => tf.field)),
    );
    const information = flatten(
      groups?.map((g) => g.templateFields.map((tf) => tf.information)),
    );
    const matchingFieldsKeys = fields
      .filter((field) => {
        const fieldInformation = information.find((i) => i?.key === field.key);
        return (
          searchNestedValues(fieldInformation?.value, searchTermLowerCase) ||
          field.label.toLocaleLowerCase().includes(searchTermLowerCase) ||
          field.key.toLocaleLowerCase().includes(searchTermLowerCase)
        );
      })
      .map((f) => f.key);

    const matchingGroups = groups
      ?.map((g) => {
        g.templateFields = g.templateFields.filter((tf) =>
          matchingFieldsKeys.includes(tf.field.key),
        );
        g.documents = g.documents.filter((d) =>
          d.field.label.toLowerCase().includes(searchTermLowerCase),
        );
        g.rootFolder = this.filterFoldersByName(g, searchTermLowerCase);

        this.searchResultsCount =
          this.searchResultsCount +
          g.documents.length +
          g.templateFields.length +
          g.rootFolder.children.length;

        return g;
      })
      .filter(
        (g) =>
          g.documents.length ||
          g.templateFields.length ||
          g.rootFolder.children.length,
      );

    return matchingGroups;
  }

  filterFoldersByName(group: GroupTemplateFields, searchTerm: string): Folder {
    const search: (folder: Folder) => Folder[] = (folder: Folder): Folder[] => {
      const matches: Folder[] = [];

      if (folder?.name.toLowerCase().includes(searchTerm) && folder.parentId) {
        matches.push(cloneDeep(folder));
      }

      folder?.children?.forEach((child) => {
        matches.push(...search(child));
      });

      return matches;
    };

    return {
      id: group.rootFolder?.id,
      children: search(group.rootFolder),
      fields: group.documents.map((f) => f.field.key),
    };
  }

  listenForSearchChange() {
    this.searchControl.valueChanges
      .pipe(
        auditTime(400),
        filter(
          (searchTerm) =>
            !searchTerm || searchTerm?.length >= DATA_ROOM_MIN_SEARCH_LENGTH,
        ),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((searchTerm) => this.onSearch(searchTerm));
  }

  clearSearch() {
    this.searchResultsCount = null;
    if (!this.searchControl.value) {
      return;
    }
    this.searchControl.patchValue('');
  }

  trackByKey(
    index: number,
    elem: Information | CompanyInformation | GroupTemplateFields,
  ) {
    return elem.key;
  }

  trackByKeyTemplateField(index: number, elem: TemplateFieldViewModel) {
    return elem.field.key;
  }

  onUploadStartEnd(isUploading: boolean) {
    this.isDocumentInboxUploadInProgress = isUploading;
  }

  abstract openManageFolderModal(group: GroupTemplateFields): void;
  abstract openManageTableModal(group: GroupTemplateFields): void;
  abstract openManageFieldModal(
    field: FieldDto,
    group: GroupTemplateFields,
    initialFile?: FileList | DragEvent,
  ): void;

  // Highlights top visible group (only for Business Case for now) - start
  onGroupVisible(groupKey: string): void {
    this.visibleGroupKeys.add(groupKey);
    this.evaluateTopVisibleGroup();
  }

  onGroupNotVisible(groupKey: string): void {
    this.visibleGroupKeys.delete(groupKey);
    this.evaluateTopVisibleGroup();
  }

  private evaluateTopVisibleGroup(): void {
    // Find the first group (top group) whose key is in the visibleGroupKeys set,  which represents the groups currently visible in the UI.
    const topGroup = this.groupsTemplateFields.find((group) =>
      this.visibleGroupKeys.has(group.key),
    );

    if (topGroup && !this.searchControl.value && !this.filterMode) {
      this.updateGroupView(topGroup.key);
    } else {
      this.updateGroupView();
    }
  }

  updateGroupView(groupKey?: string): void {
    // Disabled due to a performance issue with fin-tree-menu. This will be re-enabled once the issue is resolved and performance is optimized.
    // this.store.dispatch(
    //   StateLibBusinessCaseDataRoomPageActions.highlightTopGroupInView({
    //     highlightedTopGroupKey: groupKey ?? '',
    //   }),
    // );
  }
  // Highlights top visible group (only for Business Case for now) - end
}
