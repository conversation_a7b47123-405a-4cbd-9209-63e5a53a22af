import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  BusinessCaseControllerService,
  FieldDto,
  Group,
  Information,
  InformationControllerService,
} from '@fincloud/swagger-generator/business-case-manager';
import {
  BusinessCaseGroup,
  BusinessCaseInformation,
  ExchangeBusinessCase,
  Folder,
  InformationRecord,
} from '@fincloud/swagger-generator/exchange';
import {
  AppState,
  BusinessCaseDataRoomFilters,
  TemplateFieldViewModel,
} from '@fincloud/types/models';
import { Store } from '@ngrx/store';
import { flatten, isNumber } from 'lodash-es';
import { EMPTY, Observable, Subject } from 'rxjs';
import { catchError, switchMap, tap } from 'rxjs/operators';

import {
  buildDefault,
  TableCreateConfig,
} from '@fincloud/components/data-grid';
import { GroupTemplateFields } from '@fincloud/core/business-case';
import { IdentityService } from '@fincloud/core/services';
import { Toast } from '@fincloud/core/toast';
import { StateLibBusinessCasePageActions } from '@fincloud/state/business-case';
import { DocumentEntity } from '@fincloud/swagger-generator/document';
import {
  DataRoomFilterFieldType,
  DataType,
  DocumentMimeType,
  PeriodOption,
} from '@fincloud/types/enums';
import { FinToastService } from '@fincloud/ui/toast';
import { getMimeTypesByDataType } from '@fincloud/utils';

@Injectable({
  providedIn: 'root',
})
export class BusinessCaseDataRoomHelperService {
  private editValueEmitter$$ = new Subject<Record<string, Information>>();
  editValueEmitter$ = this.editValueEmitter$$.asObservable();

  constructor(
    private informationControllerService: InformationControllerService,
    private businessCaseControllerService: BusinessCaseControllerService,
    private store: Store<AppState>,
    private finToastService: FinToastService,
  ) {}

  mapToInformationRecord(information: { [key: string]: Information }) {
    return Object.keys(information).reduce(
      (result: InformationRecord, key: string) => {
        result[key as keyof InformationRecord] = information[
          key
        ] as BusinessCaseInformation;
        result[key as keyof InformationRecord].lastModifiedDate =
          information[key].lastModifiedDate;

        return result;
      },
      {} as InformationRecord,
    );
  }

  handleEditValueRequest(
    information: BusinessCaseInformation,
    newValue: string,
    caseId: string,
    refreshBusinessCaseData = true,
    isPortalEdit = false,
  ): Observable<Information | unknown> {
    let editValue: unknown;

    // if user saves empty field and the field is new -> return
    // if user saves field and the value hasn't changed -> return
    if (
      (!newValue &&
        !isNumber(newValue) &&
        (!information || !information?.value)) ||
      newValue === information?.value
    ) {
      return EMPTY;
    }

    if (newValue === null || newValue === undefined) {
      editValue = [];
    } else {
      const isNumeric = isNumber(newValue);
      editValue = isNumeric ? newValue : JSON.stringify(newValue);
    }

    const updateInformationParams = {
      businessCaseId: caseId,
      body: editValue,
      informationId: information.id,
    };

    const editValueRequest = !isPortalEdit
      ? this.informationControllerService.updateInformationValue(
          updateInformationParams,
        )
      : this.informationControllerService.updateInformationValueFromPortal(
          updateInformationParams,
        );

    return editValueRequest.pipe(
      tap((businessCaseInformation) => {
        if (refreshBusinessCaseData) {
          this.editValueEmitter$$.next(businessCaseInformation);
        }
      }),
      catchError((response: HttpErrorResponse) => {
        throw new Error(response.error?.message);
      }),
    );
  }

  editBusinessCaseGroups(
    businessCaseId: string,
    groupsOrdered: Group[],
    isRepresentingLeadPartner: boolean,
    fieldDto?: FieldDto,
  ) {
    return isRepresentingLeadPartner
      ? this.businessCaseControllerService.addFieldAndEditBusinessCaseGroups({
          businessCaseId,
          body: {
            groupsOrdered,
            fieldDto,
          },
        })
      : this.businessCaseControllerService.editAndMergeBusinessCaseGroups({
          businessCaseId,
          body: {
            groupsOrdered,
            fieldDto,
          },
        });
  }

  get emptyField() {
    return {
      isRequired: false,
      label: '',
      fieldType: 'SHORT_TEXT',
      expression: '',
      fieldMetaData: '',
      key: '',
      priority: 1,
    } as FieldDto;
  }

  createTableField(
    businessCase: ExchangeBusinessCase,
    tableCreateConfig: TableCreateConfig,
    groupTemplateFields: GroupTemplateFields,
    isRepresentingLeadPartner: boolean,
  ) {
    const tableValue = buildDefault(tableCreateConfig).toDto();

    const requestParams = {
      businessCaseId: businessCase.id,
      body: {
        fieldType: 'TABLE',
        key: IdentityService.generateId(),
        label: tableCreateConfig.label,
        value: tableValue,
        priority: 1,
      } as FieldDto,
    };

    this.businessCaseControllerService
      .addFieldToBusinessCase(requestParams)
      .pipe(
        switchMap(() => {
          // update groups structure
          const groupsOrderedModify = structuredClone(
            businessCase.businessCaseTemplate.template.groupsOrdered,
          );
          groupsOrderedModify
            .find((g: BusinessCaseGroup) => g.key === groupTemplateFields.key)
            .fields.push(requestParams.body.key);
          return this.editBusinessCaseGroups(
            businessCase.id,
            groupsOrderedModify,
            isRepresentingLeadPartner,
          );
        }),
        catchError((response: HttpErrorResponse) => {
          this.finToastService.show(Toast.error(response.error?.message));
          return EMPTY;
        }),
        tap(() =>
          this.store.dispatch(
            StateLibBusinessCasePageActions.loadBusinessCase({
              payload: businessCase.id,
            }),
          ),
        ),
      )
      .subscribe();
  }

  filterGroups(
    searchTerm: string | null,
    groups: GroupTemplateFields[],
    filtersValue: BusinessCaseDataRoomFilters,
    documentFiles: DocumentEntity[],
  ): {
    matchingGroups: GroupTemplateFields[];
    searchResultsCount: number | null;
  } {
    const searchTermLowerCase = searchTerm?.toLocaleLowerCase() || '';

    const matchingGroups = [groups]
      .map((groups) => this.filterGroupsBySelectedKeys(groups, filtersValue))
      .map((groups) =>
        this.filterTemplateFields(groups, searchTermLowerCase, filtersValue),
      )
      .map((groups) =>
        this.handleDocumentsAndFolders(
          groups,
          searchTermLowerCase,
          filtersValue,
          documentFiles,
        ),
      )
      .map(this.removeEmptyGroups)[0];

    const searchResultsCount =
      this.countSearchResults(matchingGroups, searchTermLowerCase) || null;

    return {
      matchingGroups,
      searchResultsCount,
    };
  }

  private filterTemplateFields(
    groups: GroupTemplateFields[],
    searchTerm: string,
    filtersValue: BusinessCaseDataRoomFilters,
  ): GroupTemplateFields[] {
    const matchingFieldKeys =
      filtersValue?.fieldType !== DataRoomFilterFieldType.DOCUMENT_AND_FOLDERS
        ? this.getMatchingFieldKeys(groups, searchTerm, filtersValue)
        : [];
    return groups.map((group) => ({
      ...group,
      templateFields: group.templateFields.filter((tf) =>
        matchingFieldKeys.includes(tf.field.key),
      ),
    }));
  }

  private handleDocumentsAndFolders(
    groups: GroupTemplateFields[],
    searchTermLowerCase: string,
    filtersValue: BusinessCaseDataRoomFilters,
    documentFiles: DocumentEntity[],
  ): GroupTemplateFields[] {
    const selectedTypes = this.getSelectedDataTypes(filtersValue);
    const allowedMimeTypes = this.getAllowedMimeTypes(selectedTypes);
    const hasSelectedFolder = selectedTypes.includes(DataType.FOLDER);

    return groups.map((group) => {
      if (filtersValue?.fieldType === DataRoomFilterFieldType.DATA) {
        return {
          ...group,
          documents: [],
          rootFolder: {
            id: group.rootFolder?.id,
            children: [],
            fields: [],
          },
        };
      }
      const filteredDocuments = this.filterDocuments(
        group.documents,
        searchTermLowerCase,
        filtersValue,
        allowedMimeTypes,
        hasSelectedFolder,
        documentFiles,
      );

      return {
        ...group,
        documents: filteredDocuments,
        rootFolder: this.filterFolders(
          { ...group, documents: filteredDocuments },
          searchTermLowerCase,
          filtersValue,
          allowedMimeTypes.length > 0,
          hasSelectedFolder,
        ),
      };
    });
  }

  private countSearchResults(
    groups: GroupTemplateFields[],
    searchTermLowerCase: string,
  ): number {
    if (searchTermLowerCase === '') {
      return 0;
    }
    return groups.reduce(
      (count, group) =>
        count +
        group.documents.length +
        group.templateFields.length +
        group.rootFolder.children.length,
      0,
    );
  }

  private removeEmptyGroups(
    groups: GroupTemplateFields[],
  ): GroupTemplateFields[] {
    return groups.filter(
      (group) =>
        group.documents.length ||
        group.templateFields.length ||
        group.rootFolder.children.length,
    );
  }

  private getMatchingFieldKeys(
    groups: GroupTemplateFields[],
    searchTerm: string,
    filtersValue: BusinessCaseDataRoomFilters,
  ): string[] {
    const informations = flatten(
      groups?.map((g) => g.templateFields.map((tf) => tf.information)),
    );

    return informations
      .filter((information) => {
        const valueMatches =
          JSON.stringify(information?.value)
            ?.toLocaleLowerCase()
            .includes(searchTerm) ||
          information.field.label.toLocaleLowerCase().includes(searchTerm) ||
          information.field.key.toLocaleLowerCase().includes(searchTerm);

        const userMatches =
          !filtersValue?.updatedBy?.length ||
          filtersValue.updatedBy.includes(information?.userId);

        const dateMatches = this.checkIsDateInSelectedPeriod(
          information?.lastModifiedDate,
          filtersValue,
        );

        return valueMatches && userMatches && dateMatches;
      })
      .map((f) => f.key);
  }

  private filterGroupsBySelectedKeys(
    groups: GroupTemplateFields[],
    filtersValue: BusinessCaseDataRoomFilters,
  ): GroupTemplateFields[] {
    if (filtersValue?.groups?.length) {
      return groups.filter((g) => filtersValue.groups.includes(g.key));
    }
    return groups;
  }

  private getSelectedDataTypes(
    filtersValue: BusinessCaseDataRoomFilters,
  ): string[] {
    return filtersValue?.dataTypesSelected
      ? Object.keys(filtersValue.dataTypesSelected).filter(
          (key) => filtersValue.dataTypesSelected[key],
        )
      : [];
  }

  private getAllowedMimeTypes(
    selectedTypes: string[],
  ): DocumentMimeType[] | string[] {
    return selectedTypes
      .filter((type) => type !== DataType.FOLDER)
      .flatMap((type) => getMimeTypesByDataType(type));
  }

  private filterDocuments(
    documents: TemplateFieldViewModel[],
    searchTerm: string,
    filtersValue: BusinessCaseDataRoomFilters,
    allowedMimeTypes: string[],
    hasSelectedFolder: boolean,
    documentFiles: DocumentEntity[],
  ): TemplateFieldViewModel[] {
    return documents.filter((d) => {
      const matchesSearch = d.field.label
        .toLocaleLowerCase()
        .includes(searchTerm);

      const matchesUser =
        !filtersValue?.updatedBy?.length ||
        filtersValue.updatedBy.includes(d.information?.userId);

      const matchesDate = this.checkIsDateInSelectedPeriod(
        d.information?.lastModifiedDate,
        filtersValue,
      );

      const matchingDoc = documentFiles.find(
        (doc) => doc.id === d.information?.value,
      );
      const contentType = matchingDoc?.contentReference?.contentType;

      const noMimeTypesSelected =
        allowedMimeTypes.length === 0 && !hasSelectedFolder;

      const isAllowedContentType =
        contentType && allowedMimeTypes.includes(contentType);

      const isPlaceholderAllowed =
        !contentType && allowedMimeTypes.includes(DataType.PLACEHOLDER);

      const matchesMimeType =
        noMimeTypesSelected || isAllowedContentType || isPlaceholderAllowed;

      return matchesSearch && matchesUser && matchesDate && matchesMimeType;
    });
  }

  private filterFolders(
    group: GroupTemplateFields,
    searchTerm: string,
    filtersValue: BusinessCaseDataRoomFilters,
    hasSelectedDocumentTypes: boolean,
    hasSelectedFolder: boolean,
  ): Folder {
    return {
      id: group.rootFolder?.id,
      children: this.searchFolders(
        group.rootFolder,
        group,
        searchTerm,
        filtersValue,
        hasSelectedDocumentTypes,
        hasSelectedFolder,
      ),
      fields: group.documents.map((f) => f.field.key),
    };
  }

  private searchFolders(
    folder: Folder,
    group: GroupTemplateFields,
    searchTerm: string,
    filtersValue: BusinessCaseDataRoomFilters,
    hasSelectedDocumentTypes: boolean,
    hasSelectedFolder: boolean,
  ): Folder[] {
    const matches: Folder[] = [];

    const matchesName = folder?.name?.toLocaleLowerCase().includes(searchTerm);
    const hasParent = !!folder?.parentId;
    const matchesUser =
      !filtersValue?.updatedBy?.length ||
      filtersValue.updatedBy.includes(folder?.updatedById);
    const matchesDate = this.checkIsDateInSelectedPeriod(
      folder.lastModifiedDate,
      filtersValue,
    );

    const includeFolder =
      !(hasSelectedDocumentTypes && !hasSelectedFolder) &&
      matchesName &&
      hasParent &&
      matchesUser &&
      matchesDate;

    const documentKeys = group.documents.map((doc) => doc.field.key);

    const documentFields = folder.fields?.filter((field) =>
      documentKeys.includes(field),
    );

    const childMatches = (folder.children ?? []).flatMap((child) =>
      this.searchFolders(
        child,
        group,
        searchTerm,
        filtersValue,
        hasSelectedDocumentTypes,
        hasSelectedFolder,
      ),
    );

    if (includeFolder) {
      const filteredChildren = childMatches.filter(
        (child) => child.parentId === folder.id,
      );

      matches.push({
        ...folder,
        fields: documentFields,
        children: filteredChildren,
      });
    }

    matches.push(...childMatches);

    return matches;
  }

  checkIsDateInSelectedPeriod = (
    dateToCheckStr: string,
    filters: BusinessCaseDataRoomFilters,
  ): boolean => {
    const selectedPeriod = filters?.dateUpdated;
    if (!selectedPeriod) {
      return true;
    }

    const normalizeDate = (date: Date) => {
      date.setHours(0, 0, 0, 0);
      return date;
    };

    const dateToCheck = normalizeDate(new Date(dateToCheckStr));
    const today = normalizeDate(new Date());

    const getDateDaysAgo = (daysAgo: number) => {
      const pastDate = new Date(today);
      pastDate.setDate(today.getDate() - daysAgo);
      return pastDate;
    };

    switch (selectedPeriod) {
      case PeriodOption.TODAY:
        return dateToCheck.getTime() === today.getTime();

      case PeriodOption.SINCE_YESTERDAY:
        return dateToCheck >= getDateDaysAgo(1);

      // Includes today and the previous 6 days (i.e., last 7 calendar days)
      case PeriodOption.LAST_7_DAYS:
        return dateToCheck >= getDateDaysAgo(6);

      // Includes today and the previous 29 days (i.e., last 30 calendar days)
      case PeriodOption.LAST_30_DAYS:
        return dateToCheck >= getDateDaysAgo(29);

      case PeriodOption.LAST_3_MONTHS: {
        const threeMonthsAgo = new Date(today);
        threeMonthsAgo.setMonth(today.getMonth() - 3);
        normalizeDate(threeMonthsAgo);
        return dateToCheck >= threeMonthsAgo;
      }

      case PeriodOption.CUSTOM_RANGE: {
        const fromDate = filters.fromDate
          ? normalizeDate(new Date(filters.fromDate))
          : null;
        const toDate = filters.toDate
          ? normalizeDate(new Date(filters.toDate))
          : null;

        return (
          (!fromDate || dateToCheck >= fromDate) &&
          (!toDate || dateToCheck <= toDate)
        );
      }

      default:
        throw new Error(`Unknown period: ${selectedPeriod}`);
    }
  };
}
