import { inject, Injectable } from '@angular/core';
import {
  FileManagerService,
  TusDocumentUploadController,
} from '@fincloud/core/files';
import { IdentityService } from '@fincloud/core/services';
import { Toast } from '@fincloud/core/toast';
import { DocumentFieldManageHelperService } from '@fincloud/neoshare/document';
import { StateLibBusinessCaseApiActions } from '@fincloud/state/business-case';
import { selectCustomerKey } from '@fincloud/state/customer';
import { TusUploadType } from '@fincloud/types/enums';
import { ValueChangeModel } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { Store } from '@ngrx/store';
import { cloneDeep } from 'lodash-es';
import { catchError, Observable, switchMap, take, tap, throwError } from 'rxjs';

@Injectable()
export abstract class DataRoomCustomHandlerService {
  protected documentFieldManageHelperService = inject(
    DocumentFieldManageHelperService,
  );
  protected finToastService = inject(FinToastService);
  protected tusDocumentUploadController = inject(TusDocumentUploadController);
  protected fileManagerService = inject(FileManagerService);
  protected identityService = inject(IdentityService);
  protected store = inject(Store);

  abstract handleEditValue(data: ValueChangeModel): Observable<unknown>;

  handleFile(data: ValueChangeModel, ownerType: 'BUSINESS_CASE' | 'COMPANY') {
    const { dataRoomOwnerId } = data;
    const endpoint =
      ownerType === 'BUSINESS_CASE'
        ? this.tusDocumentUploadController.getUploadBusinessCaseDocumentPath(
            dataRoomOwnerId,
          )
        : this.tusDocumentUploadController.getUploadCompanyDocumentPath(
            dataRoomOwnerId,
          );

    return this.store.select(selectCustomerKey).pipe(
      switchMap((customerKey) =>
        this.fileManagerService.upload({
          businessCaseId: dataRoomOwnerId,
          endpoint,
          // TODO: Double check
          typeOfTusUpload:
            ownerType === 'BUSINESS_CASE'
              ? TusUploadType.BUSINESS_CASE
              : TusUploadType.COMPANY,
          docType: 'DATA_ROOM',
          file: data.value as File,
          customerKey,
          uploadId: this.identityService.generateKey(),
        }),
      ),
      tap(({ document }) => {
        this.store.dispatch(
          StateLibBusinessCaseApiActions.businessCaseDocumentUploadedSuccess({
            document,
          }),
        );
      }),
      switchMap(({ document }) => {
        const dataModified = cloneDeep(data);
        dataModified.value = document.id;

        return this.handleEditValue(dataModified);
      }),
      tap(() =>
        this.finToastService.show(
          Toast.success(
            $localize`:@@dataRoom.customHandler.toast.success:Dokument erfolgreich aktualisiert`,
          ),
        ),
      ),
      catchError((error) => {
        const toastErrorMessage =
          this.documentFieldManageHelperService.getHandleFileErrorMsg(
            data.value as File,
            error.message,
          );
        this.finToastService.show(Toast.error(toastErrorMessage));

        return throwError(() => error);
      }),
      take(1),
      // Todo check if this makes problems
    );
  }
}
