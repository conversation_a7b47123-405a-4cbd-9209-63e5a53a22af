import { DOCUMENT } from '@angular/common';
import { Inject, Injectable } from '@angular/core';
import { FieldType } from '@fincloud/types/enums';
import { DataRoomHighlight } from '@fincloud/types/models';
import {
  DATA_ROOM_DOCUMENTS_SECTION_PREFIX,
  DATA_ROOM_DOCUMENTS_SECTION_SCROLL_CONTAINER_PREFIX,
  DATA_ROOM_MAIN_SCROLLBAR_ID,
} from '@fincloud/utils';

@Injectable()
export class DataRoomScrollService {
  constructor(@Inject(DOCUMENT) private document: Document) {}

  scrollToDataRoomItem(highlightItem: DataRoomHighlight) {
    if (
      highlightItem.fieldType === FieldType.FOLDER ||
      highlightItem.fieldType === FieldType.DOCUMENT
    ) {
      this.scrollToFolderStructureItem(highlightItem);
      return;
    }

    this.scrollToFieldOrSection(highlightItem.key);
  }

  private scrollToFolderStructureItem(highlightItem: DataRoomHighlight): void {
    // Find the documents section and scroll it to the top of the data room visible area
    const documentsSectionSelector =
      DATA_ROOM_DOCUMENTS_SECTION_PREFIX + highlightItem.groupKey;
    this.scrollToFieldOrSection(documentsSectionSelector);

    // Find the target item in the documents section and scroll it to the top of the documents section visible area
    const groupDocumentsSectionScrollbar = this.document.querySelector(
      `#${DATA_ROOM_DOCUMENTS_SECTION_SCROLL_CONTAINER_PREFIX + highlightItem.groupKey}`,
    );
    const documentsSectionItem = this.document.querySelector(
      `#${highlightItem.key}`,
    );

    const folderStructureItemScrollTopOffset =
      documentsSectionItem.getBoundingClientRect().top -
      groupDocumentsSectionScrollbar.getBoundingClientRect().top +
      groupDocumentsSectionScrollbar.scrollTop;

    groupDocumentsSectionScrollbar.scrollTo({
      top: folderStructureItemScrollTopOffset,
      behavior: 'smooth',
    });
  }

  private scrollToFieldOrSection(selector: string): void {
    const dataRoomMainScrollbar = this.document.querySelector(
      `#${DATA_ROOM_MAIN_SCROLLBAR_ID} > ng-scrollbar`,
    );
    const groupDocumentsSection = this.document.querySelector(`#${selector}`);
    const groupDocumentsSectionScrollTopOffset =
      groupDocumentsSection.getBoundingClientRect().top -
      dataRoomMainScrollbar.getBoundingClientRect().top +
      dataRoomMainScrollbar.scrollTop;

    dataRoomMainScrollbar.scrollTo({
      top: groupDocumentsSectionScrollTopOffset,
      behavior: 'smooth',
    });
  }
}
