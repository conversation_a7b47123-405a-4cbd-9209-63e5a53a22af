import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  buildDefault,
  TableCreateConfig,
} from '@fincloud/components/data-grid';
import { GroupTemplateFields } from '@fincloud/core/business-case';
import { FieldTypeEnum } from '@fincloud/core/formly';
import { IdentityService } from '@fincloud/core/services';
import { Toast } from '@fincloud/core/toast';
import {
  selectCompany,
  selectCompanyDataRoomInformation,
  selectCustomerCadrTemplate,
  StateLibCompanyPageActions,
} from '@fincloud/state/company-analysis';
import {
  CadrGroup,
  Company,
  CompanyControllerService,
  FieldDto,
  Information,
  InformationControllerService,
} from '@fincloud/swagger-generator/company';
import { DocumentCompanyControllerService } from '@fincloud/swagger-generator/document';
import { AppState } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { Store } from '@ngrx/store';
import { cloneDeep, isEqual, isNumber } from 'lodash-es';
import {
  catchError,
  concat,
  EMPTY,
  forkJoin,
  last,
  map,
  Observable,
  of,
  switchMap,
  take,
  tap,
} from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CompanyDataRoomHelperService {
  company: Company;
  constructor(
    private store: Store<AppState>,
    private informationControllerService: InformationControllerService,
    private companyControllerService: CompanyControllerService,
    private documentCompanyControllerService: DocumentCompanyControllerService,
    private finToastService: FinToastService,
  ) {}

  checkIfCadrTemplateAttached(): Observable<boolean> {
    return forkJoin([
      this.store.select(selectCompany).pipe(take(1)),
      this.store.select(selectCustomerCadrTemplate).pipe(take(1)),
      this.store.select(selectCompanyDataRoomInformation).pipe(take(1)),
    ]).pipe(
      map(([company, cadrTemplate, information]) => {
        this.company = company;
        if (!company.companyTemplate?.template) {
          return true;
        }
        return !(
          isEqual(company.companyTemplate?.template, cadrTemplate) &&
          (information === null || Object.keys(information).length === 0)
        );
      }),
    );
  }

  handleEditValueRequest(
    information: Information,
    newValue: string,
    companyId: string,
    refreshCompanyData = true,
    fieldKey?: string,
  ): Observable<Information | unknown> {
    let editValue: unknown;

    // if user saves empty field and the field is new -> return
    // if user saves field and the value hasn't changed -> return
    if (
      (!newValue && (!information || !information?.value)) ||
      newValue === information?.value
    ) {
      return EMPTY;
    }

    if (newValue === null || newValue === undefined) {
      editValue = [];
    } else {
      const isNumeric = isNumber(newValue);
      editValue = isNumeric ? newValue : JSON.stringify(newValue);
    }
    const updateInformationValue = (
      companyId: string,
      informationId: string,
      editValue: unknown,
      refreshCompanyData: boolean,
    ) =>
      this.informationControllerService
        .updateInformationValue({
          companyId,
          body: editValue,
          informationId,
        })
        .pipe(
          tap(() => {
            if (refreshCompanyData) {
              this.store.dispatch(
                StateLibCompanyPageActions.reloadCompany({
                  payload: companyId,
                }),
              );
            }
          }),
          catchError((response: HttpErrorResponse) => {
            throw new Error(response.error?.message);
          }),
        );

    if (!information?.id) {
      return this.checkIfCadrTemplateAttached().pipe(
        switchMap((isAttached) => {
          if (!isAttached) {
            return this.attachCompanyTemplate(
              companyId,
              this.company.companyTemplate?.template?.fields || [],
            ).pipe(
              switchMap(() => {
                return this.informationControllerService
                  .getAllInformation({
                    companyId: companyId,
                    includeDeleted: false,
                  })
                  .pipe(
                    switchMap((information) => {
                      const informationId = information[fieldKey].id;
                      return updateInformationValue(
                        companyId,
                        informationId,
                        editValue,
                        refreshCompanyData,
                      );
                    }),
                  );
              }),
            );
          }
          return of(null);
        }),
      );
    } else {
      return updateInformationValue(
        companyId,
        information.id,
        typeof editValue === 'string' && editValue.length === 0
          ? []
          : editValue,
        refreshCompanyData,
      );
    }
  }

  // TODO: when we have inbox

  //   moveDocumentsToGroup(
  //     documents: SelectableDocumentEntity[],
  //     businessCase: ExchangeBusinessCase,
  //     group: BusinessCaseGroup
  //   ) {
  //     const newFieldsRequests: Observable<BusinessCase>[] = [];
  //     const businessCaseGroupsModify = cloneDeep(
  //       businessCase.businessCaseTemplate.template.groupsOrdered
  //     );
  //     const groupToModify = businessCaseGroupsModify.find(
  //       (g: BusinessCaseGroup) => g.key === group.key
  //     );

  //     documents.forEach((d) => {
  //       this.store.dispatch(DeleteDocument({ payload: d }));

  //       const documentFieldDto = {
  //         fieldType: 'DOCUMENT',
  //         key: IdentityService.generateId(),
  //         isPublic: false,
  //         label: d.documentName,
  //         value: d.id,
  //         priority: 1,
  //       } as FieldDto;

  //       groupToModify.fields.push(documentFieldDto.key);

  //       newFieldsRequests.push(
  //         this.businessCaseControllerService
  //           .addFieldToBusinessCase({
  //             businessCaseId: businessCase.id,
  //             body: documentFieldDto,
  //           })
  //           .pipe(
  //             switchMap(() =>
  //               this.documentInboxControllerService.moveInboxDocumentToStandardBucketUsingPOST(
  //                 d.id
  //               )
  //             ),
  //             switchMap(() =>
  //               this.businessCaseControllerService.editBusinessCaseGroups({
  //                 body: businessCaseGroupsModify,
  //                 businessCaseId: businessCase.id,
  //               })
  //             )
  //           )
  //       );
  //     });

  //     return concat(...newFieldsRequests);
  //   }

  get emptyField() {
    return {
      isRequired: false,
      label: '',
      fieldType: 'SHORT_TEXT',
      expression: '',
      fieldMetaData: '',
      key: '',
      priority: 1,
    } as FieldDto;
  }

  createTableField(
    company: Company,
    tableCreateConfig: TableCreateConfig,
    groupTemplateFields: GroupTemplateFields,
  ) {
    const tableValue = buildDefault(tableCreateConfig).toDto();
    const fieldKey = IdentityService.generateId();
    this.companyControllerService
      .addFieldToCompany({
        companyId: company.id,
        body: {
          fieldType: 'TABLE',
          key: fieldKey,
          label: tableCreateConfig.label,
          value: tableValue,
          priority: 1,
        } as FieldDto,
      })
      .pipe(
        switchMap(() => {
          // update groups structure
          const groupsOrderedModify = cloneDeep(
            company.companyTemplate.template.groupsOrdered,
          );
          groupsOrderedModify
            .find((g) => g.key === groupTemplateFields.key)
            .fields.push(fieldKey);

          return this.manageGroupsChangeObservable(
            company.id,
            groupsOrderedModify,
          ).pipe(
            tap(() => {
              this.store.dispatch(
                StateLibCompanyPageActions.reloadCompany({
                  payload: company.id,
                }),
              );
            }),
          );
        }),
        catchError((response: HttpErrorResponse) => {
          this.finToastService.show(Toast.error(response.error?.message));
          return of(null);
        }),
      )
      .subscribe();
  }

  manageGroupsChange(
    companyId: string,
    groups: CadrGroup[],
    showToast = false,
  ) {
    this.manageGroupsChangeObservable(companyId, groups).subscribe({
      next: (company) => {
        if (showToast) {
          this.finToastService.show(Toast.success());
        }
        this.store.dispatch(
          StateLibCompanyPageActions.setCompany({ payload: company }),
        );
      },
      error: () => {
        this.finToastService.show(Toast.error());
      },
    });
  }

  handleEditGroups(
    isSuccessful: boolean,
    company: Company,
    isError: boolean,
    showToast: boolean,
  ) {
    if (isSuccessful) {
      if (showToast) {
        this.finToastService.show(Toast.success());
      }
      this.store.dispatch(
        StateLibCompanyPageActions.setCompany({ payload: company }),
      );
    }

    if (isError) {
      this.finToastService.show(Toast.error());
    }
  }

  manageGroupsChangeObservable(companyId: string, groupsOrdered: CadrGroup[]) {
    return this.checkIfCadrTemplateAttached().pipe(
      switchMap((isAttached) => {
        if (!isAttached) {
          return this.attachCompanyTemplate(
            companyId,
            this.company.companyTemplate?.template?.fields || [],
          );
        }

        return of(null);
      }),
      switchMap(() => {
        return this.companyControllerService.addFieldAndEditCompanyGroups({
          companyId,
          body: { groupsOrdered },
        });
      }),
    );
  }

  copyFields(informations: Information[], group: CadrGroup, company: Company) {
    this.checkIfCadrTemplateAttached()
      .pipe(
        switchMap((isAttached) => {
          if (!isAttached) {
            return this.attachCompanyTemplate(
              company.id,
              this.company.companyTemplate?.template?.fields || [],
            );
          }

          return of(null);
        }),
        switchMap(() => {
          const groupExists =
            company.companyTemplate &&
            company.companyTemplate.template.groupsOrdered
              .map((g) => g.key)
              .includes(group.key);
          if (!groupExists) {
            // if new group, create group first
            const groupsManaged: CadrGroup[] =
              company.companyTemplate && company.companyTemplate.template
                ? [...company.companyTemplate.template.groupsOrdered]
                : [];

            groupsManaged.push({
              groupVisibility: {
                visibility: 'PRIVATE',
              },
              fields: [],
              value: group.key,
            });

            return this.companyControllerService.addFieldAndEditCompanyGroups({
              companyId: company.id,
              body: {
                groupsOrdered: groupsManaged,
              },
            });
          }

          return of(null);
        }),
        switchMap((responseCompany) => {
          const fieldInformation = informations.map((information) => {
            const cloneInformation = { ...information };
            cloneInformation.field = { ...information.field };
            cloneInformation.field.key = IdentityService.generateId();

            return this.companyControllerService
              .getCompanyById({
                id: company.id,
              })
              .pipe(
                switchMap((latestCompanyData) => {
                  return this.copyField(
                    cloneInformation,
                    latestCompanyData,
                    latestCompanyData.companyTemplate.template.groupsOrdered.find(
                      (cadrGroup) =>
                        responseCompany
                          ? cadrGroup.value === group.key
                          : cadrGroup.key === group.key,
                    ),
                  );
                }),
              );
          });
          return concat(...fieldInformation);
        }),
        last(),
      )
      .subscribe({
        next: (company) => {
          this.company = company;
          this.store.dispatch(
            StateLibCompanyPageActions.reloadCompany({ payload: company.id }),
          );
          this.finToastService.show(Toast.success());
        },
        error: () => {
          this.finToastService.show(Toast.error());
        },
      });
  }

  private copyField(
    information: Information,
    company: Company,
    group: CadrGroup,
  ): Observable<Company> {
    if (information.field.fieldType === FieldTypeEnum.DOCUMENT) {
      return this.documentCompanyControllerService
        .duplicateDocumentForCompany({
          newCompanyId: company.id,
          documentId: information.value as string,
        })
        .pipe(
          switchMap((documentEntity) =>
            this.addFieldToCompany(
              information,
              company,
              group,
              documentEntity.id,
            ),
          ),
        );
    }
    return this.addFieldToCompany(information, company, group);
  }

  addFieldToCompany(
    information: Information,
    company: Company,
    group: CadrGroup,
    documentId?: string,
  ): Observable<Company> {
    const fieldDto: FieldDto = {
      ...information.field,
      value: documentId ?? information.value,
    };

    const groupsManaged: CadrGroup[] = cloneDeep(
      company.companyTemplate.template.groupsOrdered,
    );

    groupsManaged
      .find((g: CadrGroup) => g.key === group.key)
      .fields.push(fieldDto.key);

    return this.companyControllerService
      .addFieldAndEditCompanyGroups({
        companyId: company.id,
        body: {
          groupsOrdered: groupsManaged,
          fieldDto,
        },
      })
      .pipe(catchError(() => EMPTY));
  }

  attachCompanyTemplate(companyId: string, companyTemplateFields: FieldDto[]) {
    return this.companyControllerService.attachTemplate({
      companyId,
      body: {
        parameters: companyTemplateFields.map(({ key: name, value: val }) => ({
          name,
          val: val ? val : [],
        })),
      },
    });
  }
}
