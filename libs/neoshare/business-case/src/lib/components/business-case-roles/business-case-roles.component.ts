import {
  selectBusinessCase,
  selectCustomerBusinessCaseContext,
  selectHasAnyBusinessCasePermission,
  selectIsBusinessCaseActive,
  selectIsParticipationTypeLeader,
} from '@fincloud/state/business-case';

import { ModalService } from '@fincloud/core/modal';
import { CURRENCY_MASK_CONFIG } from '@fincloud/core/utils';

import {
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  BusinessCase,
  BusinessCaseControllerService,
  CasePermissionControllerService,
  Group,
  ParticipantCasePermissionSetEntity,
} from '@fincloud/swagger-generator/business-case-manager';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { Store } from '@ngrx/store';
import {
  EMPTY,
  Observable,
  catchError,
  filter,
  forkJoin,
  of,
  switchMap,
  tap,
} from 'rxjs';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, Validators } from '@angular/forms';
import { ConfirmationModalComponent } from '@fincloud/components/modals';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { Toast } from '@fincloud/core/toast';
import {
  StateLibBusinessCasePageActions,
  StateLibMyPartnersPageActions,
  selectCanPlatformManagerAddHimself,
} from '@fincloud/state/business-case';
import { CadrGroup } from '@fincloud/swagger-generator/company';
import {
  BusinessCasePermission,
  CustomerType,
  FinancingStructureType,
  ParticipationType,
} from '@fincloud/types/enums';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinModalService } from '@fincloud/ui/modal';
import { FinToastService } from '@fincloud/ui/toast';
import { FinSize } from '@fincloud/ui/types';
import { cloneDeep } from 'lodash-es';
import { ParticipantType } from '../../enums/participant-type';
import { BusinessCaseParticipantsAccessRightsModalComponent } from '../business-case-participants-access-rights-modal/business-case-participants-access-rights-modal.component';
import { ParticipantDataChangeConfirmationModalComponent } from '../participant-data-change-confirmation-modal/participant-data-change-confirmation-modal.component';

@Component({
  selector: 'ui-business-case-roles',
  templateUrl: './business-case-roles.component.html',
  styleUrls: ['./business-case-roles.component.scss'],
})
export class BusinessCaseRolesComponent implements OnInit, OnChanges {
  private readonly participationAmountContext = {
    title: $localize`:@@dashboard.collaboration.myPartners.roles.changeAmountModal.changeParticpantAmountText:Beteiligungsbetrag ändern`,
    label: $localize`:@@dashboard.collaboration.myPartners.roles.changeAmountModal.participantAmountText:Betrag`,
    message: $localize`:@@dashboard.collaboration.myPartners.roles.changeAmountModal.infoText:Der aktualisierte Beteiligungsbetrag wird auf alle Plattformseiten angezeigt wo zutreffend.`,
  };
  @Input() participantPermissionSet: ParticipantCasePermissionSetEntity;
  @Input() readOnly = false;
  @Input() isCustomerGuest = false;
  @Input() customerType: CustomerType;
  @Input() participantName: string;
  @Input() totalParticipationAmount: number;
  @Input() isCaseRealEstate: boolean;

  //Duplicate case
  @Input() userParticipantAmount: number;
  @Input() readOnlyAndHidden = false;
  @Input() isDuplicateCase = false;
  @Input() selectedBusinessCaseType: 'FINANCING_CASE';

  @Output() updateAmount = new EventEmitter<{
    value: number;
    customerKey: string;
  }>();
  @Output() setParticipantType = new EventEmitter();
  @Output() updateParticipationAmount = new EventEmitter();

  isCurrentCustomerLead$ = this.store.select(selectIsParticipationTypeLeader);

  actionsAllowedCustomerTypes = [CustomerType.BANK, CustomerType.FSP];
  participationAmountAllowedCustomerTypes = [
    CustomerType.BANK,
    CustomerType.FSP,
    CustomerType.CORPORATE,
  ];
  businessCase: ExchangeBusinessCase;
  isLoggedInCustomerStructurer: boolean;

  manage = $localize`:@@dashboard.collaboration.myPartners.roles.invitationManagement.text.permission.administer:Verwalten`;
  preview = $localize`:@@dashboard.collaboration.myPartners.roles.invitationManagement.text.permission.preview:Vorschau`;
  firstChange = true;

  readonly finSize = FinSize;
  readonly finButtonAppearance = FinButtonAppearance;
  readonly businessCasePermission = BusinessCasePermission;
  readonly customerTypes = CustomerType;

  get hasParticipationAmount() {
    return (
      this.businessCase.participants.find(
        (p) => p.customerKey === this.participantPermissionSet?.customerKey,
      )?.totalParticipationAmount > 0
    );
  }

  get isCaseMiscellaneousOrRealEstate() {
    return (
      this.businessCase?.structuredFinancingConfiguration
        ?.financingStructureType === FinancingStructureType.REAL_ESTATE ||
      this.businessCase?.structuredFinancingConfiguration
        ?.financingStructureType === FinancingStructureType.MISCELLANEOUS
    );
  }

  get isLeadCustomer() {
    return (
      this.participantPermissionSet?.participationType ===
      ParticipationType.LEADER
    );
  }

  get permissionHtml(): typeof BusinessCasePermission {
    return BusinessCasePermission;
  }

  financingShare = new FormControl(false);

  permissions$: Observable<string[]>;

  canEditFinancingDetails$ = this.store.select(
    selectHasAnyBusinessCasePermission(['BCP-00065']),
  );
  participationAmountControl = new FormControl();
  participantType = new FormControl();
  currencyMaskConfig = CURRENCY_MASK_CONFIG[this.regionalSettings.locale];

  isBusinessCaseActive$ = this.store.select(selectIsBusinessCaseActive);

  isPlatformManageStillNotPartOfCase$ = this.store.select(
    selectCanPlatformManagerAddHimself,
  );

  disabledForInternal = false;
  copyParticipantAs = [
    {
      value: ParticipantType.PARTICIPANT,
      name: $localize`:@@businessCase.participantAccess.copyAs.caseParticipant:Teilnehmer`,
    },
    {
      value: ParticipantType.INVITEE,
      name: $localize`:@@businessCase.participantAccess.copyAs.invitee:Eingeladener`,
    },
  ];

  // TODO: Either move component or make it dummy
  constructor(
    private destroyRef: DestroyRef,
    private store: Store,
    private modalService: ModalService,
    private businessCaseService: BusinessCaseControllerService,
    private finToastService: FinToastService,
    private casePermissionService: CasePermissionControllerService,
    private regionalSettings: RegionalSettingsService,
    private finModalService: FinModalService,
  ) {}

  ngOnInit(): void {
    this.store
      .select(selectCustomerBusinessCaseContext)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((customerBusinessCaseContext) => {
        this.isLoggedInCustomerStructurer =
          customerBusinessCaseContext?.participationType ===
          ParticipationType.STRUCTURER;

        this.disabledForInternal =
          !customerBusinessCaseContext?.permissions?.includes(
            BusinessCasePermission.BCP_00131,
          ) && this.isLoggedInCustomerStructurer;
      });

    this.store
      .select(selectBusinessCase)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        filter((c) => !!c),
      )
      .subscribe((businessCase) => {
        this.businessCase = businessCase;
      });

    this.setInitialParticipationAmount(this.userParticipantAmount ?? 0);
    this.participantType.setValue(ParticipantType.PARTICIPANT);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (
      changes.selectedBusinessCaseType &&
      !changes.selectedBusinessCaseType.firstChange &&
      this.participantPermissionSet?.customerKey
    ) {
      this.participantType.setValue(ParticipantType.INVITEE);
      this.updateParticipantType(ParticipantType.INVITEE);
    }

    if (this.participantPermissionSet?.permissions) {
      this.permissions$ = of(this.participantPermissionSet?.permissions).pipe(
        tap((permissions) => {
          if (this.firstChange && this.hasParticipationAmount) {
            const result =
              this.customerType === 'BANK'
                ? permissions.includes(BusinessCasePermission.BCP_00063)
                : permissions.includes(BusinessCasePermission.BCP_00140);
            this.financingShare.setValue(result);
            this.firstChange = false;
          }
        }),
      );
    }
  }

  triggerBusinessCaseParticipantsAccessRightsModal() {
    this.manageParticipantAccessRights();
  }

  openConfirmationModal(toggleState: boolean, isLeadCustomer?: boolean) {
    if (isLeadCustomer) {
      if (!toggleState) {
        this.toggleFinancingShareAndRequestPermission(toggleState);
      } else {
        this.finModalService
          .open(ConfirmationModalComponent, {
            data: {
              title: $localize`:@@businessCase.participationWithdraw.modal.title:Die Finanzierungsbeteiligung wurde bereits gesetzt.`,
              message: $localize`:@@businessCase.participationWithdraw.modal.title2:Sind Sie sich sicher, dass Sie diese zurückziehen und vom gesammelten
      Betrag abziehen möchten?`,
              confirmButtonAppearance: FinButtonAppearance.PRIMARY,
              confirmLabel: $localize`:@@businessCase.dataRoom.participantRequest.withdrawLabelButtonTitle:Zurückziehen`,
              cancelLabel: $localize`:@@button.label.cancel:Abbrechen`,
              svgIcon: 'svgWithdrawParticipationIcons',
              size: FinSize.L,
            },
            disableClose: true,
            size: FinSize.S,
          })
          .afterClosed()
          .pipe(filter((result) => !!result))
          .subscribe(() =>
            this.toggleFinancingShareAndRequestPermission(toggleState),
          );
      }
    } else {
      this.requestBankBusinessCasePermission(toggleState, false);
    }
  }

  toggleFinancingShareAndRequestPermission(toggleState: boolean): void {
    this.financingShare.setValue(!toggleState);
    this.requestBankBusinessCasePermission(!toggleState, true);
  }

  requestBankBusinessCasePermission(
    toggleState: boolean,
    isLeadCustomer: boolean,
  ): void {
    const permissionCode =
      this.customerType === CustomerType.BANK
        ? BusinessCasePermission.BCP_00063
        : BusinessCasePermission.BCP_00140;

    this.getPermissionRequest(
      permissionCode,
      toggleState,
      isLeadCustomer,
    ).subscribe();
  }

  switchPermissionState(toggleState: boolean, permissionCode?: string) {
    this.getPermissionRequest(permissionCode, toggleState).subscribe();
  }

  updateParticipantAmount(value: number | null) {
    this.updateAmount.emit({
      value,
      customerKey: this.participantPermissionSet.customerKey,
    });
  }

  updateParticipantType(participantType: string) {
    this.setParticipantType.emit({
      customerKey: this.participantPermissionSet.customerKey,
      isInvitee: participantType === ParticipantType.INVITEE,
    });
  }

  manageParticipantAccessRights() {
    const hasDataRoomEditRights = !!this.getHasParticipantPermission(
      BusinessCasePermission.BCP_00016,
    );

    this.finModalService
      .open<
        {
          groupsVisibility: (Group | CadrGroup)[];
          hasDataRoomEditRights: boolean;
          customerKeyEdited: string;
        },
        BusinessCaseParticipantsAccessRightsModalComponent
      >(BusinessCaseParticipantsAccessRightsModalComponent, {
        data: {
          participantName: this.participantName,
          hasDataRoomEditRights: hasDataRoomEditRights,
          groups:
            this.businessCase?.businessCaseTemplate?.template?.groupsOrdered,
          participantCustomerKey: this.participantPermissionSet.customerKey,
          readOnlyAndHidden: this.readOnlyAndHidden,
        },
        size: FinSize.M,
        disableClose: true,
      })
      .afterClosed()
      .pipe(
        filter((result) => !!result),
        switchMap((customerEdited) => {
          const dataRoomPermissionRequest = this.getPermissionRequest(
            BusinessCasePermission.BCP_00016,
            customerEdited.hasDataRoomEditRights,
            false,
            true,
          );

          return dataRoomPermissionRequest.pipe(
            switchMap(() => {
              return forkJoin([
                this.getPermissionRequest(
                  BusinessCasePermission.BCP_00021,
                  customerEdited.hasDataRoomEditRights,
                  false,
                  true,
                ),
                this.getPermissionRequest(
                  BusinessCasePermission.BCP_00022,
                  customerEdited.hasDataRoomEditRights,
                  false,
                  true,
                ),
              ]);
            }),
            switchMap(() => {
              return this.businessCaseService.addFieldAndEditBusinessCaseGroups(
                {
                  businessCaseId: this.businessCase.id,
                  body: {
                    groupsOrdered: customerEdited.groupsVisibility,
                  },
                },
              );
            }),
            tap({
              next: (businessCase) => {
                this.store.dispatch(
                  StateLibBusinessCasePageActions.setBusinessCaseGroups({
                    payload: (businessCase as BusinessCase)
                      ?.businessCaseTemplate.template.groupsOrdered,
                  }),
                );
                this.finToastService.show(Toast.success());
              },
              error: () => this.finToastService.show(Toast.error()),
            }),
          );
        }),
      )
      .subscribe();
  }

  overrideParticipationAmount(value: number): void {
    this.finModalService
      .open<
        { amount: number },
        ParticipantDataChangeConfirmationModalComponent
      >(ParticipantDataChangeConfirmationModalComponent, {
        data: {
          amount: value ?? 0,
          ...this.participationAmountContext,
          validators: [
            Validators.min(this.businessCase.minParticipationAmount || 0),
            Validators.max(this.businessCase.maxParticipationAmount),
          ],
        },
        size: FinSize.S,
        disableClose: true,
      })
      .afterClosed()
      .pipe(filter(Boolean))
      .subscribe((resp) => this.updateParticipationAmount.emit(resp.amount));
  }

  addUserToGuestCustomer() {
    this.store.dispatch(
      StateLibMyPartnersPageActions.addUserToGuestCustomerFromMyPartnersPage({
        payload: {
          customerKey: this.participantPermissionSet.customerKey,
        },
      }),
    );
  }

  private getHasParticipantPermission(permission: BusinessCasePermission) {
    return this.participantPermissionSet?.permissions?.includes(permission);
  }

  private getPermissionRequestParams(permissionCode: string) {
    return {
      businessCaseId: this.businessCase?.id,
      permissionSetId: this.participantPermissionSet.id,
      permissionCode,
    };
  }

  private getPermissionRequest(
    permissionCode: string,
    isAdd: boolean,
    isLeadCustomer?: boolean,
    skipToastMessage?: boolean,
  ) {
    const params = this.getPermissionRequestParams(permissionCode);
    const request = isAdd
      ? this.casePermissionService.addPermissionToParticipant(params)
      : this.casePermissionService.deletePermissionFromParticipant(params);
    return request.pipe(
      tap((permissionsSetEntity) => {
        // this is stupid
        this.participantPermissionSet = cloneDeep(permissionsSetEntity);
        this.permissions$ = of(permissionsSetEntity.permissions);

        if (isLeadCustomer) {
          this.store.dispatch(
            StateLibBusinessCasePageActions.setParticipantContextEffectTrigger({
              payload: permissionsSetEntity,
            }),
          );
        }
      }),
      tap(() => {
        if (!skipToastMessage) {
          this.finToastService.show(
            Toast.success(
              $localize`:@@businessCase.participantRoles.toast.success:Teilnehmereinstellungen erfolgreich aktualisiert`,
            ),
          );
        }
      }),
      catchError(() => {
        if (!skipToastMessage) {
          this.finToastService.show(
            Toast.error(
              $localize`:@@businessCase.participantRoles.toast.error:Fehler beim Aktualiseren des Teilnehmereinstellungen`,
            ),
          );
        }

        return EMPTY;
      }),
    );
  }

  private setInitialParticipationAmount(amount: number | null) {
    this.participationAmountControl.setValue(amount);
  }
}
