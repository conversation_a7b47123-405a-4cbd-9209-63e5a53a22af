import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { ConfirmationModalComponent } from '@fincloud/components/modals';
import { ModalService } from '@fincloud/core/modal';
import {
  CollaborationActivityLogComponent,
  CollaborationInvitationModalComponent,
  INVITATION_STATUS_NAME,
} from '@fincloud/neoshare/business-case-collaboration';
import { TabNavigationService } from '@fincloud/neoshare/services';
import {
  StateLibInvitationsPageActions,
  selectSectionInvitationData,
} from '@fincloud/state/business-case';
import { StateLibInvitationPageActions } from '@fincloud/state/invitation';
import { Invitation } from '@fincloud/swagger-generator/application';
import { Customer } from '@fincloud/swagger-generator/authorization-server';
import {
  BusinessCasePermission,
  ChatTab,
  CustomerType,
  InvitationFlowStrategy,
  InvitationStatus,
} from '@fincloud/types/enums';
import {
  ApplicationOrInvitationInfo,
  Dictionary,
} from '@fincloud/types/models';
import { FinBadgeStatus } from '@fincloud/ui/badges';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinModalService } from '@fincloud/ui/modal';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { BehaviorSubject, Observable, filter, map, merge, tap } from 'rxjs';
import { ButtonStepsLabel } from '../../enums/button-steps-label';
import { SectionSelectionData } from '../../models/section-selection-data';
import { BODY_HINT_MESSAGE } from '../../utils/body-hint-message';
import { HEADER_HINT_MESSAGE } from '../../utils/header-hint-message';

@Component({
  selector: 'app-section-invitation',
  templateUrl: './section-invitation.component.html',
  styleUrls: ['./section-invitation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SectionInvitationComponent implements OnInit {
  // private acceptOrRejectInvitation$$ = new Subject<boolean>();
  private shouldDisableSubmitButton$$ = new BehaviorSubject<boolean>(false);

  badgeStatus = FinBadgeStatus;
  invitationStatusName = INVITATION_STATUS_NAME;
  finSize = FinSize;
  finButtonAppereance = FinButtonAppearance;
  customerType = CustomerType;
  headerHintMessage = HEADER_HINT_MESSAGE;
  bodyHintMessage = BODY_HINT_MESSAGE;
  invitationStatus = InvitationStatus;
  businessCasePermission = BusinessCasePermission;

  canAddAmount = false;
  hintIsShown = true;
  actionPending = false;
  participationAmountControl = new UntypedFormControl(0);

  invitedPersonMessageFinancingCase = $localize`:@@invite.person.message:Wenn Sie die Annahme oder Ablehnung von Einladungen zur Teilnahme an einem Projekt umleiten möchten, können Sie eine Person aus Ihrer Organisation einladen, die das Projekt verwalten kann.`;

  stepBackLabel = $localize`:@@contract.create.modal.backButton:Zurück`;
  declineInviteToJoinLabel = $localize`:@@businessCase.dashboard.button.label.reject:Ablehnen`;

  shouldDisableSubmitButton$: Observable<boolean> = merge(
    this.participationAmountControl.valueChanges.pipe(map((amount) => !amount)),
    this.shouldDisableSubmitButton$$.asObservable(),
  );
  getSectionInvitationData$: Observable<SectionSelectionData> = this.store
    .select(selectSectionInvitationData)
    .pipe(
      tap((data) => {
        this.setButtonNextStepLabel(data);
        this.setButtonBackStepLabel(data.invitation);
      }),
    );

  private _nextStepButton = ButtonStepsLabel.INITIAL_STEP;
  private _backStepButton: ButtonStepsLabel;

  constructor(
    private chatNavService: TabNavigationService,
    private modalService: ModalService,
    private store: Store,
    private finModalService: FinModalService,
  ) {}

  ngOnInit(): void {
    this.store.dispatch(
      StateLibInvitationsPageActions.startInvitationPolling({
        payload: true,
      }),
    );
  }

  getButtonNextStepLabel(
    data: Pick<
      SectionSelectionData,
      'invitation' | 'customer' | 'businessCase'
    >,
    canAddAmount: boolean,
  ): string {
    if (
      data.customer.customerType === CustomerType.BANK &&
      data.invitation?.invitationStatus === InvitationStatus.PENDING &&
      !canAddAmount &&
      data.invitation?.invitationType ===
        InvitationFlowStrategy.INVITE_TO_APPLY_STRATEGY
    ) {
      return $localize`:@@sectionApplication.submitApplication:Bewerbung abschicken`;
    }

    if (
      data.invitation?.invitationType ===
      InvitationFlowStrategy.INVITE_TO_JOIN_STRATEGY
    ) {
      return $localize`:@@businessCase.dashboard.button.label.assume:Annehmen`;
    }

    return $localize`:@@sectionApplication.submitApplication:Bewerbung abschicken`;
  }
  public openChat(
    invitation: Invitation,
    userCustomerKey: string,
    businessCaseId: string,
  ) {
    const chatId = invitation.chatId;
    this.chatNavService.addTabFragmentToURL(
      userCustomerKey,
      businessCaseId,
      ChatTab.BILATERAL,
      chatId,
    );
  }

  public openActivityLogModal(
    invitation: Invitation,
    customers: Dictionary<Customer>,
  ) {
    this.finModalService.open(CollaborationActivityLogComponent, {
      data: {
        businessCaseId: invitation.businessCaseId,
        customerKey: invitation.invitedCustomerKey,
        customers,
      },
      size: this.finSize.L,
    });
  }

  public openInvitationModal(
    data: Pick<
      SectionSelectionData,
      'invitation' | 'customer' | 'businessCase' | 'customerNamesByKey'
    >,
  ) {
    this.store.dispatch(
      StateLibInvitationsPageActions.clearAcceptedInvitations(),
    );
    const currentCustomer = {
      ...data.invitation,
      customerName:
        data.customerNamesByKey[data.invitation.invitedCustomerKey].name,
      customerKey: data.invitation.invitedCustomerKey,
      customerStatus: data.customer.customerStatus,
    };

    this.finModalService.open(CollaborationInvitationModalComponent, {
      data: {
        applicationOrInvitationInfo:
          currentCustomer as ApplicationOrInvitationInfo,
        businessCase: data.businessCase,
        alreadyAddedOrganisation: [],
        isOpenViaKebabMenu: true,
        isOpenViaInvitedOrganization: true,
      },
      size: FinSize.M,
      disableClose: true,
    });
  }

  public confirmAction(actionType: 'accept' | 'reject', showModal = true) {
    if (!showModal) {
      this.store.dispatch(StateLibInvitationPageActions.acceptInvitation());
      this.hintIsShown = false;
      // this.acceptOrRejectInvitation$$.next(true);
      return;
    }

    const confirmMessage =
      actionType === 'accept'
        ? $localize`:@@confirm.accept.invitation.message:Sind Sie sicher, dass Sie diese Einladung annehmen wollen?`
        : $localize`:@@confirm.reject.invitation.message:Sind Sie sicher, dass Sie diese Einladung ablehnen wollen?`;

    const svgIcon =
      actionType === 'accept'
        ? 'svgCorporateAcceptGroup'
        : 'svgCorporateCancelGroup';

    this.finModalService
      .open(ConfirmationModalComponent, {
        data: {
          title: confirmMessage,
          confirmButtonAppearance: FinButtonAppearance.PRIMARY,
          confirmLabel: $localize`:@@button.label.confirm:Ja, Ich bin sicher`,
          cancelLabel: $localize`:@@detachMasterCase.dialog.label.no:Nein`,
          svgIcon,
          size: FinSize.L,
        },
        size: this.finSize.S,
      })
      .afterClosed()
      .pipe(
        filter(Boolean),
        map(() => {
          if (actionType === 'accept') {
            this.store.dispatch(
              StateLibInvitationPageActions.acceptInvitation(),
            );
          } else {
            this.store.dispatch(
              StateLibInvitationPageActions.declineInvitation(),
            );
          }
          this.hintIsShown = false;
        }),
      )
      .subscribe();
  }

  public setButtonAction() {
    this.store.dispatch(
      StateLibInvitationsPageActions.clearAcceptedInvitations(),
    );
    if (this.actionPending || this.shouldDisableSubmitButton$$.value) {
      return;
    }
    switch (this._nextStepButton) {
      case ButtonStepsLabel.APPLY_STEP:
        this.shouldDisableSubmitButton$$.next(true);
        this.confirmAction('accept', false);
        break;
      case ButtonStepsLabel.SUBMIT_STEP:
        this.store.dispatch(StateLibInvitationPageActions.acceptInvitation());
        this.hintIsShown = false;
        // this.acceptOrRejectInvitation$$.next(true);
        break;
      case ButtonStepsLabel.ACCEPT_INVITE_TO_JOIN:
        this.confirmAction('accept');
        break;
    }
  }

  public setBackStepButtonAction() {
    this.store.dispatch(
      StateLibInvitationsPageActions.clearAcceptedInvitations(),
    );
    this.shouldDisableSubmitButton$$.next(false);
    switch (this._backStepButton) {
      case ButtonStepsLabel.GO_STEP_BACK:
        this._nextStepButton = ButtonStepsLabel.INITIAL_STEP;
        this.canAddAmount = !this.canAddAmount;
        this.hintIsShown = true;
        break;
      case ButtonStepsLabel.DECLINE_INVITE_TO_JOIN:
        this.confirmAction('reject');
        break;
    }
  }

  private setButtonNextStepLabel(
    data: Pick<SectionSelectionData, 'invitation' | 'customer'>,
  ): void {
    if (
      data.customer.customerType === CustomerType.BANK &&
      data.invitation?.invitationStatus === InvitationStatus.PENDING &&
      !this.canAddAmount &&
      data.invitation?.invitationType ===
        InvitationFlowStrategy.INVITE_TO_APPLY_STRATEGY
    ) {
      this._nextStepButton = ButtonStepsLabel.APPLY_STEP;
    }
    if (
      data.invitation?.invitationType ===
      InvitationFlowStrategy.INVITE_TO_JOIN_STRATEGY
    ) {
      this._nextStepButton = ButtonStepsLabel.ACCEPT_INVITE_TO_JOIN;
    }
  }

  private setButtonBackStepLabel(invitation: Invitation): void {
    if (
      invitation?.invitationStatus === InvitationStatus.PENDING &&
      this.canAddAmount
    ) {
      this._backStepButton = ButtonStepsLabel.GO_STEP_BACK;
      return;
    }
    if (invitation?.invitationStatus === InvitationStatus.PENDING) {
      this._backStepButton = ButtonStepsLabel.DECLINE_INVITE_TO_JOIN;
    }
  }
}
