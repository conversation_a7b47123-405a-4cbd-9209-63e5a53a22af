@for (entity of messageSources.fields; track entity.key) {
  <ng-container
    *ngTemplateOutlet="
      entity.key === chunkType.FinStructureField ? financingDetails : dataRoom;
      context: { $implicit: entity.values }
    "
  ></ng-container>
}

<ng-template #dataRoom let-fields>
  @if (messageSources.documents?.length || fields?.length) {
    <section class="w-100">
      <header
        class="source-title tw-text-color-text-primary tw-my-6"
        i18n="@@dashboard.businessCase.dataRoomTab"
      >
        Data Room
      </header>

      @if (messageSources.documents?.length) {
        <header class="mb-2" i18n="@@neogpt.chat.message.documents.label">
          Dokumente:
        </header>
        <ul class="list-unstyled mb-0">
          @for (
            document of messageSources.documents;
            track document.id;
            let isFirst = $first;
            let isLast = $last
          ) {
            @if (isFirst || areDocumentsVisible) {
              <li
                class="source d-flex mb-3"
                [ngClass]="{ 'pe-none': !document.label }"
              >
                <div
                  [ngClass]="{
                    'pt-3':
                      document.label &&
                      (showPagesFileTypes | includes: document.fileExt),
                  }"
                >
                  @if (imageFilesTypes | includes: document.fileExt) {
                    <ui-icon
                      name="image"
                      size="medium-large"
                      color="gray"
                      class="d-block me-2"
                    ></ui-icon>
                  }
                  @if (goToPageFileTypes | includes: document.fileExt) {
                    <ui-icon
                      class="d-block me-3"
                      name="document"
                      size="medium"
                      color="gray"
                    ></ui-icon>
                  }
                  @if (textFileTypes | includes: document.fileExt) {
                    <ui-icon
                      class="d-block me-2"
                      name="text_fields"
                      size="medium-large"
                      color="gray"
                    ></ui-icon>
                  }
                </div>
                <div class="flex-fill">
                  @if (document.label) {
                    <button
                      [disabled]="
                        goToPageFileTypes | includes: document.fileExt
                      "
                      [attr.data-field-id]="hightLightPrefix + document.id"
                      (click)="
                        goToSource({
                          id: document.id,
                          session: neoGptActiveSession.DATA_ROOM,
                          inbox: document.inbox,
                        })
                      "
                      class="source-btn fw-bold btn-clean w-100 text-start mb-1 text-truncated"
                    >
                      {{ document?.label }}
                    </button>
                  } @else {
                    <div
                      class="fw-bold btn-clean w-100 text-start mb-1 text-truncated"
                    >
                      {{ document?.meta?.filename }}
                    </div>
                    <div
                      class="text-color-gray"
                      i18n="@@neogpt.chat.message.document.unavailable"
                    >
                      Dokument nicht verfügbar
                    </div>
                  }
                  <!-- Pages -->
                  @if (
                    document.pages.length &&
                    document.label &&
                    (showPagesFileTypes || [] | includes: document.fileExt)
                  ) {
                    <button
                      type="button"
                      class="btn-clean mt-1"
                      (click)="showSources(document.id)"
                    >
                      <ui-icon
                        name="svgChevronRight"
                        [size]="'extra-small'"
                        class="d-inline-block me-1"
                        [ngClass]="{
                          'rotate-down': areSourcesVisible[document.id],
                        }"
                      ></ui-icon>
                      {{ document.pages.length }} Referenzen
                    </button>
                    @if (areSourcesVisible[document.id]) {
                      <hr class="mt-2" />
                      Seiten:
                      @for (
                        page of document.pages;
                        track page;
                        let isLast = $last
                      ) {
                        <button
                          type="button"
                          class="btn-clean source-btn ms-1"
                          (click)="goToPdfPage(document, page)"
                          [disabled]="
                            !(goToPageFileTypes | includes: document.fileExt)
                          "
                        >
                          {{ page }}
                        </button>
                        @if (!isLast && document.pages.length > 1) {
                          ,
                        }
                      }
                    }
                  }
                </div>
                @if (
                  !(goToPageFileTypes | includes: document.fileExt) &&
                  document.label
                ) {
                  <div class="ps-2">
                    <button
                      type="button"
                      class="btn-clean"
                      (click)="downloadDocument(document.id)"
                    >
                      <ui-icon
                        size="medium"
                        color="dark"
                        name="download"
                      ></ui-icon>
                    </button>
                  </div>
                }
              </li>
            }
          }
        </ul>
        @if (messageSources.documents?.length > 1) {
          <button
            type="button"
            (click)="toggleDocumentsVisibility()"
            class="btn-clean w-100 text-color-gray"
          >
            @if (areDocumentsVisible) {
              <ng-container i18n="@@neogpt.chat.message.sources.showLess"
                >Weniger anzeigen</ng-container
              >
            } @else {
              <ng-container i18n="@@neogpt.chat.message.sources.showMore"
                >Mehr anzeigen</ng-container
              >
            }
          </button>
        }
      }

      @if (fields.length) {
        <ns-neogpt-chat-source-section
          [fields]="fields"
          [session]="neoGptActiveSession.DATA_ROOM"
          (goToFieldSource)="goToSource($event)"
        ></ns-neogpt-chat-source-section>
      }
    </section>
  }
</ng-template>

<ng-template #financingDetails let-fields>
  @if (fields?.length) {
    <section class="w-100">
      <header
        class="source-title tw-text-color-text-primary tw-my-6"
        i18n="
          @@dashboard.businessCase.financing.details.nav.financing.structure"
      >
        Finanzierungsstruktur
      </header>
      <ns-neogpt-chat-source-section
        [fields]="fields"
        [session]="neoGptActiveSession.FINANCING_DETAILS"
        (goToFieldSource)="goToSource($event)"
      ></ns-neogpt-chat-source-section>
    </section>
  }
</ng-template>
