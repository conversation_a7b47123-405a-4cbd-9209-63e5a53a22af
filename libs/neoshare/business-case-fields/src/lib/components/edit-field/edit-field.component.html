<div [class.edit-field-wrapper-document]="isfieldTypeDocument">
  @if (fieldType === fieldTypeEnum.SELECT) {
    <fin-warning-message
      [label]="dropdownWarningMessage"
      [showIcon]="true"
      [appearance]="finWarningMessageAppearance.INFORMATIVE"
    ></fin-warning-message>
  }

  <div class="tw-flex tw-justify-between">
    @if (isfieldTypeDocument) {
      <div [formGroup]="editFieldForm">
        <app-document-field-upload
          class="tw-border-r tw-border-colour-border-tertiary-subtle"
          [documentId]="businessCaseInformation?.value?.toString()"
          [dataRoomOwnerId]="businessCase.id"
          [fileName]="
            businessCaseInformation?.value &&
            businessCaseInformation?.field.label.toLocaleLowerCase()
          "
          [fileUploadEvent]="fileUploadEvent"
          [initialFile]="initialFile"
          formControlName="documentId"
          (fileUploading)="onFileUploading($event)"
          (fileDeleting)="onFileDeleting($event)"
        ></app-document-field-upload>
      </div>
    }
    <fin-scrollbar
      class="tw-w-full"
      [ngClass]="{
        'tw-max-h-[32.2rem]': fieldType !== fieldTypeEnum.DOCUMENT,
        'tw-max-h-[38.8rem]': fieldType === fieldTypeEnum.DOCUMENT,
        'tw-max-h-[51.8rem]': fieldType === fieldTypeEnum.SELECT,
      }"
    >
      <app-document-field
        [documentCategoryOptions]="documentCategoryOptions$ | async"
        [fieldType]="fieldType"
        [information]="businessCaseInformation"
        [documentId]="documentId"
        [formGroup]="editFieldForm"
        [fileDeleting]="fileDeleting"
        [fileUploading]="fileUploading"
        [isMirrored]="isMirrored"
        [newField]="newField"
      ></app-document-field>
      @if (isFieldTypeSelect) {
        <ui-dropdown-options-configuration
          [ngClass]="{ 'tw-grow': newField, 'tw-w-[50%]': !newField }"
          (addedOption)="scrollToBottom()"
          [(ngModel)]="fieldMetadata"
        ></ui-dropdown-options-configuration>
      }
      @if (showConfirmDeleteChatMessage) {
        <div
          class="existing-chat-warning"
          i18n="@@editField.existingChatWarning.text"
        >
          Zu dieser Information gibt es eine laufende Diskussion. Wenn Sie das
          Feld löschen, wird die Diskussion archiviert.
        </div>
      }
    </fin-scrollbar>
  </div>

  <fin-modal-footer separator>
    <button
      fin-button
      fin-modal-close
      [appearance]="finButtonAppearance.SECONDARY"
    >
      <span i18n="@@button.label.cancel">Abbrechen</span>
    </button>

    <button
      [disabled]="fileUploading"
      fin-button
      [appearance]="finButtonAppearance.PRIMARY"
      (click)="save()"
    >
      <span i18n="@@button.label.save">Speichern</span>
    </button>
  </fin-modal-footer>
</div>
