import { HttpErrorResponse } from '@angular/common/http';
import {
  ChangeDetectorRef,
  Component,
  DestroyRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import {
  BusinessCaseControllerService,
  FieldDto,
  FieldEditRequestBody,
  Information,
} from '@fincloud/swagger-generator/business-case-manager';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractValueAccessor,
  doesNotEndWith,
  FORBIDDEN_CHARACTERS_REGEX,
  makeControlValidatorProvider,
  makeControlValueAccessorProvider,
} from '@fincloud/core/form';
import { FieldTypeEnum } from '@fincloud/core/formly';
import { IdentityService } from '@fincloud/core/services';
import { Toast } from '@fincloud/core/toast';
import { BusinessCaseDataRoomHelperService } from '@fincloud/neoshare/data-room';
import { selectAccessRights } from '@fincloud/state/access';
import {
  selectDocumentFieldCategoryOptions,
  selectGlobalRequiredFieldKeys,
} from '@fincloud/state/business-case';
import { StateLibChatPageActions } from '@fincloud/state/chat';
import { ChatManagementControllerService } from '@fincloud/swagger-generator/communication';
import {
  BusinessCaseGroup,
  BusinessCaseInformation,
  ExchangeBusinessCase,
} from '@fincloud/swagger-generator/exchange';
import { AccessRights } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
// import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { StateLibFolderStructureDocumentPageActions } from '@fincloud/state/folder-structure';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FIN_MODAL_REF_PROVIDER } from '@fincloud/ui/modal';
import { FinWarningMessageAppearance } from '@fincloud/ui/warning-message';
import { Store } from '@ngrx/store';
import { cloneDeep, isEqual } from 'lodash-es';
import { NgScrollbar } from 'ngx-scrollbar';
import { EMPTY, forkJoin, Observable, switchMap, take } from 'rxjs';
import { catchError, finalize, tap } from 'rxjs/operators';
import { documentNameUniqueValidator } from '../../validators/document-name-unique.validator';

@Component({
  selector: 'ui-edit-field',
  templateUrl: './edit-field.component.html',
  styleUrls: ['./edit-field.component.scss'],
  providers: [
    makeControlValueAccessorProvider(EditFieldComponent),
    makeControlValidatorProvider(EditFieldComponent),
    FIN_MODAL_REF_PROVIDER,
  ],
})
export class EditFieldComponent
  extends AbstractValueAccessor<string>
  implements OnInit
{
  @Input() businessCaseInformation?: BusinessCaseInformation;
  @Input() fieldDto: FieldDto;
  @Input() orderedGroups: BusinessCaseGroup[];
  @Input() fieldGroup: BusinessCaseGroup;
  @Input() businessCase: ExchangeBusinessCase;
  @Input() fileUploadEvent: DragEvent;
  @Input() existingChatId: string;
  @Input() isMirrored: boolean;
  @Input() initialFile: FileList;

  @Output() closeModal: EventEmitter<void> = new EventEmitter();

  @ViewChild(NgScrollbar)
  scrollbarRef: NgScrollbar;

  readonly finButtonAppearance = FinButtonAppearance;
  readonly finWarningMessageAppearance = FinWarningMessageAppearance;
  readonly fieldTypeEnum = FieldTypeEnum;
  readonly dropdownWarningMessage = $localize`:@@templateField.dropdownComponent.warningMessage:Bitte setzen Sie mindestens 2 Dropdown-Optionen`;
  readonly documentNameForbiddenEnd = '.';

  categoryId: string;
  fieldLabel: string;
  documentId: string;
  description: string;
  editFieldForm: UntypedFormGroup;
  fieldMetadata: unknown;
  fieldType: string;
  isGlobalRequiredField: boolean;
  fileUploading = false;
  fileDeleting = false;
  access: AccessRights;
  fileName: string;
  showConfirmDeleteChatMessage: boolean;
  isfieldTypeDocument: boolean;
  isFieldTypeSelect: boolean;
  newField: boolean;

  private isDocumentField: boolean;

  documentCategoryOptions$ = this.store.select(
    selectDocumentFieldCategoryOptions,
  );

  constructor(
    changeDetectorRef: ChangeDetectorRef,
    private destroyRef: DestroyRef,
    private formBuilder: UntypedFormBuilder,
    private businessCaseControllerService: BusinessCaseControllerService,
    private finToastService: FinToastService,
    private businessCaseHelperService: BusinessCaseDataRoomHelperService,
    private store: Store,
    private chatManagementControllerService: ChatManagementControllerService,
  ) {
    super(changeDetectorRef);
  }

  get isExistingTopicChat() {
    return !!this.existingChatId;
  }

  ngOnInit(): void {
    this.store
      .select(selectAccessRights)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((access: AccessRights) => (this.access = access));
    this.store
      .select(selectGlobalRequiredFieldKeys)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((globalRequiredFieldsKeys) => {
        const fieldKey =
          this.businessCaseInformation?.field?.key || this.fieldDto?.key;
        this.isGlobalRequiredField =
          globalRequiredFieldsKeys.includes(fieldKey);
      });

    this.fieldType = this.fieldDto?.fieldType;

    this.fieldLabel = this.fieldDto?.label || '';
    this.isfieldTypeDocument = this.fieldType === this.fieldTypeEnum.DOCUMENT;
    this.isFieldTypeSelect = this.fieldType === this.fieldTypeEnum.SELECT;

    const labelValidators = [Validators.required];
    const asyncValidators = [];

    if (this.isfieldTypeDocument) {
      labelValidators.push(
        Validators.pattern(FORBIDDEN_CHARACTERS_REGEX),
        doesNotEndWith(this.documentNameForbiddenEnd),
      );

      asyncValidators.push(
        documentNameUniqueValidator(
          this.store,
          this.fieldGroup.key,
          this.fieldLabel,
        ),
      );
    }

    this.editFieldForm = this.formBuilder.group({
      label: [this.fieldLabel, labelValidators, asyncValidators],
      description: this.fieldDto?.description || '',
      categoryId: this.fieldDto?.categoryId || null,
    });

    if (this.isFieldTypeSelect) {
      this.fieldMetadata = this.fieldDto?.fieldMetaData as [];
    }
    if (this.isfieldTypeDocument) {
      this.editFieldForm.addControl(
        'documentId',
        new UntypedFormControl(this.businessCaseInformation?.value),
      );
    }

    this.editFieldForm.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(
        ({
          label,
          description,
          documentId,
          categoryId,
        }: {
          label: string;
          description: string;
          documentId: string;
          categoryId: string;
        }) => {
          this.fieldLabel = label;
          this.documentId = documentId;
          this.description = description;
          this.categoryId = categoryId;
        },
      );

    this.newField = !this.fieldDto?.key;

    this.isDocumentField =
      this.fieldDto.fieldType === this.fieldTypeEnum.DOCUMENT;
  }

  scrollToBottom() {
    setTimeout(() => {
      void this.scrollbarRef?.scrollTo({ bottom: 0, duration: 500 });
    });
  }

  save() {
    if (this.editFieldForm.invalid) {
      for (const controlName in this.editFieldForm.controls) {
        const control = this.editFieldForm.get(controlName);

        control.markAsTouched();
        control.markAsDirty();
        control.updateValueAndValidity();
      }

      return;
    }

    this.newField ? this.createField() : this.updateField();
  }

  onFileUploading(fileUploading: { isUploading: boolean; fileName?: string }) {
    this.fileUploading = fileUploading.isUploading;
    this.fileName = fileUploading.fileName;
  }

  onFileDeleting(fileDeleting: boolean) {
    this.fileDeleting = fileDeleting;
  }

  private createField() {
    const fieldKey = IdentityService.generateId();
    const fieldDto = {
      ...this.fieldDto,
      isPublic: false,
      label: this.fieldLabel,
      key: fieldKey,
      fieldMetaData: this.fieldMetadata ?? '',
      description: this.description ?? '',
      categoryId: this.editFieldForm?.getRawValue()?.categoryId || undefined,
    };

    if (this.isDocumentField && this.documentId) {
      fieldDto.value = this.documentId;
    }

    if (this.isDocumentField) {
      this.store.dispatch(
        StateLibFolderStructureDocumentPageActions.addDocument({
          groupKey: this.fieldGroup.key,
          fieldDto,
        }),
      );

      return;
    }

    this.store
      .select(selectAccessRights)
      .pipe(
        take(1),
        switchMap((accessRights) => {
          // update groups structure
          const groupsOrderedModify = cloneDeep(this.orderedGroups);
          groupsOrderedModify
            .find((g: BusinessCaseGroup) => g.key === this.fieldGroup.key)
            .fields.push(fieldKey);
          this.orderedGroups = groupsOrderedModify;

          return this.businessCaseHelperService.editBusinessCaseGroups(
            this.businessCase.id,
            groupsOrderedModify,
            accessRights.granular.isRepresentingLeadPartner,
            fieldDto,
          );
        }),
        tap(() => this.successCloseModal(this.fileName)),
        catchError((response: HttpErrorResponse) => {
          this.finToastService.show(Toast.error(response.error?.message));
          return EMPTY;
        }),
      )
      .subscribe();
  }

  private updateField() {
    const requests: Observable<FieldDto | Information>[] = [];

    if (
      this.isLabelEdited ||
      this.isMetadataEdited ||
      this.isDescriptionEdited ||
      this.isCategoryEdited
    ) {
      const editFieldBaseData =
        this.businessCaseControllerService.editBusinessCaseField({
          fieldKey: this.fieldDto.key,
          businessCaseId: this.businessCase.id,
          body: {
            label: this.fieldLabel,
            fieldMetaData: this.fieldMetadata ?? '',
            description: this.description ?? this.fieldDto?.description,
            categoryId: this.editFieldForm.getRawValue()?.categoryId || null,
          } as FieldEditRequestBody,
        });
      requests.push(editFieldBaseData);
    }

    if (this.isLabelEdited && this.isExistingTopicChat) {
      this.editTopicChatTopicName();
    }

    if (this.isDocumentEdited) {
      const editValueRequest =
        this.businessCaseHelperService.handleEditValueRequest(
          this.businessCaseInformation,
          this.documentId,
          this.businessCase.id,
          false,
        );
      requests.push(editValueRequest);
    }

    if (this.isSelectedOptionRemoved) {
      const editSelectValueRequest =
        this.businessCaseHelperService.handleEditValueRequest(
          this.businessCaseInformation,
          null,
          this.businessCase.id,
          false,
        );

      requests.push(editSelectValueRequest);
    }
    if (requests.length) {
      forkJoin([...requests])
        .pipe(
          finalize(() => {
            this.successCloseModal();
          }),
          catchError((response: HttpErrorResponse) => {
            this.finToastService.show(Toast.error(response.error?.message));
            return EMPTY;
          }),
        )
        .subscribe();
    }
  }

  onConfirmDeleteTriggered() {
    if (this.isExistingTopicChat) {
      this.showConfirmDeleteChatMessage = true;
    }
  }

  private editTopicChatTopicName() {
    this.chatManagementControllerService
      .changeChatTopic({
        newTopic: this.fieldLabel,
        chatId: this.existingChatId,
        businessCaseId: this.businessCase?.id,
      })
      .subscribe({
        next: (chat) => {
          this.store.dispatch(
            StateLibChatPageActions.updateChat({ payload: chat }),
          );
        },
      });
  }

  get isSelectedOptionRemoved() {
    return (
      this.isMetadataEdited &&
      !(this.fieldMetadata as string[]).includes(
        this.businessCaseInformation?.value,
      ) &&
      this.businessCaseInformation?.value
    );
  }

  get isDocumentEdited() {
    return (
      this.isfieldTypeDocument &&
      (this.documentId || this.fileDeleting) &&
      this.documentId != this.businessCaseInformation?.value
    );
  }

  get isLabelEdited() {
    return this.fieldLabel !== this.fieldDto?.label;
  }

  get isDescriptionEdited() {
    return this.description !== this.fieldDto?.description;
  }

  get isCategoryEdited() {
    return this.categoryId !== this.fieldDto?.categoryId;
  }

  get isMetadataEdited() {
    return (
      this.fieldMetadata &&
      !isEqual(this.fieldMetadata, this.fieldDto.fieldMetaData)
    );
  }

  private successCloseModal(message?: string) {
    if (message && message.trim() !== '') {
      const displayMessage = $localize`:@@fileUpload.success:wurde erfolgreich hochgeladen.`;
      this.finToastService.show(Toast.success(`${message} ${displayMessage}`));
    } else {
      // Call show without a message when message is empty
      this.finToastService.show(Toast.success());
    }

    this.closeModal.next();
  }
}
