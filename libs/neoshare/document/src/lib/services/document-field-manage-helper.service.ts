import { ElementRef, Injectable, ViewChild } from '@angular/core';
import {
  DocumentHelperService,
  DocumentPreviewHelperService,
} from '@fincloud/core/document';
import { FileService } from '@fincloud/core/files';
import { retryCall } from '@fincloud/core/rxjs';
import { StateLibDocumentPageActions } from '@fincloud/state/document';
import { Size } from '@fincloud/swagger-generator/document-preview';
import { Store } from '@ngrx/store';
import { catchError, from, of, switchMap, take } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class DocumentFieldManageHelperService {
  @ViewChild('fullscreen', { static: false })
  componentFullscreen: ElementRef<HTMLElement>;

  constructor(
    private documentHelperService: DocumentHelperService,
    private fileService: FileService,
    private documentPreviewHelperService: DocumentPreviewHelperService,
    private store: Store,
  ) {}

  getDocumentThumbnail(documentId: string) {
    this.store.dispatch(
      StateLibDocumentPageActions.getDocumentMimeType({ documentId }),
    );

    return this.documentPreviewHelperService
      .getDocumentPreview(documentId, Size.S)
      .pipe(
        retryCall({ maxRetries: 5, initialInterval: 1000 }),
        take(3),
        switchMap((blob) => {
          return from(this.fileService.blobToDataURL(blob as Blob));
        }),
        catchError(() => of(null)),
      );
  }

  downloadDocument(documentId: string, fieldLabel?: string) {
    this.fileService.downloadDocument(documentId, fieldLabel);
  }

  getHandleFileErrorMsg(file: File, responseErrorMessage: string) {
    let toastErrorMessage = '';

    if (
      this.documentHelperService.getHasVirusStatusCode(responseErrorMessage)
    ) {
      toastErrorMessage =
        this.documentHelperService.getDocumentWithVirusErrorMsg(file.name);
    } else if (
      this.documentHelperService.getHasInvalidTypeStatusCode(
        responseErrorMessage,
      )
    ) {
      toastErrorMessage =
        this.documentHelperService.getDocumentWithVirusErrorMsg(file.name);
    } else if (
      this.documentHelperService.getHasMissingContentTypeStatusCode(
        responseErrorMessage,
      )
    ) {
      toastErrorMessage =
        this.documentHelperService.getDocumentWithVirusErrorMsg(file.name);
    } else {
      toastErrorMessage = $localize`:@@documentField.manageHelper.toast.error:Problem beim Hochladen`;
    }
    return toastErrorMessage;
  }
}
