<div class="tw-flex tw-w-full">
  <div
    class="tw-w-full tw-inline-block tw-p-[2.4rem_3.2rem]"
    [ngClass]="{
      'tw-grow': newField || fieldType === fieldTypes.DOCUMENT,
      'tw-basis-[50%]': !newField && fieldType !== fieldTypes.DOCUMENT,
    }"
  >
    @if (fieldType === fieldTypeDocument) {
      <div class="tw-mb-[2.4rem]">
        @if ((information || documentId) && (isPDF$ | async)) {
          <p class="tw-text-[1.4rem] tw-font-medium">
            {{ labelMessage }}
          </p>
          <button
            fin-button
            [appearance]="finButtonAppearance.SECONDARY"
            [disabled]="isBtnDisabled"
            class="tw-mt-[1.6rem]"
            (click)="startAIGenerate()"
          >
            <fin-icon src="assets/svg/svgAIDocument.svg"></fin-icon>
            {{ generateAITextBtn }}
          </button>
          <p class="tw-absolute tw-pt-[0.5rem] tw-text-color-text-error">
            {{ errorMsg$ | async }}
          </p>
        } @else {
          <div
            i18n="@@documentFiller.instructions"
            class="tw-text-[1.4rem] tw-font-medium"
          >
            Wussten Sie schon? neoshare AI kann Ihnen jetzt Titel- und
            Beschreibungsvorschläge für PDFs mit Textinhalt liefern.
          </div>
        }
      </div>
    }

    <div class="tw-flex tw-flex-col tw-gap-[2.4rem]" [formGroup]="formGroup">
      <div>
        <fin-input
          [readonly]="isMirrored"
          formControlName="label"
          dynamicErrorSpace="dynamic"
          [maxLength]="150"
        >
          @if (!isMirrored) {
            <fin-input-countdown finInputSuffix></fin-input-countdown>
          }
          <ng-container finInputLabel>
            <div class="!tw-flex tw-justify-between tw-w-full">
              <span><span i18n="@@editField.label">Bezeichnung</span>*</span>
              @if (isMirrored) {
                <fin-icon
                  name="info"
                  finTooltip
                  [size]="finSize.S"
                  [content]="syncedFS"
                  [openDelay]="400"
                  class="tw-text-color-icons-tertiary"
                >
                </fin-icon>
              }
            </div>
          </ng-container>
          <ng-content select="[field-hint]"></ng-content>

          <fin-field-messages>
            <ng-template
              finFieldMessage
              type="error"
              errorKey="required"
              i18n="@@validationError.requiredField"
            >
              Pflichtfeld
            </ng-template>
            <ng-template
              finFieldMessage
              type="error"
              errorKey="documentNameNotUnique"
              i18n="@@documentField.validationError.documentNameExist"
            >
              Der Name wird bereits für ein Dokumentfeld verwendet. Bitte wählen
              Sie einen anderen
            </ng-template>
            <ng-template
              finFieldMessage
              type="error"
              errorKey="doesNotEndWith"
              i18n="
                @@folderStructure.manageFolderModal.folderName.errors.doesNotEndWith"
            >
              Datei- oder Ordnernamen dürfen nicht enden mit:
              {{ documentNameForbiddenEnd }}
            </ng-template>
            <ng-template
              finFieldMessage
              type="error"
              errorKey="pattern"
              i18n="@@validationError.invalidSymbol"
            >
              Unzulässiges Symbol
            </ng-template>
          </fin-field-messages>
        </fin-input>
        <ng-container
          *ngTemplateOutlet="
            revertButton;
            context: {
              counter: isButtonVisible[label],
              inputName: label,
            }
          "
        ></ng-container>
      </div>

      <div>
        <fin-text-area
          [maxLength]="maxDocumentDescriptionLength"
          [disabled]="isMirrored"
          formControlName="description"
          dynamicErrorSpace="dynamic"
        >
          <ng-container finInputLabel>
            <span
              i18n="
                @@contractManagement.template.templateModal.description.label"
              >Beschreibung</span
            >
          </ng-container>
          <ng-container finInputHint>
            <div class="tw-float-right tw-mt-[0.4rem]">
              {{ formGroup.controls.description.value.length }}/{{
                maxDocumentDescriptionLength
              }}
            </div>
          </ng-container>
        </fin-text-area>
        <ng-container
          *ngTemplateOutlet="
            revertButton;
            context: {
              counter: isButtonVisible[description],
              inputName: description,
            }
          "
        ></ng-container>
      </div>

      @if (showDocumentCategory$ | async) {
        <fin-dropdown
          formControlName="categoryId"
          i18n-label="@@dataRoom.documentCategory.label"
          label="Dokumentenkategorie auswählen"
          [options]="documentCategoryOptions"
          i18n-placeholder="@@select.initialPlaceHolder"
          placeholder="Keine ausgewählt"
        >
        </fin-dropdown>
      }
    </div>
  </div>
</div>

<ng-template #syncedFS>
  <div i18n="@@editField.mirroredField" class="tw-flex tw-text-start">
    Dieses Feld ist mit den Finanzierungsdetails synchronisiert und kann daher
    nicht bearbeitet werden
  </div>
</ng-template>

<ng-template #revertButton let-counter="counter" let-inputName="inputName">
  @if (counter) {
    <button
      fin-button
      [appearance]="finButtonAppearance.STEALTH"
      [size]="finSize.M"
      class="tw-mt-[0.4rem]"
      (click)="revertValue(inputName)"
    >
      <fin-icon name="keyboard_return"></fin-icon>
      <span
        class="tw-font-semibold tw-text-[1.4rem] tw-leading-[2rem]"
        i18n="@@documentFiller.revert.button"
        >Zurücksetzen</span
      >
    </button>
  }
</ng-template>
