import {
  Component,
  DestroyRef,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AbstractControl, FormGroup } from '@angular/forms';
import { FieldTypeEnum } from '@fincloud/core/formly';
import { selectIsVolksbankGroupWithBmsSales } from '@fincloud/state/customer';
import {
  StateLibDocumentAiFillerPageActions,
  selectError,
  selectIsPDF,
  selectTitleAndDescription,
} from '@fincloud/state/document';
import { Information } from '@fincloud/swagger-generator/company';
import { DocumentCategoryDto } from '@fincloud/swagger-generator/document';
import { BusinessCaseInformation } from '@fincloud/swagger-generator/exchange';
import { Dictionary } from '@fincloud/types/models';
import { FinButtonActionType, FinButtonAppearance } from '@fincloud/ui/button';
import { FinDropdownOption } from '@fincloud/ui/dropdown';
import { FinSize } from '@fincloud/ui/types';
import { DOCUMENT_AI_BUTTON_LABEL } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { map, shareReplay } from 'rxjs';

@Component({
  selector: 'app-document-field',
  templateUrl: './document-field.component.html',
  styleUrls: ['./document-field.component.scss'],
})
export class DocumentFieldComponent implements OnInit, OnChanges, OnDestroy {
  @Input() fieldType: string;
  @Input() newField: boolean;
  @Input() formGroup: FormGroup;
  @Input() fileDeleting: boolean;
  @Input() fileUploading: boolean;
  @Input() documentCategoryOptions: FinDropdownOption<
    DocumentCategoryDto['id']
  >[];

  @Input() isMirrored: boolean;

  @Input() set documentId(documentId: string) {
    this._documentId = documentId;
  }

  get documentId() {
    return this._documentId;
  }

  @Input() set information(information: BusinessCaseInformation | Information) {
    this._information = information;
  }

  get information() {
    return this._information;
  }

  readonly fieldTypes = FieldTypeEnum;
  readonly finButtonAppearance = FinButtonAppearance;
  readonly finSize = FinSize;
  readonly finButtonActionType = FinButtonActionType;
  readonly maxDocumentDescriptionLength = 250;
  readonly documentNameForbiddenEnd = '.';

  private readonly MAX_ATTEMPTS = 3;
  private readonly MIN_ATTEMPTS = 0;
  private readonly PREV_VALUE_INDEX = 2;

  private _information: BusinessCaseInformation | Information;
  private _documentId = '';
  private previousValue: Dictionary<string> = {};
  private generatedAiAttemps: number = this.MIN_ATTEMPTS;
  private documentName: string;
  private initialLabel = '';
  private initialDescription = '';
  private valueHistory: { [key: string]: string[] } = {
    label: [],
    description: [],
  };
  private resetCounters: { [key: string]: number } = {
    label: this.MIN_ATTEMPTS,
    description: this.MIN_ATTEMPTS,
  };
  isButtonVisible: { [key: string]: boolean } = {
    label: false,
    description: false,
  };

  generateAITextBtn: string = DOCUMENT_AI_BUTTON_LABEL.DEFAULT_BUTTON_TEXT;
  isBtnDisabled = false;
  inputName: string;
  private initialMessage = $localize`:@@documentFiller.label:Generieren Sie Titel und Beschreibung mit neoshare AI`;
  private messageMaxAttemptsReached = $localize`:@@documentFiller.labelMaxAttemptsReached:neoshare AI-Vorschläge sind auf 3 begrenzt.`;
  labelMessage = this.initialMessage;
  errorMsg$ = this.store.select(selectError);
  isPDF$ = this.store.select(selectIsPDF);
  titleAndDescription$ = this.store.select(selectTitleAndDescription);

  constructor(
    private destroyRef: DestroyRef,
    private store: Store,
  ) {}

  ngOnInit(): void {
    this.initializeFormValues();
    this.isPdf();
    this.subscribeToTitleAndDescription();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (
      (changes.documentId?.currentValue && changes.fileUploading) ||
      changes.fileDeleting
    ) {
      this._documentId = changes.documentId?.currentValue;
      this.isPdf();
    }
    if (
      !changes.documentId?.currentValue &&
      !changes.documentId?.previousValue &&
      changes.documentId &&
      !changes.information
    ) {
      return;
    } else if (
      !changes.documentId?.currentValue &&
      !changes.information?.currentValue?.id &&
      !changes.fileUploading
    ) {
      this._information = changes.information?.currentValue?.id;
      this._documentId = changes.documentId?.currentValue;
      this.isPdf();
      this.clearData();
      this.resetForm();
      this.resetFormState(['label', 'description']);
    }
  }

  get labelControl() {
    return this.formGroup.controls.label;
  }

  get descriptionControl() {
    return this.formGroup.controls.description;
  }

  get labelValue() {
    return this.formGroup.controls.label.value;
  }

  get descriptionValue() {
    return this.formGroup.controls.description.value;
  }

  get label() {
    return this.controlNames(this.labelControl);
  }

  get description() {
    return this.controlNames(this.descriptionControl);
  }

  get fieldTypeDocument() {
    return this.fieldTypes.DOCUMENT;
  }

  showDocumentCategory$ = this.store
    .select(selectIsVolksbankGroupWithBmsSales)
    .pipe(
      shareReplay({ bufferSize: 1, refCount: true }),
      map(
        (canShowCategories) =>
          canShowCategories && this.fieldType === this.fieldTypes.DOCUMENT,
      ),
    );

  onInputChange(inputData: { [key: string]: string }): void {
    Object.keys(inputData).forEach((key) => {
      const value = inputData[key];
      this.valueHistory[key].push(value);
      this.inputName = key;
      this.isButtonVisible[key] = this.showButton(key);
    });
  }

  revertValue(inputName: string): void {
    if (this.resetCounters[inputName] < this.MAX_ATTEMPTS) {
      this.resetCounters[inputName] += 1;
      this.formGroup
        .get(inputName)
        .setValue(this.previousValue[inputName] ?? '');
      this.previousValue[inputName] = this.getPreviousValue(inputName);
      this.valueHistory[inputName] = this.valueHistory[inputName].slice(0, -1);
      this.isButtonVisible[inputName] = this.showButton(inputName);
    }
  }

  startAIGenerate() {
    this.generatedAiAttemps++;

    this.setGeneratingState();
    const documentId = this.documentId || (this.information?.value as string);
    this.store.dispatch(
      StateLibDocumentAiFillerPageActions.getBinary({
        documentId,
        documentName: this.documentName,
      }),
    );
  }

  private controlNames(control: AbstractControl) {
    let name: string;
    Object.keys(this.formGroup.controls).forEach((key) => {
      const childControl = this.formGroup.get(key);
      if (childControl !== control) {
        return;
      }

      name = key;
    });

    return name;
  }

  private isPdf() {
    this.isBtnDisabled = false;
    const documentId = this._documentId || (this._information?.value as string);
    if (documentId && this.fieldType === this.fieldTypeDocument) {
      this.store.dispatch(
        StateLibDocumentAiFillerPageActions.loadDocument({ documentId }),
      );
    }
  }

  private initializeFormValues(): void {
    this.initialLabel = this.formGroup.controls.label.value;
    this.initialDescription = this.formGroup.controls.description.value;
    if (this.initialDescription || this.initialLabel) {
      this.pushInitialValues(['label', 'description']);
    }
  }

  private pushInitialValues(fields: string[]): void {
    fields.forEach((field) => {
      const initialValue = this.formGroup.get(field).value;
      if (initialValue) {
        this.valueHistory[field].push(initialValue);
      }
    });
  }

  private getPreviousValue(inputName: string): string {
    return this.valueHistory[inputName][
      this.valueHistory[inputName].length - this.PREV_VALUE_INDEX
    ];
  }

  private showButton(inputName: string): boolean {
    this.previousValue[inputName] = this.getPreviousValue(inputName);
    let isNotPrePopulated = true;
    const isDescription = inputName === 'description';

    const current = this.formGroup.get(inputName).value;
    if (!current) {
      isNotPrePopulated = false;
    }

    if (
      (this.initialLabel || this.initialDescription) &&
      (this.descriptionValue || this.labelValue) &&
      !this.previousValue
    ) {
      isNotPrePopulated = false;
    }

    if (isDescription && !this.initialDescription && this.descriptionValue) {
      isNotPrePopulated = true;
    }

    return (
      this.resetCounters[inputName] < this.MAX_ATTEMPTS && isNotPrePopulated
    );
  }

  private subscribeToTitleAndDescription(): void {
    this.titleAndDescription$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((data) => {
        if (data) {
          this.formGroup.get('label').setValue(data.title);
          this.formGroup.get('description').setValue(data.summary);
          this.generateAgaingButtonState(false);
          this.onInputChange({
            description: data.summary,
            label: data.title,
          });
        }
      });
    this.errorMsg$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((error) => {
        if (error) {
          this.generateAgaingButtonState(true);
        }
      });
  }

  private resetFormState(fields: string[]): void {
    fields.forEach((field) => {
      this.valueHistory[field] = [];
      this.resetCounters[field] = this.MIN_ATTEMPTS;
      this.isButtonVisible[field] = false;
    });
    this.generatedAiAttemps = this.MIN_ATTEMPTS;
    this.labelMessage = this.initialMessage;
    this.initialDescription = '';
    this.initialLabel = '';
  }

  private resetForm() {
    Object.keys(this.formGroup.controls).forEach((key) => {
      this.formGroup.get(key)?.setValue('');
    });
    this.generateAITextBtn = DOCUMENT_AI_BUTTON_LABEL.DEFAULT_BUTTON_TEXT;
    this.isBtnDisabled = false;
  }

  private setGeneratingState() {
    this.generateAITextBtn = DOCUMENT_AI_BUTTON_LABEL.GENERATING_RESPONSE_TEXT;
    this.isBtnDisabled = true;
  }

  private generateAgaingButtonState(isDisabled: boolean) {
    this.generateAITextBtn = DOCUMENT_AI_BUTTON_LABEL.GENERATE_AGAIN_TEXT;
    this.isBtnDisabled =
      isDisabled || this.generatedAiAttemps >= this.MAX_ATTEMPTS;
    if (this.generatedAiAttemps >= this.MAX_ATTEMPTS) {
      this.labelMessage = this.messageMaxAttemptsReached;
    }
  }

  private clearData() {
    this.store.dispatch(StateLibDocumentAiFillerPageActions.clearDataForPdf());
  }

  ngOnDestroy() {
    this.store.dispatch(StateLibDocumentAiFillerPageActions.clearDataForPdf());
  }
}
