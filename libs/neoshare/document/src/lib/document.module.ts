import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NsUiButtonsModule } from '@fincloud/components/buttons';
import { NsUiFilesModule } from '@fincloud/components/files';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiLoadersModule } from '@fincloud/components/loaders';
import { NsUiPdfModule } from '@fincloud/components/pdf';
import { NsUiSelectsModule } from '@fincloud/components/selects';
import { NsUiTextModule } from '@fincloud/components/text';
import { NsUiNgxModule } from '@fincloud/components/third-party-modules';
import { NsUiTooltipModule } from '@fincloud/components/tooltip';
import { NsCoreDirectivesModule } from '@fincloud/core/directives';
import { NsCoreFocusModule } from '@fincloud/core/focus';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { NsNeogptChatModule } from '@fincloud/neoshare/neogpt-chat';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinDropdownModule } from '@fincloud/ui/dropdown';
import { FinFieldMessageModule } from '@fincloud/ui/field-message';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinInputModule } from '@fincloud/ui/input';
import { FinLoaderModule } from '@fincloud/ui/loader';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import { FinTextAreaModule } from '@fincloud/ui/text-area';
import { FinTooltipModule } from '@fincloud/ui/tooltip';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinWarningMessageModule } from '@fincloud/ui/warning-message';
import { TourNgxBootstrapModule } from 'ngx-ui-tour-ngx-bootstrap';
import { DocumentFieldUploadComponent } from './components/document-field-upload/document-field-upload.component';
import { DocumentFieldComponent } from './components/document-field/document-field.component';
import { DocumentPreviewComponent } from './components/document-preview/document-preview.component';
import { DownloadFilesAsZipComponent } from './components/download-files-as-zip/download-files-as-zip.component';

@NgModule({
  imports: [
    CommonModule,
    NsUiPdfModule,
    TourNgxBootstrapModule,
    NsCoreFocusModule,
    FormsModule,
    ReactiveFormsModule,
    FinTextAreaModule,
    NsNeogptChatModule,
    FinTooltipModule,
    FinIconModule,
    FinInputModule,
    FinButtonModule,
    NsCorePipesModule,
    NsUiSelectsModule,
    NsUiLoadersModule,
    NsUiIconsModule,
    NsUiFilesModule,
    NsUiButtonsModule,
    NsUiNgxModule,
    NsUiPdfModule,
    NsUiTooltipModule,
    NsUiTextModule,
    FinFieldMessageModule,
    FinLoaderModule,
    NsCoreDirectivesModule,
    NsCorePipesModule,
    FinWarningMessageModule,
    FinTruncateTextModule,
    FinDropdownModule,
    FinScrollbarModule,
  ],
  declarations: [
    DocumentFieldUploadComponent,
    DocumentPreviewComponent,
    DownloadFilesAsZipComponent,
    DocumentFieldComponent,
  ],
  exports: [
    DownloadFilesAsZipComponent,
    DocumentFieldUploadComponent,
    DocumentFieldComponent,
  ],
})
export class NsDocumentModule {}
