import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NsUiBooleansModule } from '@fincloud/components/booleans';
import { ElementCountComponent } from '@fincloud/components/element-count';
import { NsUiFilesModule } from '@fincloud/components/files';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiNavigationModule } from '@fincloud/components/navigation';
import { NsUiNumberModule } from '@fincloud/components/number';
import { NsUiTruncatedTextModule } from '@fincloud/components/truncated-text';
import { NsCoreDateModule } from '@fincloud/core/date';
import { NsCoreDirectivesModule } from '@fincloud/core/directives';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { NsBusinessCaseRefactoringModule } from '@fincloud/neoshare/business-case';
import { NsBusinessCaseFieldsModule } from '@fincloud/neoshare/business-case-fields';
import { NsFieldInformationActionsModule } from '@fincloud/neoshare/field-information-actions';
import { NsNeogptChatModule } from '@fincloud/neoshare/neogpt-chat';
import { FinActionsMenuModule } from '@fincloud/ui/actions-menu';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinDocumentModule } from '@fincloud/ui/document';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinLoaderModule } from '@fincloud/ui/loader';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import { FinTooltipModule } from '@fincloud/ui/tooltip';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { NgxPermissionsModule } from 'ngx-permissions';
import { CompositeFieldComponent } from './components/composite-field/composite-field.component';
import { DocumentFieldComponent } from './components/document-field/document-field.component';
import { TemplateFieldComponent } from './components/template-field/template-field.component';
import { LabelFieldPipe } from './pipes/label-field.pipe';

@NgModule({
  declarations: [
    TemplateFieldComponent,
    CompositeFieldComponent,
    DocumentFieldComponent,
    LabelFieldPipe,
  ],
  imports: [
    CommonModule,
    NsFieldInformationActionsModule,
    NsNeogptChatModule,
    ReactiveFormsModule,
    NsBusinessCaseFieldsModule,
    NsUiFilesModule,
    FinTruncateTextModule,
    FinTooltipModule,
    NsUiBooleansModule,
    NsBusinessCaseRefactoringModule,
    FinDocumentModule,
    FinIconModule,
    NsCoreDirectivesModule,
    FinButtonModule,
    FinActionsMenuModule,
    FinActionsMenuModule,
    FinIconModule,
    NgxPermissionsModule,
    NsCorePipesModule,
    NsCoreDateModule,
    NsUiFilesModule,
    NsUiTruncatedTextModule,
    NsUiIconsModule,
    NsUiNavigationModule,
    NsUiNumberModule,
    FinMenuItemModule,
    ElementCountComponent,
    FinLoaderModule,
  ],
  exports: [TemplateFieldComponent, DocumentFieldComponent],
})
export class NsTemplateFieldModule {}
