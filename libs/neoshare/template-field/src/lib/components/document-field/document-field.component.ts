import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  EventEmitter,
  HostListener,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { DocumentInteractionsService } from '@fincloud/core/services';
import { Toast } from '@fincloud/core/toast';
import { FieldBase } from '@fincloud/neoshare/business-case-fields';
import { selectDocumentsFiles } from '@fincloud/state/business-case';
import { selectDocumentIconPathsById } from '@fincloud/state/document';
import { TemplateFieldViewMode } from '@fincloud/types/enums';
import { FolderStructureListViewDisplayOptions } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { FinSize } from '@fincloud/ui/types';
import { FOLDER_STRUCTURE_LIST_VIEW_COLUMN_SIZE_LIST } from '@fincloud/utils';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { isEqual } from 'lodash-es';
import { Observable, ReplaySubject, distinctUntilChanged, map } from 'rxjs';

@Component({
  selector: 'ui-document-field',
  templateUrl: './document-field.component.html',
  styleUrls: ['./document-field.component.scss'],

  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentFieldComponent extends FieldBase implements OnChanges {
  @Input()
  img$: Observable<string | ArrayBuffer>;

  documentIconPaths$ = this.store.select(selectDocumentIconPathsById);

  private documentId$$ = new ReplaySubject<string>(1);
  documentSize$ = this.documentId$$.pipe(
    concatLatestFrom(() => this.store.select(selectDocumentsFiles)),
    map(
      ([documentId, documentFiles]) =>
        documentFiles.find((doc) => doc.id === documentId)?.contentReference
          ?.contentSize,
    ),
    distinctUntilChanged(isEqual),
  );

  @Input() documentId: string;
  @Input() isSearchMode = false;
  @Input() isFilterMode = false;
  @Input() isUploading = false;
  @Input() isThereErrorDuringUpload = false;
  @Input() disabled = false;
  @Input() viewMode: TemplateFieldViewMode = TemplateFieldViewMode.DEFAULT;
  @Input() listViewDisplayOptions: FolderStructureListViewDisplayOptions;

  @Output() fileDropped = new EventEmitter<File>();
  @Output() showInEnclosingFolder = new EventEmitter();
  @Output() previewModalOpen = new EventEmitter();

  img: string | ArrayBuffer;
  noDescriptionMessage = $localize`:@@documentField.message.missingDescription:Keine Beschreibung hinzugefügt`;
  private showSpinner = false;
  protected finSize = FinSize;
  protected dateFormatAndTime: string;
  selected = false;

  private readonly toastErrorMaxFileCountReachedPrefix = $localize`:@@fileUploader.toast.error.maxFileCount.prefix:Maximale Dateianzahl erreicht. Es können maximal`;
  private readonly toastErrorMaxFileCountReachedSuffix = $localize`:@@fileUploader.toast.error.maxFileCount.suffix:gleichzeitig hochgeladen werden.`;

  readonly templateFieldViewMode = TemplateFieldViewMode;
  readonly listViewColumnSizeList = FOLDER_STRUCTURE_LIST_VIEW_COLUMN_SIZE_LIST;

  @HostListener('dragover', ['$event'])
  onDragOver(event: DragEvent) {
    event.preventDefault();
  }

  @HostListener('dragenter', ['$event'])
  onDragEnter(ev: DragEvent): void {
    ev.stopImmediatePropagation();
    ev.preventDefault();

    if (this.isEditMode) {
      this.documentInteractionsService.fieldEnter();
      this.selected = true;
    }
  }

  onDragLeave(ev: DragEvent): void {
    ev.stopImmediatePropagation();
    ev.preventDefault();

    if (this.isEditMode) {
      this.documentInteractionsService.fieldExit();
      this.selected = false;
    }
  }

  @HostListener('drop', ['$event'])
  onItemDrop(ev: DragEvent): void {
    ev.stopImmediatePropagation();
    ev.preventDefault();
    this.selected = false;

    this.documentInteractionsService.stopAllHoverEffects();

    if (!this.isEditMode) {
      return;
    }

    if (ev.dataTransfer.files?.length > 1) {
      this.finToastService.show(
        Toast.error(
          `${this.toastErrorMaxFileCountReachedPrefix} 1 ${this.toastErrorMaxFileCountReachedSuffix}`,
        ),
      );

      return;
    }

    if (ev?.dataTransfer?.files?.length) {
      this.onDrop(ev.dataTransfer.files[0]);
    }
  }

  get showNewDocumentField() {
    return (
      !this.showSpinner &&
      !this.documentId &&
      !this.isUploading &&
      !this.isThereErrorDuringUpload
    );
  }

  get showLoadingSpinner() {
    return (this.showSpinner && !!this.documentId) || this.isUploading;
  }

  constructor(
    public destroyRef: DestroyRef,
    regionalSettings: RegionalSettingsService,
    private documentInteractionsService: DocumentInteractionsService,
    private finToastService: FinToastService,
    private store: Store,
  ) {
    super(destroyRef);
    this.dateFormatAndTime = `${regionalSettings.dateFormat} HH:mm`;
  }

  ngOnChanges(changes: SimpleChanges): void {
    super.ngOnChanges(changes);

    if (changes.documentId) {
      this.documentId$$.next(changes.documentId?.currentValue);

      if (!changes.documentId.currentValue) {
        this.img = undefined;
      }
    }
  }

  onOpenPreviewModal() {
    if (!this.isUploading) {
      this.previewModalOpen.emit();
    }
  }

  onShowInEnclosingFolder() {
    this.showInEnclosingFolder.emit();
  }

  onDrop(file: File) {
    this.img = '';
    this.fileDropped.emit(file);
  }
}
