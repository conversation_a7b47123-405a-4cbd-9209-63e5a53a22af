@if (viewMode === templateFieldViewMode.DEFAULT) {
  <fin-document
    [description]="field.description || noDescriptionMessage"
    [showLoader]="showLoadingSpinner"
    [dateAndTime]="information?.lastModifiedDate | time: dateFormatAndTime"
    [imageSrc]="img$ | async"
    [selected]="selected && isEditMode"
    [disabled]="disabled"
    [warning]="isFieldRequestHighlight"
    [error]="isThereErrorDuringUpload"
    (click)="onOpenPreviewModal()"
  >
    <ng-container finFieldLabel>
      <ng-container *ngTemplateOutlet="fieldLabelTemplate"></ng-container>
    </ng-container>
    <ng-container finIcon>
      <ng-container *ngTemplateOutlet="fieldIconTemplate"></ng-container>
    </ng-container>

    <ng-container finActions>
      <ng-container *ngTemplateOutlet="fieldActionsTemplate"></ng-container>
    </ng-container>
  </fin-document>
} @else {
  <div
    class="tw-flex tw-items-center tw-gap-[2.4rem] tw-w-[calc(100%-6px)] tw-h-[4.4rem] tw-px-[1.2rem] tw-mx-auto tw-border-b tw-border-color-border-default-minimal hover:tw-bg-color-hover-neutral"
    [ngClass]="{
      'tw-border tw-rounded-[0.4rem] tw-mb-[2px] tw-mt-[-1px] !tw-w-full !tw-px-[1.4rem]':
        isFieldRequestHighlight ||
        isThereErrorDuringUpload ||
        isUploading ||
        (selected && isEditMode),
      '!tw-border-color-border-default-warning': isFieldRequestHighlight,
      '!tw-border-color-border-default-error': isThereErrorDuringUpload,
      '!tw-border-color-border-default-interactive tw-bg-color-background-secondary-minimal':
        selected && isEditMode,
      'tw-bg-color-background-disabled': isUploading,
    }"
  >
    <div
      class="tw-flex tw-justify-start tw-items-center tw-gap-[1.2rem] tw-flex-grow tw-flex-shrink tw-cursor-pointer tw-w-full tw-overflow-hidden"
      (click)="onOpenPreviewModal()"
    >
      <div class="tw-min-w-fit">
        @if (isUploading) {
          <fin-loader></fin-loader>
        } @else {
          <ng-container *ngTemplateOutlet="fieldIconTemplate"></ng-container>
        }
      </div>
      <div class="tw-text-body-2-strong" finTruncateText>
        <ng-container *ngTemplateOutlet="fieldLabelTemplate"></ng-container>
      </div>
    </div>
    @if (listViewDisplayOptions?.fileSize) {
      <div
        class="tw-text-body-2-moderate"
        [style.min-width.px]="listViewColumnSizeList.fileSize"
      >
        @if (documentSize$ | async; as documentSize) {
          {{ documentSize | fileSize }}
        } @else {
          -
        }
      </div>
    }
    @if (listViewDisplayOptions?.dateUpdated) {
      <div
        class="tw-text-body-2-moderate"
        [style.min-width.px]="listViewColumnSizeList.dateUpdated"
      >
        {{ information?.lastModifiedDate | time: dateFormatAndTime }}
      </div>
    }
    <div
      class="tw-flex tw-justify-end tw-items-center"
      [style.min-width.px]="listViewColumnSizeList.actions"
    >
      <ng-container *ngTemplateOutlet="fieldActionsTemplate"></ng-container>
    </div>
  </div>
}

@if (selected) {
  <div
    class="tw-absolute tw-top-0 tw-left-0 tw-w-full tw-h-full"
    (dragleave)="onDragLeave($event)"
  ></div>
}

<ng-template #fieldLabelTemplate>
  @if (highlightLabel) {
    <mark class="tw-p-0">{{ field.label }}</mark>
  } @else {
    {{ field.label }}
  }
</ng-template>

<ng-template #fieldIconTemplate>
  @if (documentId && documentIconPaths$ | async; as iconPaths) {
    <fin-icon
      [class.fin-opacity-40]="disabled"
      [size]="finSize.L"
      [src]="iconPaths[documentId]"
    ></fin-icon>
  } @else {
    <fin-icon
      [class.fin-opacity-40]="disabled"
      [size]="finSize.L"
      src="assets/svg/svgPlaceholderIcon.svg"
    ></fin-icon>
  }
</ng-template>

<ng-template #fieldActionsTemplate>
  <div class="tw-flex tw-flex-row tw-w-fit">
    @if (!isEditMode && isSharedDataRoom) {
      <button
        class="tw-mr-[1.2rem]"
        (click)="copyField(); $event.stopPropagation()"
      >
        <fin-icon name="content_copy" [size]="finSize.S"></fin-icon>
      </button>
    }
    <ng-content> </ng-content>
    @if ((isSearchMode || isFilterMode) && !isEditMode) {
      <button
        class="tw-ml-5"
        (click)="onShowInEnclosingFolder(); $event.stopPropagation()"
      >
        <fin-icon
          finTooltip
          i18n-content="@@folderStructure.actions.showInEnclosing"
          content="Im beigefügten Ordner anzeigen"
          name="content_paste_go"
          [size]="finSize.S"
        ></fin-icon>
      </button>
    }
  </div>
</ng-template>
