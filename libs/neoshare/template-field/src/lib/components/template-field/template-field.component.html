<!-- FIELD INFORMATION ACTIONS -->
<ng-template #fieldInformationActions>
  <app-field-information-actions
    class="tw-flex tw-grow tw-justify-end"
    [businessCaseId]="templateFieldData.dataRoomOwnerId"
    [businessCaseParticipants]="templateFieldData.businessCaseParticipants"
    [canSeeTopicChats]="templateFieldData.canSeeTopicChats"
    [caseFieldParticipantAccess]="
      templateFieldData.caseFieldsParticipantAccessDict?.[information.id]
    "
    [caseFieldParticipantInputRequest]="
      templateFieldData.caseFieldsParticipantInputsReqsDict?.[information.id]
    "
    [chat]="
      templateFieldData.existingTopicChats?.[information.id] ||
      templateFieldData.existingArchivedTopicChats?.[information.id]
    "
    [config]="templateFieldData.fieldActionsConfig"
    [documentId]="informationValue"
    [hasDataRoomWriteAccess]="templateFieldData.hasDataRoomWriteAccess"
    [hasDracoonSynchronization]="hasDracoonSynchronization"
    [hasFieldGroupAccess]="hasFieldGroupAccess"
    [information]="information"
    [isArchivedTopicChat]="
      !!templateFieldData.existingArchivedTopicChats?.[information.id]
    "
    [isCustomerRealEstate]="templateFieldData.isCustomerRealEstate"
    [isDocument]="isFieldDocument"
    [isEditMode]="isEditMode"
    [isExistingTopicChat]="
      !!templateFieldData.existingTopicChats?.[information.id]
    "
    [isFieldEditable]="isFieldEditable"
    [isLeadCustomer]="templateFieldData.isEmployeeOfLeadCustomer"
    [isMirroredCalculatableField]="isMirroredCalculatableField"
    [isMirroredField]="isMirroredField"
    [isPartOfBusinessCase]="templateFieldData.isPartOfBusinessCase"
    [isParticipantPartOfChat]="isParticipantPartOfChat"
    [isServiceSynchronizedWithNextfolder]="isServiceSynchronizedWithNextfolder"
    [isUploadingDocument]="isUploading"
    [nextfolderDocuments]="nextfolderDocuments"
    [userCustomerKey]="templateFieldData.userCustomerKey"
    (download)="download($event)"
    (upload)="selectFilesForUpload()"
  ></app-field-information-actions>
</ng-template>

<ng-template #fieldKebapActions>
  @if (showActionsMenu && !isFieldKebapActionsMenuEmpty) {
    <button type="button" [finActionMenuTrigger]="finMenu.panel">
      <fin-icon name="more_vert"></fin-icon>
    </button>
    <fin-actions-menu #finMenu="finActionMenu">
      <ng-container *ngTemplateOutlet="menuOptions"></ng-container>
    </fin-actions-menu>
  }
</ng-template>

@if (isFieldDocument) {
  <ui-file-uploader></ui-file-uploader>
}

<div
  [id]="templateFieldPrefix + information.key"
  class="tw-flex tw-flex-col tw-w-full tw-h-full tw-justify-start tw-grid-cols-{{
    templateFieldData.countOfOccupiedColumns || 2
  }}"
  [class.tw-justify-start]="
    field.fieldType === fieldsTypes.DOCUMENT ||
    field.fieldType === fieldsTypes.LONG_TEXT
  "
  [ngClass]="{
    'tw-grid tw-col-span-full tw-gap-[2.4rem]':
      field.fieldType === fieldsTypes.LONG_TEXT,
  }"
>
  <!-- @if (!isFieldDocument) {
    <div class="field-header">
      <ui-truncated-text widthCalculationExpression="24rem" class="label">
        {{ field.index }}
        {{ translateFields(information.field?.label ?? field.label) }}
      </ui-truncated-text>
      <ng-container *ngTemplateOutlet="fieldInformationActions"></ng-container>
    </div>
  } -->
  <div
    class="content tw-relative"
    [class.tw-col-span-2]="field.fieldType === fieldsTypes.LONG_TEXT"
    [class.tw-col-span-full]="field.fieldType === fieldsTypes.TABLE"
  >
    <ng-container *ngTemplateOutlet="fieldComponent"></ng-container>
  </div>
</div>

<!-- FIELD COMPONENT TEMPLATE -->
<ng-template #fieldComponent>
  <!-- Long text & Table fields have a dynamic height depending on their content which can't be
    calculated so that we could display a correct placeholder, hence we are not deferring their views.
  -->
  @defer (
    on viewport;
    when (field.fieldType === fieldsTypes.LONG_TEXT || field.fieldType === fieldsTypes.TABLE)
  ) {
    @switch (field.fieldType) {
      @case ('BOOLEAN') {
        <ui-boolean-field
          [dynamicErrorSpace]="templateFieldData.dynamicErrorSpace"
          [field]="field"
          [hasDataRoomWriteAccess]="templateFieldData.hasDataRoomWriteAccess"
          [highlightLabel]="isAIAssistantSourceSelected"
          [information]="information"
          [isEditMode]="isFieldInEditMode"
          [isFieldEditable]="isFieldEditable"
          [isFieldRequestHighlight]="fieldRequestHighlighted"
          [isMirroredField]="isMirroredField"
          [isSharedDataRoom]="templateFieldData.isSharedDataRoom"
          [label]="information | labelField: field"
          (copied)="onCopyField()"
          (save)="onValueSaved($event)"
        >
          <ng-container
            *ngTemplateOutlet="fieldInformationActions"
            information-actions
          >
          </ng-container>
          <ng-container *ngTemplateOutlet="fieldKebapActions" field-actions>
          </ng-container>
          @if (cantEditMirroredField && isEditMode) {
            <ng-container field-hint *ngTemplateOutlet="fieldHint">
            </ng-container>
          }
        </ui-boolean-field>
      }
      @case ('DATE') {
        <ui-date-field
          [dynamicErrorSpace]="templateFieldData.dynamicErrorSpace"
          [field]="field"
          [hasDataRoomWriteAccess]="templateFieldData.hasDataRoomWriteAccess"
          [highlightLabel]="isAIAssistantSourceSelected"
          [information]="information"
          [isEditMode]="isFieldInEditMode"
          [isFieldEditable]="isFieldEditable"
          [isFieldRequestHighlight]="fieldRequestHighlighted"
          [isMirroredField]="isMirroredField"
          [isSharedDataRoom]="templateFieldData.isSharedDataRoom"
          [label]="information | labelField: field"
          (copied)="onCopyField()"
          (save)="onValueSaved($event)"
        >
          <ng-container
            *ngTemplateOutlet="fieldInformationActions"
            information-actions
          >
          </ng-container>
          <ng-container *ngTemplateOutlet="fieldKebapActions" field-actions>
          </ng-container>
          @if (cantEditMirroredField && isEditMode) {
            <ng-container field-hint *ngTemplateOutlet="fieldHint">
            </ng-container>
          }
        </ui-date-field>
      }
      @case ('SHORT_TEXT') {
        <ui-text-field
          [dynamicErrorSpace]="templateFieldData.dynamicErrorSpace"
          [field]="field"
          [hasDataRoomWriteAccess]="templateFieldData.hasDataRoomWriteAccess"
          [highlightLabel]="isAIAssistantSourceSelected"
          [information]="information"
          [isEditMode]="isFieldInEditMode"
          [isFieldEditable]="isFieldEditable"
          [isFieldRequestHighlight]="fieldRequestHighlighted"
          [isMirroredField]="isMirroredField"
          [isSharedDataRoom]="templateFieldData.isSharedDataRoom"
          [label]="information | labelField: field"
          (copied)="onCopyField()"
          (save)="onValueSaved($event)"
        >
          <ng-container
            *ngTemplateOutlet="fieldInformationActions"
            information-actions
          >
          </ng-container>
          <ng-container *ngTemplateOutlet="fieldKebapActions" field-actions>
          </ng-container>
          @if (cantEditMirroredField && isEditMode) {
            <ng-container field-hint *ngTemplateOutlet="fieldHint">
            </ng-container>
          }
        </ui-text-field>
      }
      @case ('LONG_TEXT') {
        <ui-text-area-template-field
          [dynamicErrorSpace]="templateFieldData.dynamicErrorSpace"
          [field]="field"
          [hasDataRoomWriteAccess]="templateFieldData.hasDataRoomWriteAccess"
          [highlightLabel]="isAIAssistantSourceSelected"
          [information]="information"
          [isEditMode]="isFieldInEditMode"
          [isFieldEditable]="isFieldEditable"
          [isFieldRequestHighlight]="fieldRequestHighlighted"
          [isMirroredField]="isMirroredField"
          [isSharedDataRoom]="templateFieldData.isSharedDataRoom"
          [label]="information | labelField: field"
          (copied)="onCopyField()"
          (save)="onValueSaved($event)"
        >
          <ng-container
            *ngTemplateOutlet="fieldInformationActions"
            information-actions
          >
          </ng-container>
          <ng-container *ngTemplateOutlet="fieldKebapActions" field-actions>
          </ng-container>
          @if (cantEditMirroredField && isEditMode) {
            <ng-container field-hint *ngTemplateOutlet="fieldHint">
            </ng-container>
          }
        </ui-text-area-template-field>
      }
      @case ('LOCATION') {
        <ui-location-field
          [dynamicErrorSpace]="templateFieldData.dynamicErrorSpace"
          [field]="field"
          [hasDataRoomWriteAccess]="templateFieldData.hasDataRoomWriteAccess"
          [highlightLabel]="isAIAssistantSourceSelected"
          [information]="information"
          [isEditMode]="isFieldInEditMode"
          [isFieldEditable]="isFieldEditable"
          [isFieldRequestHighlight]="fieldRequestHighlighted"
          [isMirroredField]="isMirroredField"
          [isSharedDataRoom]="templateFieldData.isSharedDataRoom"
          [label]="information | labelField: field"
          (copied)="onCopyField()"
          (save)="onValueSaved($event)"
        >
          <ng-container
            *ngTemplateOutlet="fieldInformationActions"
            information-actions
          >
          </ng-container>

          <ng-container field-prefix>
            <fin-icon
              finFieldPrefix
              class="tw-cursor-pointer"
              name="location_on"
              finTooltip
              i18n-content="@@templateField.showOnMap"
              content="Auf der Karte anzeigen"
              (click)="onLocation(); $event.stopPropagation()"
            ></fin-icon>
          </ng-container>
          @if (cantEditMirroredField && isEditMode) {
            <ng-container field-hint *ngTemplateOutlet="fieldHint">
            </ng-container>
          }
          <ng-container *ngTemplateOutlet="fieldKebapActions" field-actions>
          </ng-container
        ></ui-location-field>
      }
      @case ('SELECT') {
        <ui-select-field
          [dynamicErrorSpace]="templateFieldData.dynamicErrorSpace"
          [field]="field"
          [hasDataRoomWriteAccess]="templateFieldData.hasDataRoomWriteAccess"
          [highlightLabel]="isAIAssistantSourceSelected"
          [information]="information"
          [isEditMode]="isFieldInEditMode"
          [isFieldEditable]="isFieldEditable"
          [isFieldRequestHighlight]="fieldRequestHighlighted"
          [isMirroredField]="isMirroredField"
          [isSharedDataRoom]="templateFieldData.isSharedDataRoom"
          [label]="information | labelField: field"
          (copied)="onCopyField()"
          (save)="onValueSaved($event)"
        >
          <ng-container
            *ngTemplateOutlet="fieldInformationActions"
            information-actions
          >
          </ng-container>
          <ng-container *ngTemplateOutlet="fieldKebapActions" field-actions>
          </ng-container>
          @if (cantEditMirroredField && isEditMode) {
            <ng-container field-hint *ngTemplateOutlet="fieldHint">
            </ng-container>
          }
        </ui-select-field>
      }
      @case ('TABLE') {
        <ui-data-grid-field
          [businessCaseId]="templateFieldData.dataRoomOwnerName"
          [dynamicErrorSpace]="templateFieldData.dynamicErrorSpace"
          [field]="field"
          [hasDataRoomWriteAccess]="templateFieldData.hasDataRoomWriteAccess"
          [highlightLabel]="isAIAssistantSourceSelected"
          [information]="information"
          [isEditMode]="isFieldInEditMode"
          [isFieldEditable]="isFieldEditable"
          [isFieldRequestHighlight]="fieldRequestHighlighted"
          [isMirroredField]="isMirroredField"
          [isSharedDataRoom]="templateFieldData.isSharedDataRoom"
          [label]="information | labelField: field"
          (copied)="onCopyField()"
          (save)="onValueSaved($event)"
        >
          <ng-container
            *ngTemplateOutlet="fieldInformationActions"
            information-actions
          >
          </ng-container>
          <ng-container *ngTemplateOutlet="fieldKebapActions" field-actions>
          </ng-container>
        </ui-data-grid-field>
      }
      @case ('COMPOSITE') {
        <ui-composite-field
          [field]="field"
          [hasDataRoomWriteAccess]="templateFieldData.hasDataRoomWriteAccess"
          [highlightLabel]="isAIAssistantSourceSelected"
          [information]="information"
          [isEditMode]="isFieldInEditMode"
          [isFieldEditable]="isFieldEditable"
          [isFieldRequestHighlight]="fieldRequestHighlighted"
          [isMirroredField]="isMirroredField"
          [isSharedDataRoom]="templateFieldData.isSharedDataRoom"
          (copied)="onCopyField()"
          (save)="onValueSaved($event)"
        >
        </ui-composite-field>
        @if (isEditMode) {
          <div class="commission-fee-info">
            <ui-icon name="info" color="gray" size="medium"> </ui-icon>
            <span i18n="@@templateField.composite"
              >Sie können den Wert hier nur solange ändern, bis Sie eine
              Einladung verschickt haben oder eine Bewerbung erhalten
              haben.</span
            >
          </div>
        }
      }
      @case ('DOCUMENT') {
        <ui-document-field
          [documentId]="informationValue"
          [field]="field"
          [hasDataRoomWriteAccess]="templateFieldData.hasDataRoomWriteAccess"
          [highlightLabel]="isAIAssistantSourceSelected"
          [img$]="img$"
          [information]="information"
          [isEditMode]="isFieldInEditMode"
          [isFieldRequestHighlight]="fieldRequestHighlighted"
          [isMirroredField]="isMirroredField"
          [isSearchMode]="isSearchMode"
          [isFilterMode]="isFilterMode"
          [isSharedDataRoom]="templateFieldData.isSharedDataRoom"
          [isThereErrorDuringUpload]="hasErrorDuringUpload"
          [isUploading]="isUploading"
          [viewMode]="viewMode"
          [listViewDisplayOptions]="
            templateFieldData.folderStructureListViewDisplayOptions
          "
          (copied)="onCopyField()"
          (fileDropped)="onDrop($event)"
          (previewModalOpen)="openPreviewModal()"
          (save)="onValueSaved($event)"
          (showInEnclosingFolder)="onShowInEnclosingFolder()"
        >
          <ng-container
            *ngTemplateOutlet="fieldInformationActions"
          ></ng-container>
          @if (showActionsMenu) {
            <button
              class="tw-ml-[1.2rem]"
              fin-button-action
              [actionType]="
                isUploading
                  ? finButtonActionType.TERTIARY
                  : finButtonActionType.PRIMARY
              "
              [disabled]="isUploading"
              (click)="$event.stopPropagation()"
              [finActionMenuTrigger]="finMenu.panel"
            >
              <fin-icon name="more_vert" [size]="finSize.S"></fin-icon>
            </button>
            <fin-actions-menu #finMenu="finActionMenu">
              @if (
                (isSearchMode || isFilterMode) &&
                isEditMode &&
                hasFieldGroupAccess
              ) {
                <button
                  fin-menu-item
                  (click)="onShowInEnclosingFolder()"
                  [size]="finSize.M"
                  iconName="content_paste_go"
                >
                  <ng-container finMenuItemTitle>
                    <span i18n="@@folderStructure.actions.showInEnclosing"
                      >Im beigefügten Ordner anzeigen</span
                    >
                  </ng-container>
                </button>
              }
              @if (hasFieldGroupAccess) {
                <ng-container
                  *ngTemplateOutlet="editFieldMenuItemTemplate"
                ></ng-container>

                @if (
                  templateFieldData.isVisibilityForAllParticipantsOn ||
                  templateFieldData.isEmployeeOfLeadCustomer ||
                  templateFieldData.isCADR
                ) {
                  <ng-container
                    *ngTemplateOutlet="revisionsMenuItemTemplate"
                  ></ng-container>
                }
              }

              @if (
                templateFieldData.fieldActionsConfig.supportsFolderStructure &&
                hasFieldGroupAccess &&
                !isFilterMode
              ) {
                <button
                  fin-menu-item
                  (click)="onMoveFile(information.field)"
                  [size]="finSize.M"
                  iconName="drive_file_move"
                >
                  <ng-container finMenuItemTitle>
                    <span i18n="@@folderStructure.folder.actions.move">
                      Verschieben
                    </span>
                  </ng-container>
                </button>
              }
              <button
                fin-menu-item
                (click)="selectFilesForUpload()"
                [size]="finSize.M"
                iconName="upload_file"
              >
                <ng-container finMenuItemTitle>
                  <span i18n="@@documentInbox.label.uploadFiles">
                    Hochladen
                  </span>
                </ng-container>
              </button>
              @if (hasFieldGroupAccess && !isFilterMode) {
                <ng-container
                  *ngTemplateOutlet="deleteMenuItemTemplate"
                ></ng-container>
              }
            </fin-actions-menu>
          }
        </ui-document-field>
      }
      @default {
        <ui-number-field
          [dynamicErrorSpace]="templateFieldData.dynamicErrorSpace"
          [field]="field"
          [hasDataRoomWriteAccess]="templateFieldData.hasDataRoomWriteAccess"
          [highlightLabel]="isAIAssistantSourceSelected"
          [information]="information"
          [isEditMode]="isFieldInEditMode"
          [isFieldEditable]="isFieldEditable"
          [isFieldRequestHighlight]="fieldRequestHighlighted"
          [isMirroredField]="isMirroredField"
          [isSharedDataRoom]="templateFieldData.isSharedDataRoom"
          [label]="information | labelField: field"
          (copied)="onCopyField()"
          (save)="onValueSaved($event)"
        >
          <ng-container
            *ngTemplateOutlet="fieldInformationActions"
            information-actions
          >
          </ng-container>
          <ng-container *ngTemplateOutlet="fieldKebapActions" field-actions>
          </ng-container>
          @if (cantEditMirroredField && isEditMode) {
            <ng-container field-hint *ngTemplateOutlet="fieldHint">
            </ng-container>
          }
        </ui-number-field>
      }
    }
  } @placeholder {
    <!-- Required html element for the defer view functionality to work -->
    <div [style.height.rem]="fieldPlaceholderHeightRem"></div>
  }
</ng-template>

<!-- MENU OPTIONS TEMPLATE -->
<ng-template #menuOptions>
  @if (
    (!isMirroredField || templateFieldData.isEmployeeOfLeadCustomer) &&
    hasFieldGroupAccess
  ) {
    <ng-container *ngTemplateOutlet="editFieldMenuItemTemplate"></ng-container>
  }
  @if (
    (templateFieldData.isVisibilityForAllParticipantsOn ||
      templateFieldData.isEmployeeOfLeadCustomer ||
      templateFieldData.isCADR) &&
    !isMirroredCalculatableField &&
    hasFieldGroupAccess &&
    (templateFieldData.isCADR || (canSeeRevision$ | async))
  ) {
    <ng-container *ngTemplateOutlet="revisionsMenuItemTemplate"></ng-container>
  }
  @if (
    ((!isGlobalRequiredField &&
      field?.key !== commissionFeeFieldKey &&
      (!isMirroredField || templateFieldData.isEmployeeOfLeadCustomer)) ||
      templateFieldData.isCADR) &&
    hasFieldGroupAccess
  ) {
    <ng-container *ngTemplateOutlet="deleteMenuItemTemplate"></ng-container>
  }
</ng-template>
<ng-template #deleteMenuItemTemplate>
  <button
    fin-menu-item
    attention="true"
    [size]="finSize.M"
    iconName="delete"
    (click)="onDeleteField()"
  >
    <ng-container finMenuItemTitle>
      <span i18n="@@folderStructure.folder.actions.delete">
        Löschen</span
      ></ng-container
    >
  </button></ng-template
>

<ng-template #revisionsMenuItemTemplate>
  <button
    fin-menu-item
    [size]="finSize.M"
    iconName="replay"
    (click)="onOpenRevisions()"
  >
    <ng-container finMenuItemTitle>
      <span i18n="@@facilityField.actionsMenu.item.revisions"
        >Revisionen</span
      ></ng-container
    >
  </button>
</ng-template>

<ng-template #editFieldMenuItemTemplate>
  <button
    fin-menu-item
    [size]="finSize.M"
    iconName="edit"
    (click)="onEditField()"
  >
    <ng-container finMenuItemTitle>
      <span i18n="@@facilityField.actionsMenu.item.editField"
        >Feld bearbeiten</span
      >
    </ng-container>
  </button>
</ng-template>

<ng-template #fieldHint>
  <span i18n="@@templateField.mirrored-field.participant"
    >Kann nur vom Fallinhaber bearbeitet werden</span
  >
</ng-template>
