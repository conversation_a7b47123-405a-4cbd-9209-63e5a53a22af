import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  EventEmitter,
  HostBinding,
  inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FileUploaderComponent } from '@fincloud/components/files';
import {
  FieldDtoWithIndex,
  FieldInputRequestState,
  InformationUtils,
} from '@fincloud/core/business-case';
import { BusinessCaseLocation, FieldTypeEnum } from '@fincloud/core/formly';
import { isTemplateRefEmpty } from '@fincloud/core/utils';
import { DocumentFieldManageHelperService } from '@fincloud/neoshare/document';
import { LocationService } from '@fincloud/neoshare/services';
import { selectHasBusinessCasePermission } from '@fincloud/state/business-case';
import { StateLibDocumentPageActions } from '@fincloud/state/document';
import {
  FieldDto,
  Information,
} from '@fincloud/swagger-generator/business-case-manager';
import { Information as CompanyInformation } from '@fincloud/swagger-generator/company';
import { NextFolderDocument } from '@fincloud/swagger-generator/document';
import {
  BusinessCasePermission,
  TemplateFieldViewMode,
} from '@fincloud/types/enums';
import {
  AccessRights,
  DataRoomTemplateFieldData,
  UploadFilesBreakdown,
  ValueChangeModel,
} from '@fincloud/types/models';
import { FinButtonActionType } from '@fincloud/ui/button';
import { FinSize } from '@fincloud/ui/types';
import {
  checkCustomerExplicitGroupVisibility,
  COMMISSION_FEE_FIELD_KEY,
  DATA_ROOM_DOCUMENTS_SECTION_ITEM_PREFIX,
  DATA_ROOM_TEMPLATE_FIELD_PREFIX,
  HIGHLIGHT_PREFIX,
} from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { cloneDeep, isEmpty, isEqual } from 'lodash-es';
import { Observable, of } from 'rxjs';

@Component({
  selector: 'ui-template-field',
  templateUrl: './template-field.component.html',
  styleUrls: ['./template-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TemplateFieldComponent
  implements OnInit, OnChanges, AfterViewInit
{
  @Input() field: FieldDtoWithIndex;
  @Input() information: Information | CompanyInformation;
  @Input() isEditMode: boolean;
  @Input() isSearchMode: boolean;
  @Input() isFilterMode: boolean;
  @Input() viewMode: TemplateFieldViewMode = TemplateFieldViewMode.DEFAULT;

  @Input() templateFieldData: DataRoomTemplateFieldData;

  @Input() nextfolderDocuments: NextFolderDocument[];
  @Input() isServiceSynchronizedWithNextfolder: boolean;
  @Input() hasDracoonSynchronization: boolean;

  @Output() openModalRequested = new EventEmitter<{
    modalTab: 'edit' | 'revisions';
    isMirroredField: boolean;
  }>();
  @Output() fieldCopy = new EventEmitter();
  @Output() valueChanged = new EventEmitter<ValueChangeModel>();
  @Output() fileUpload = new EventEmitter<ValueChangeModel>();
  @Output() fieldDeleted = new EventEmitter<ValueChangeModel>();
  @Output() moveModalOpen = new EventEmitter<FieldDto>();
  @Output() showInEnclosingFolder = new EventEmitter<string>();

  viewContainerRef = inject(ViewContainerRef);

  readonly finSize = FinSize;
  readonly finButtonActionType = FinButtonActionType;
  readonly fieldsTypes = FieldTypeEnum;
  readonly commissionFeeFieldKey = COMMISSION_FEE_FIELD_KEY;
  private readonly fieldRequestedStates: string[] = [
    FieldInputRequestState.REQUESTED,
    FieldInputRequestState.RE_REQUESTED,
  ];

  img$: Observable<string | ArrayBuffer>;
  isFieldEditable = true;
  accessRights: AccessRights;
  informationValue: string;
  AIAssistantSourceId: string;
  isFieldKebapActionsMenuEmpty: boolean;

  @ViewChild(FileUploaderComponent) fileUploader: FileUploaderComponent;
  @ViewChild('menuOptions', { static: false })
  menuOptionsRef: TemplateRef<unknown>;

  @HostBinding('class.full-span') isFullWidth = false;
  @HostBinding('class.tw-col-span-full') get isFullRowInGrid(): boolean {
    return (
      this.field.fieldType === this.fieldsTypes.LONG_TEXT ||
      this.field.fieldType === this.fieldsTypes.TABLE
    );
  }

  get fieldPlaceholderHeightRem(): number {
    if (this.field.fieldType === this.fieldsTypes.DOCUMENT) {
      return this.viewMode === TemplateFieldViewMode.DEFAULT
        ? 12.8 // Height of document cards in folder structure grid view
        : 4.4; // Height of document rows in folder structure list view
    }

    // Height of all inputs + their labels (except for long text & table fields)
    return 7.2;
  }

  get templateFieldPrefix(): string {
    return this.isFieldDocument
      ? DATA_ROOM_DOCUMENTS_SECTION_ITEM_PREFIX
      : DATA_ROOM_TEMPLATE_FIELD_PREFIX;
  }

  get hasFieldGroupAccess(): boolean {
    if (this.templateFieldData.isCADR) {
      return true;
    }
    const fieldGroup =
      this.templateFieldData.groupsWithoutFolderStructure?.find((g) =>
        g.fields.includes(this.field?.key),
      );

    return !checkCustomerExplicitGroupVisibility(
      fieldGroup,
      this.templateFieldData.userCustomerKey,
      this.templateFieldData.isEmployeeOfLeadCustomer,
    );
  }

  get uploadingFileBreakdown(): UploadFilesBreakdown {
    return this.templateFieldData.uploadingFilesBreakdownList?.find(
      ({ key }) => key === this.information?.field?.key,
    );
  }

  get isUploading(): boolean {
    return (
      this.uploadingFileBreakdown && this.uploadingFileBreakdown.isUploading
    );
  }

  get hasErrorDuringUpload(): boolean {
    return this.uploadingFileBreakdown && this.uploadingFileBreakdown.hasError;
  }

  get isGlobalRequiredField(): boolean {
    return this.templateFieldData.globalRequiredFieldKeys?.includes(
      this.field?.key,
    );
  }

  get isMirroredCalculatableField(): boolean {
    return this.templateFieldData.mirroredCalculatableFieldKeys?.includes(
      this.field?.key,
    );
  }

  get isMirroredField(): boolean {
    return this.templateFieldData.mirroredFieldKeys?.includes(this.field?.key);
  }

  get isParticipantPartOfChat(): boolean {
    const topicChat =
      this.templateFieldData.existingTopicChats?.[this.information.id] ||
      this.templateFieldData.existingArchivedTopicChats?.[this.information.id];

    return (
      topicChat &&
      (topicChat.chatCustomers?.some(
        (chatCustomer) =>
          chatCustomer.customerKey === this.templateFieldData.userCustomerKey,
      ) ||
        this.templateFieldData.caseLeaderCustomerKey ===
          this.templateFieldData.userCustomerKey)
    );
  }

  get isAIAssistantSourceSelected(): boolean {
    return (
      this.templateFieldData.selectedAIAssistantSourceId &&
      this.AIAssistantSourceId ===
        this.templateFieldData.selectedAIAssistantSourceId
    );
  }

  get isFieldRequested(): boolean {
    return this.fieldRequestedStates.includes(
      this.templateFieldData.caseFieldsParticipantInputsReqsDict?.[
        this.information.id
      ]?.state,
    );
  }

  get fieldRequestHighlighted(): boolean {
    return (
      !this.templateFieldData.isEmployeeOfLeadCustomer &&
      this.isFieldRequested &&
      !this.openedForModification
    );
  }

  canSeeRevision$ = this.store.select(
    selectHasBusinessCasePermission(BusinessCasePermission.BCP_00021),
  );

  get openedForModification(): boolean {
    return (
      this.templateFieldData.caseFieldsParticipantInputsReqsDict?.[
        this.information.id
      ]?.state === FieldInputRequestState.OPEN_FOR_MODIFICATION
    );
  }

  get isFieldInEditMode(): boolean {
    return (
      this.isEditMode ||
      ((this.isFieldRequested || this.openedForModification) &&
        !this.templateFieldData.isEmployeeOfLeadCustomer)
    );
  }

  get isFieldDocument(): boolean {
    return this.field?.fieldType === 'DOCUMENT';
  }

  get cantEditMirroredField(): boolean {
    return (
      this.isMirroredField &&
      !(
        this.templateFieldData.isEmployeeOfLeadCustomer ||
        this.isFieldRequested ||
        this.openedForModification
      )
    );
  }

  get showActionsMenu(): boolean {
    return (
      this.isFieldInEditMode &&
      (this.templateFieldData.isEmployeeOfLeadCustomer ||
        this.templateFieldData.hasDataRoomWriteAccess)
    );
  }

  constructor(
    private destroyRef: DestroyRef,
    private documentFieldManageHelperService: DocumentFieldManageHelperService,
    private store: Store,
    private locationService: LocationService,
  ) {}

  ngOnInit(): void {
    this.isFullWidth = ['LONG_TEXT', 'TABLE', 'COMPOSITE'].includes(
      this.field.fieldType,
    );

    if (this.information) {
      this.AIAssistantSourceId =
        HIGHLIGHT_PREFIX +
        (this.isFieldDocument ? this.information.value : this.information.id);
    }

    this.checkFieldEditable();
    this.checkInformationEmtptyValue();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!isEqual(this.informationValue, this.information?.value as string)) {
      this.informationValue = this.information?.value as string;
    }

    if (
      (changes.field && !changes.field?.firstChange) ||
      (changes.information && !changes.information?.firstChange)
    ) {
      this.checkInformationEmtptyValue();
    }

    if (
      changes?.information?.currentValue &&
      !isEqual(
        changes?.information?.currentValue,
        changes?.information?.previousValue,
      ) &&
      !isEmpty(this.information) &&
      this.isFieldDocument
    ) {
      this.getDocumentThumbnail(this.information?.value as string);
    }

    this.checkFieldEditable();
    this.isFieldKebapActionsMenuEmpty = isTemplateRefEmpty(
      this.menuOptionsRef,
      this.viewContainerRef,
    );
  }

  ngAfterViewInit(): void {
    if (this.isFieldDocument) {
      this.fileUploader.fileSelected
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe((files) => {
          this.handleFile(files[0]);
        });
    }
  }

  onValueSaved(value: string) {
    const currentRequest = [
      this.templateFieldData.caseFieldsParticipantInputsReqsDict?.[
        this.information.id
      ],
    ]?.find((cfir) =>
      [
        FieldInputRequestState.OPEN_FOR_MODIFICATION,
        FieldInputRequestState.REQUESTED,
        FieldInputRequestState.RE_REQUESTED,
      ].includes(cfir?.state as FieldInputRequestState),
    );

    const valueChangeParams = {
      value,
      information: this.information,
      dataRoomOwnerId: this.templateFieldData.dataRoomOwnerId,
      isFieldDocument: this.isFieldDocument,
      isPortalEdit: !!currentRequest,
      isFieldRequested: this.isFieldRequested,
      caseFieldInputRequestReason: currentRequest?.reason || '',
      fieldKey: this.field.key,
      requestType: currentRequest?.requestType,
      customerKey: currentRequest?.customerKey,
      participantUsersToBeNotified:
        currentRequest?.participantUsersToBeNotified,
      isLeadEdit: this.templateFieldData.isEmployeeOfLeadCustomer,
    } as ValueChangeModel;

    this.valueChanged.emit(valueChangeParams);
  }

  onCopyField() {
    this.fieldCopy.emit();
  }

  onEditField() {
    this.openModal('edit');
  }

  onOpenRevisions() {
    if (!this.information) {
      return;
    }

    this.openModal('revisions');
  }

  onDeleteField() {
    this.fieldDeleted.emit();
  }

  onShowInEnclosingFolder() {
    this.showInEnclosingFolder.emit(this.field.key);
  }

  private checkFieldEditable() {
    if (!this.information) {
      return;
    }

    const isCalculatable = this.information
      ? !!this.information.field?.expression?.length
      : !!this.field?.expression?.length;

    const isConditionToPreventEditMet =
      isCalculatable ||
      this.field.key === 'companyId' ||
      (this.field.key === 'commissionFee' &&
        this.templateFieldData.isCommissionFieldDisabled) ||
      this.cantEditMirroredField ||
      // mirrored key in the financing details is calculatable
      this.isMirroredCalculatableField;

    // on purpose with ternary because !isConditionToPreventEditingMet would be hard to understand
    // TODO: Combine with isFieldInEditMode
    this.isFieldEditable = !isConditionToPreventEditMet;
  }

  private checkInformationEmtptyValue() {
    if (InformationUtils.isEmptyValue(this.information?.value)) {
      this.information = cloneDeep(this.information);
      if (!this.information) {
        this.information = {
          value: null,
        };
      } else {
        this.information.value = null;
      }
      this.informationValue = null;
    }
  }

  private openModal(tab: 'edit' | 'revisions') {
    this.openModalRequested.emit({
      modalTab: tab,
      isMirroredField: this.isMirroredField,
    });
  }

  // Document field related logic

  openPreviewModal() {
    if (!this.information?.value) {
      return;
    }

    this.store.dispatch(
      StateLibDocumentPageActions.openPdfViewer({
        documentId: this.information?.value as string,
        fieldLabel: this.information?.field?.label,
      }),
    );
  }
  onMoveFile(documentField: FieldDto) {
    this.moveModalOpen.emit(documentField);
  }

  onDrop(file: File) {
    if (!this.isEditMode && !this.isFieldRequested) {
      return;
    }

    this.handleFile(file);
  }

  download(event: Event) {
    event.stopPropagation();
    this.documentFieldManageHelperService.downloadDocument(
      this.information?.value as string,
      this.information?.field?.label,
    );
  }

  selectFilesForUpload() {
    this.fileUploader.chooseFile();
  }

  private handleFile(file: File) {
    const currentRequest = [
      this.templateFieldData.caseFieldsParticipantInputsReqsDict?.[
        this.information.id
      ],
    ]?.find((cfir) =>
      [
        FieldInputRequestState.OPEN_FOR_MODIFICATION,
        FieldInputRequestState.REQUESTED,
        FieldInputRequestState.RE_REQUESTED,
      ].includes(cfir?.state as FieldInputRequestState),
    );

    const valueChangeParams = {
      value: file,
      information: this.information,
      fieldKey: this.information?.field?.key || this.field?.key,
      dataRoomOwnerId: this.templateFieldData.dataRoomOwnerId,
      isFieldDocument: this.isFieldDocument,
      isPortalEdit: !!currentRequest,
      isFieldRequested: this.isFieldRequested,
      caseFieldInputRequestReason: currentRequest?.reason || '',
      requestType: currentRequest?.requestType,
      customerKey: currentRequest?.customerKey,
      participantUsersToBeNotified:
        currentRequest?.participantUsersToBeNotified,
      isLeadEdit: this.templateFieldData.isEmployeeOfLeadCustomer,
    } as ValueChangeModel;

    this.fileUpload.emit(valueChangeParams);
  }

  private getDocumentThumbnail(documentId: string) {
    if (InformationUtils.isEmptyValue(documentId)) {
      this.img$ = of(null);

      return;
    }
    this.img$ =
      this.documentFieldManageHelperService.getDocumentThumbnail(documentId);
  }

  onLocation() {
    this.locationService.showOnMap(
      this.information.value as BusinessCaseLocation,
    );
  }
}
