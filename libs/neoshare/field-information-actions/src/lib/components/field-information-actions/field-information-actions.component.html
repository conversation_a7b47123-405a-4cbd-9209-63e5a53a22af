<div
  class="field-actions tw-flex tw-items-center"
  [ngClass]="isDocument ? 'tw-gap-[1.2rem]' : 'tw-gap-[0.8rem]'"
>
  <!-- FIELD DESCRIPTION -->
  @if (config.supportsFieldDescription) {
    @if (fieldDescription && !isDocument) {
      <button
        finTooltip
        fin-button-action
        class="!tw-cursor-default"
        [openDelay]="400"
        [content]="fieldDescription"
        [size]="finSize.S"
        [shape]="finButtonShape.RECTANGLE"
        [disabled]="!isEditMode"
      >
        <fin-icon
          name="info"
          [class.tw-text-color-icons-tertiary]="
            !isDocument || isUploadingDocument
          "
          [class.tw-text-color-icons-primary]="
            isDocument && !isUploadingDocument
          "
        ></fin-icon>
      </button>
    }
  }
  <!-- CHAT -->
  @if (
    information?.id &&
    canSeeTopicChats &&
    config?.supportsChat &&
    hasFieldGroupAccess &&
    !isMirroredField
  ) {
    @if (newTabPathForChat?.length) {
      <button
        fin-button-action
        [size]="finSize.S"
        [shape]="finButtonShape.RECTANGLE"
        [disabled]="!chatActionConfig?.clickable || isUploadingDocument"
        (click)="
          chatActionConfig.chatAction.bind(this).call('');
          $event.stopPropagation()
        "
      >
        <fin-icon
          [src]="'assets/svg/' + chatActionConfig?.iconName + '.svg'"
          [class.tw-text-color-icons-tertiary]="
            !isDocument || isUploadingDocument
          "
          [class.tw-text-color-icons-primary]="
            isDocument && !isUploadingDocument
          "
        ></fin-icon>
      </button>
    }
  }

  @if (!isMirroredCalculatableField) {
    <!-- PARTICIPANT REQUEST FIELD ACTIONS -->
    @if (
      config?.supportsParticipantFieldRequest &&
      (canRequestInformationFromOrganization$ | async)
    ) {
      @if (isLeadCustomer && businessCaseParticipants?.length > 1) {
        <button
          finTooltip
          fin-button-action
          [class.!tw-cursor-default]="!isEditMode"
          [content]="
            !participantRequestDisabled ? participantRequestTooltip : null
          "
          [size]="finSize.S"
          [shape]="finButtonShape.RECTANGLE"
          [disabled]="!isEditMode || isUploadingDocument"
          (click)="openRequestFieldParticipantModal(); $event.stopPropagation()"
        >
          <fin-icon
            [src]="'assets/svg/' + participantRequestStateIcon + '.svg'"
            [class.tw-text-color-icons-tertiary]="
              !isDocument || isUploadingDocument
            "
            [class.tw-text-color-icons-primary]="
              isDocument && !isUploadingDocument
            "
          ></fin-icon>
        </button>
      }
    }
  }

  <!-- DOCUMENT SPECIFIC ACTIONS -->
  @if (isDocument) {
    @if (documentId) {
      <button
        fin-button-action
        [disabled]="isUploadingDocument"
        [size]="finSize.S"
        [shape]="finButtonShape.RECTANGLE"
        (click)="onDownload($event)"
      >
        <fin-icon
          name="download"
          [class.tw-text-color-icons-tertiary]="isUploadingDocument"
          [class.tw-text-color-icons-primary]="!isUploadingDocument"
        ></fin-icon>
      </button>
    }
    @if (
      (openedForModification || isFieldParticipantRequested) &&
      !isLeadCustomer &&
      !hasDataRoomWriteAccess
    ) {
      <button
        fin-button-action
        [size]="finSize.S"
        [shape]="finButtonShape.RECTANGLE"
        (click)="onUpload()"
        [disabled]="isUploadingDocument"
      >
        <fin-icon
          name="file_upload"
          [class.tw-text-color-icons-tertiary]="isUploadingDocument"
          [class.tw-text-color-icons-primary]="!isUploadingDocument"
        >
        </fin-icon>
      </button>
    }
  }
  <!-- DOCUMENT ACTIONS For Apps-->
  @if (
    isDocument && information?.value && isServiceSynchronizedWithNextfolder
  ) {
    <ng-container
      [ngTemplateOutlet]="iconTemplate"
      [ngTemplateOutletContext]="{ iconInfo: getDocumentIconName() }"
    >
    </ng-container>
  }

  @if (isDocument && information?.value && hasDracoonSynchronization) {
    @if (documentId) {
      <fin-badge-app
        [type]="finBadgeAppType.DRACCOON"
        [status]="finBadgeAppStatus.INFORMATION"
      ></fin-badge-app>
    }
  }
  <!-- REQUESTED FROM PERSPECTIVE TOOLTIP -->
  @if (isFieldParticipantRequested && !isLeadCustomer) {
    <button
      finTooltip
      fin-button-action
      class="!tw-cursor-default"
      [content]="portalRequestedTooltip"
      [disabled]="!isEditMode"
      [size]="finSize.S"
      [shape]="finButtonShape.RECTANGLE"
    >
      <fin-icon
        src="assets/svg/svgAssignmentAdd.svg"
        [class.tw-text-color-icons-tertiary]="
          !isDocument || isUploadingDocument
        "
        [class.tw-text-color-icons-primary]="isDocument && !isUploadingDocument"
      ></fin-icon>
    </button>
  }
  <!-- FIELD MIRRORED -->
  @if (isMirroredField && isLeadCustomer) {
    <button
      finTooltip
      fin-button-action
      class="!tw-cursor-default"
      [openDelay]="400"
      [content]="compareIconHoverText"
      [size]="finSize.S"
      [shape]="finButtonShape.RECTANGLE"
      [disabled]="!isEditMode"
    >
      <fin-icon
        name="compare_arrows"
        [class.tw-text-color-icons-tertiary]="
          !isDocument || isUploadingDocument
        "
        [class.tw-text-color-icons-primary]="isDocument && !isUploadingDocument"
      ></fin-icon>
    </button>
  }
</div>

<ng-template #participantRequestTooltip>
  <div class="tw-text-start">
    <div class="tw-flex tw-gap-1">
      <span>{{ participantRequestTitle }}</span>
      <span class="tw-font-bold">{{ participantRequestAdditionalInfo }}</span>
    </div>
    <div>{{ participantRequestAdditionalInfoFooter }}</div>
  </div>
</ng-template>

<ng-template #portalRequestedTooltip>
  <div class="tw-text-start">
    {{ tooltipTranslation(this.currentRequestedFieldReason) }}
  </div>
</ng-template>

<ng-template #iconTemplate let-iconInfo="iconInfo">
  <fin-badge-app
    finTooltip
    [openDelay]="400"
    [content]="iconInfo.tooltipName"
    [status]="iconInfo.status"
  ></fin-badge-app>
</ng-template>
