import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { NsCoreHotkeysModule } from '@fincloud/core/hotkeys';
import { NsCoreLayoutModule } from '@fincloud/core/layout';
import { sideNavigationsFeature } from '@fincloud/state/side-navigations';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinObserversModule } from '@fincloud/ui/observers';
import { FinSeparatorsModule } from '@fincloud/ui/separators';
import { FinSidePanelModule } from '@fincloud/ui/side-panel';
import { FinTooltipModule } from '@fincloud/ui/tooltip';
import { provideState } from '@ngrx/store';
import { InfiniteScrollDirective } from 'ngx-infinite-scroll';
import { NgScrollbarModule } from 'ngx-scrollbar';
import { FooterComponent } from './components/footer/footer.component';
import { InfiniteScrollComponent } from './components/infinite-scroll/infinite-scroll.component';
import { LayoutToggleButtonComponent } from './components/layout-toggle-button/layout-toggle-button.component';
import { PageRibbonComponent } from './components/page-ribbon/page-ribbon.component';
import { SidebarContentManagerComponent } from './components/sidebar-content-manager/sidebar-content-manager.component';

@NgModule({
  imports: [
    CommonModule,
    NgScrollbarModule,
    InfiniteScrollDirective,
    RouterModule,
    FinSidePanelModule,
    NsCoreLayoutModule,
    FinButtonModule,
    FinIconModule,
    FinTooltipModule,
    NsCoreHotkeysModule,
    FinSeparatorsModule,
    FinObserversModule,
  ],
  declarations: [
    FooterComponent,
    InfiniteScrollComponent,
    PageRibbonComponent,
    SidebarContentManagerComponent,
    LayoutToggleButtonComponent,
  ],
  exports: [
    FooterComponent,
    InfiniteScrollComponent,
    PageRibbonComponent,
    SidebarContentManagerComponent,
    LayoutToggleButtonComponent,
  ],
  providers: [provideState(sideNavigationsFeature)],
})
export class NsUiLayoutModule {}
