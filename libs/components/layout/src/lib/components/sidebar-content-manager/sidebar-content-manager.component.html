<ng-template #openSidebarTemplate>
  <div>
    <span i18n="@@navigationPanel.expand.btn.tooltip">Erweitern</span>
  </div>
</ng-template>

@if (sideSectionLeft) {
  <div
    class="tw-absolute tw-z-[2] tw-h-full tw-pt-10 tw-w-[1.4rem]"
    [@openClose]="pagePanelIsOpen ? 'opened' : 'closed'"
    (click)="!pagePanelIsOpen && toggleButtonClicked()"
  >
    <div
      finTooltip
      positionTarget="ui-layout-toggle-button"
      placement="right"
      [content]="openSidebarTemplate"
      class="tw-absolute tw-top-0 tw-w-full tw-h-full tw-z-[1] tw-cursor-pointer hover:tw-bg-color-surface-hover"
      [class.tw-pointer-events-none]="pagePanelIsOpen"
    ></div>
    <!-- Secondary tabs: 4.8rem, top padding: 2.4rem, half of switch height: 1.6rem  -->
    <ui-layout-toggle-button
      id="ui-layout-toggle-button"
      class="tw-sticky tw-inline-block tw-top-[calc(4.8rem+2.4rem+1.6rem)] tw-z-[2]"
      [isOpenState]="pagePanelIsOpen"
      (toggle)="toggleButtonClicked()"
    >
    </ui-layout-toggle-button>
  </div>
}

<fin-side-panel
  #sideNavContainer
  [open]="pagePanelIsOpen"
  [partiallyOpenSize]="partiallyOpenSize"
  [fixedInViewport]="false"
  [fixSticky]="true"
  class="tw-h-full"
>
  <ng-template finSidePanelSlide>
    <div
      class="tw-transition-[height] tw-duration-[400ms] tw-h-full tw-flex tw-flex-col tw-w-[30rem] tw-bg-color-surface-primary"
      [ngStyle]="sidePanelStyles"
      [class.tw-pl-12]="!pagePanelIsOpen"
    >
      <ng-container [ngTemplateOutlet]="sideSectionLeft"></ng-container>
    </div>
  </ng-template>

  <ng-template finSidePanelContent>
    <div class="tw-flex tw-w-full tw-h-full">
      <div
        class="tw-h-full tw-flex tw-w-full tw-flex-col tw-border-l-[0.1rem] tw-bg-color-surface-primary tw-items-stretch tw-relative"
        [class.!tw-w-[calc(100%-30rem)]]="sideSectionRight"
        [finObserveResizeDisabled]="!observeMainPanelResize"
        (finObserveResize)="onMainPanelResize($event)"
      >
        <ng-container [ngTemplateOutlet]="mainSectionPanel"></ng-container>
      </div>
      <div
        class="tw-transition-[height] tw-duration-[400ms] tw-flex tw-h-full tw-max-w-[30rem] tw-bg-color-surface-primary"
        [ngStyle]="sidePanelStyles"
      >
        <ng-container [ngTemplateOutlet]="sideSectionRight"></ng-container>
      </div>
    </div>
  </ng-template>
</fin-side-panel>
