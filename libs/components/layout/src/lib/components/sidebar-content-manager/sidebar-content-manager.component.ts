import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { NgStyle } from '@angular/common';
import {
  Component,
  ContentChild,
  Input,
  Output,
  TemplateRef,
} from '@angular/core';
import { StateLibSideNavigationsPageActions } from '@fincloud/state/side-navigations';
import { Store } from '@ngrx/store';
import { Subject, debounceTime } from 'rxjs';

@Component({
  selector: 'app-sidebar-content-manager',
  templateUrl: './sidebar-content-manager.component.html',
  styleUrl: './sidebar-content-manager.component.scss',
  animations: [
    trigger('openClose', [
      state(
        'closed',
        style({
          left: '16rem',
          transform: 'translateX(-16rem)',
        }),
      ),
      state(
        'opened',
        style({
          left: '28.7rem',
          transform: 'translateX(0rem)',
        }),
      ),
      transition('closed <=> opened', [animate('0.2s')]),
    ]),
  ],
})
export class SidebarContentManagerComponent {
  @ContentChild('navPanel')
  sideSectionLeft?: TemplateRef<unknown>;

  @ContentChild('rightPanel')
  sideSectionRight?: TemplateRef<unknown>;

  @ContentChild('mainPanel')
  mainSectionPanel?: TemplateRef<unknown>;

  @ContentChild('rightPanelButton')
  rightSectionButton?: TemplateRef<unknown>;

  private toggle$$ = new Subject<void>();

  @Input() partiallyOpenSize = 14;
  @Input() pagePanelIsOpen: boolean;
  @Input() fixSticky = true;
  @Input() sidePanelStyles: NgStyle['ngStyle'] = {};
  @Input() observeMainPanelResize = false;

  @Output() toggle = this.toggle$$.pipe(debounceTime(10));

  constructor(private store: Store) {}

  toggleButtonClicked() {
    this.toggle$$.next();
  }

  onMainPanelResize(resizeObserverEntry: ResizeObserverEntry): void {
    this.store.dispatch(
      StateLibSideNavigationsPageActions.setMainPanelWidth({
        width: resizeObserverEntry.contentRect.width,
      }),
    );
  }
}
