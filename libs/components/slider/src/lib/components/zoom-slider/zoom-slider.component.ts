import { ChangeContext, Options } from '@angular-slider/ngx-slider';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import { FinSeparator } from '@fincloud/ui/separators';
import { FinSize } from '@fincloud/ui/types';
import { merge } from 'lodash-es';

@Component({
  selector: 'ui-zoom-slider',
  templateUrl: './zoom-slider.component.html',
  styleUrls: ['./zoom-slider.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ZoomSliderComponent implements OnInit {
  @Input()
  options: Options;

  @Input()
  value = 1;

  @Input()
  isFullScreen = false;

  @Input()
  isMinimapShown = false;

  @Output()
  changed = new EventEmitter<number>();

  @Output()
  toggleMinimap = new EventEmitter<void>();

  manualRefresh: EventEmitter<void> = new EventEmitter<void>();

  showMinimapIconTooltip = $localize`:@@companyGraph.showMinimap.tooltip:Minimap anzeigen`;
  hideMinimapIconTooltip = $localize`:@@companyGraph.hideMinimap.tooltip:Minimap verbergen`;
  defaultOptions: Options = {
    floor: 0.1,
    ceil: 2,
    step: 0.1,
    showTicks: false,
  };

  sliderOptions: Options;
  finSize = FinSize;
  finSeparator = FinSeparator;
  finButtonShape = FinButtonShape;
  finButtonAppearance = FinButtonAppearance;

  get tooltipContainer(): string {
    return this.isFullScreen ? '#canvas' : 'body';
  }

  ngOnInit(): void {
    this.sliderOptions = merge(this.defaultOptions, this.options);
  }

  onChange(event: ChangeContext): void {
    this.changed.emit(event.value);
    this.value = event.value;
  }

  onZoomIn(): void {
    if (this.value + this.sliderOptions.step <= this.sliderOptions.ceil) {
      this.value += this.sliderOptions.step;
    }
    this.changed.emit(this.value);
  }

  onZoomOut(): void {
    if (this.value - this.sliderOptions.step >= this.sliderOptions.floor) {
      this.value -= this.sliderOptions.step;
    }
    this.changed.emit(this.value);
  }

  onToggleMinimap(): void {
    this.toggleMinimap.emit();
  }
}
