@use 'styles/src/lib/common';
@mixin empty-data-room() {
  border-radius: common.$border-radius;
  padding: 2.4rem 3rem;
  display: flex;
  gap: 3rem;
  align-items: center;
  @include common.heading6;
  color: theme('colors.color-background-dark-moderate');
}

@mixin ui-tooltip() {
  display: flex;
  align-items: center;
  margin-left: 1.2rem;
  border-radius: common.$border-radius;
  width: 2.1rem;
  height: 1.9rem;
  padding: 0 2px;
  margin-top: 1px;
}

@mixin margin-padding-for-fields-and-filter() {
  app-business-case-template-fields {
    padding-left: 2rem;
  }

  ui-search-filter {
    margin-bottom: 2rem;
  }
}

@mixin groups-fields-container() {
  .section-header-wrapper {
    display: flex;
    align-items: center;
  }

  .section-title-overview {
    display: flex;
    align-items: center;
    width: 100%;

    @include common.heading2();
    margin: 1rem 0;

    span {
      white-space: nowrap;
    }
  }

  .section-title-divider {
    width: 100%;
    margin-left: 1.2rem;
  }

  .group-container {
    position: relative;
    margin-bottom: 4.5rem;

    .empty-group-placeholder {
      height: 18rem;
      margin-top: 1.2rem;

      .inner {
        width: 100%;
        height: 100%;
        border: 1px dashed theme('colors.color-background-dark-subtle');
        border-radius: 1.2rem;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        .text-main {
          @include common.heading4();
        }

        .text-sub {
          @include common.heading8();
          color: theme('colors.color-background-dark-moderate');
        }
      }
    }

    .nextfolder-additional-height {
      height: 25rem;
    }
  }

  ui-horizontal-divider.documents {
    margin-top: 1rem;
    margin-bottom: 2.5rem;
  }
}

@mixin group-container-overlay() {
  .overlay {
    display: none;
    position: absolute;
    width: calc(100% + 3rem);
    height: calc(100% + 3rem);
    border-radius: common.$border-radius;
    border: 0.1rem dashed theme('colors.color-brand-dark');
    left: -1.5rem;
    top: -1.5rem;

    &.hovered {
      @include common.flex-justify();

      .background {
        position: absolute;
        opacity: 0.8;
        border-radius: common.$border-radius;
        background-color: theme('colors.color-brand-dark');
        width: calc(100% - 2.5rem);
        height: calc(100% - 2.5rem);
        // z-index: 2;
      }

      .dragzone-info {
        @include common.flex-justify();
        flex-direction: column;
        color: theme('colors.color-surface-primary');
        // z-index: 3;

        .group-title {
          @include common.heading1();
        }

        .sub-text {
          @include common.heading6();
          margin-bottom: 2rem;
        }
      }
    }
  }
}

@mixin fields-container() {
  .fields-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 1.6rem;

    .field {
      width: 100%;
    }

    .full-span {
      grid-column: span 2;
    }
  }

  .no-result {
    @include common.flex-justify();
    @include common.heading4();
    padding-bottom: 2.4rem;
  }

  .no-visible-fields {
    @include common.heading6();
    color: theme('colors.color-background-dark-moderate');
    display: flex;
    align-items: center;
    gap: 1.2rem;
  }

  .empty-cadr-linked {
    @include common.card(false);
    padding: 2.4rem;
    display: flex;
    gap: 3rem;
    align-items: center;
    @include common.heading6;
    color: theme('colors.color-background-dark-moderate');
  }
}

@mixin template-container() {
  @include margin-padding-for-fields-and-filter();

  .groups-fields-container {
    @include groups-fields-container();

    .group-container {
      @include group-container-overlay();
    }
  }

  @include fields-container();

  .document-inbox {
    position: sticky;
    bottom: 0;
    width: 100%;
  }

  .copy {
    cursor: pointer;
    margin-left: 1.2rem;
  }
}
