import { Inject, Injectable, LOCALE_ID, Optional } from '@angular/core';
// import dayjs from 'dayjs';
import { formatDate } from '@angular/common';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { Locale } from '@fincloud/types/enums';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import * as dayjs from 'dayjs';
import * as de from 'dayjs/locale/de';
import * as relativeTime from 'dayjs/plugin/relativeTime';
import * as updateLocale from 'dayjs/plugin/updateLocale';
import * as weekday from 'dayjs/plugin/weekday';
import { SupportedDateValue } from '../models/supported-date-value';
import { DATE_FORMAT_CONFIG } from '../utils/date-format-config';
import { PAST_TIME_SUFFIX } from '../utils/past-time-suffix';

// import { DEFAULT_DATE_FORMAT } from './extend.datepipe';

@Injectable({
  providedIn: 'root',
})
export class DateService {
  private dayJS = dayjs;
  constructor(
    @Inject(PAST_TIME_SUFFIX) private pastTimeSuffix: string,
    @Optional() @Inject(DATE_FORMAT_CONFIG) private timeConfig = {},
    @Inject(LOCALE_ID) private locale: Locale,
    private regionalSettings: RegionalSettingsService,
  ) {
    this.dayJS.extend(weekday);
    this.dayJS.extend(relativeTime);
    this.dayJS.extend(updateLocale);

    this.setLocale(this.locale ?? 'de');
  }

  private setLocale(locale: string) {
    // TODO: separate locale configs instead of overriding DE settings for EN
    this.dayJS.locale(de);

    this.dayJS.updateLocale('de', {
      relativeTime: {
        ...de.relativeTime,
        ...this.getRelativeTimeConfig(),
        past: (
          number: number,
          _withoutSuffix: boolean,
          _key: string,
          _isFuture: boolean,
        ) => {
          return `${this.pastTimeSuffix} ${number}`;
        },
        ...this.timeConfig,
      },
      weekdays: [
        $localize`:@@dashboard.activityLogs.weekdays.sunday:Sonntag`,
        $localize`:@@dashboard.activityLogs.weekdays.monday:Montag`,
        $localize`:@@dashboard.activityLogs.weekdays.tuesday:Dienstag`,
        $localize`:@@dashboard.activityLogs.weekdays.wednesday:Mittwoch`,
        $localize`:@@dashboard.activityLogs.weekdays.thursday:Donnerstag`,
        $localize`:@@dashboard.activityLogs.weekdays.friday:Freitag`,
        $localize`:@@dashboard.activityLogs.weekdays.saturday:Samstag`,
      ],
    });
  }

  private getRelativeTimeConfig() {
    if (this.locale === 'en') {
      return {
        // relative time format strings, keep %s %d as the same
        s: 'a few seconds',
        m: 'a minute',
        mm: '%d minutes',
        h: 'an hour',
        hh: '%d hours', // e.g. 2 hours, %d been replaced with 2
        d: 'a day',
        dd: '%d days',
        M: 'a month',
        MM: '%d months',
        y: 'a year',
        yy: '%d years',
      };
    }

    return {
      s: 'ein paar Sekunden',
      m: 'eine Minute',
      mm: '%d Minuten',
      h: 'einer Stunde',
      hh: '%d Stunden',
      d: 'einem Tag',
      dd: '%d Tagen',
      M: 'einem Monat',
      MM: '%d Monaten',
      y: 'einem Jahr',
      yy: '%d Jahren',
    };
  }

  /**
   * Format a date in the past in terms of time passed
   *
   * @param {(SupportedDateValue)} dateValue
   * @return {*}  {string}
   */
  fromNow(dateValue: SupportedDateValue): string {
    return this.dayJS(dateValue).fromNow();
  }

  /**
   *  Subtract a specific number of days from a given date (defaults to now)
   *
   * @param {number} days
   * @return {*}  {Date}
   */
  subtractDays(
    days: number,
    dateValue: SupportedDateValue = this.dayJS(),
  ): Date {
    return this.dayJS(dateValue).subtract(days, 'day').toDate();
  }

  /**
   * Convert epoch time (timestamp) to Date
   *
   * @param {number} timestamp
   * @return {*}  {Date}
   */
  timestampToDate(timestamp: number): Date {
    return this.dayJS.unix(timestamp).toDate();
  }

  /**
   * Get days difference between passed dates
   *
   * @param {Date} date
   * @return {*}  {number}
   */
  getAbsoluteDifferenceBetweenDates(
    date1: SupportedDateValue,
    date2: SupportedDateValue,
    timeInterval: dayjs.UnitTypeLongPlural = 'milliseconds',
  ): number {
    return Math.abs(this.dayJS(date1).diff(date2, timeInterval));
  }

  getDifferenceBetweenDates(
    date1: SupportedDateValue,
    date2: SupportedDateValue,
    timeInterval: dayjs.UnitTypeLongPlural = 'milliseconds',
    floatingPointNumber = false,
  ): number {
    return this.dayJS(date1).diff(date2, timeInterval, floatingPointNumber);
  }

  getConsecutiveDayDifferenceFromToday(date: SupportedDateValue) {
    const now = this.dayJS()
      .set('hours', 0)
      .set('minutes', 0)
      .set('seconds', 0);

    const futureDate = this.dayJS(date)
      .set('hours', 0)
      .set('minutes', 0)
      .set('seconds', 0);

    return Math.round(Math.abs(futureDate.diff(now, 'days', true)));
  }

  getCurrentDate() {
    return this.dayJS();
  }

  dateToString(dateValue: SupportedDateValue): string {
    return this.dayJS(dateValue).toISOString();
  }

  getMillisecondsDifference(
    date1: SupportedDateValue,
    date2: SupportedDateValue,
  ): number {
    return Math.abs(this.dayJS(date1).diff(date2, 'milliseconds'));
  }

  // Convert date format to: yyyy-mm-dd
  convertedDate(date: string) {
    const parts = date.split(/[-/.]/);
    const convertedDate = `${parts[2]}-${parts[1]?.padStart(2, '0')}-${parts[0]?.padStart(2, '0')}`;
    return convertedDate;
  }

  // Remove any character that is not a digit or a decimal point
  removeNonNumericCharacters(dateValue: string) {
    if (!dateValue) {
      return null;
    }

    const newDateValue = dateValue.replace(/[^0-9./]/g, '');
    return newDateValue;
  }

  dateToISOString() {
    return this.dayJS().toDate().toISOString();
  }

  todaysDate(): Date {
    return this.dayJS().toDate();
  }

  toCompanyDocumentDate(dateValue: string): Date {
    // Failed to do it by providing format
    const [day, month, year] = dateValue.split('.');
    return this.dayJS().day(+day).month(+month).year(+year).toDate();
  }

  toDate(dateValue: SupportedDateValue): Date {
    return this.dayJS(dateValue).toDate();
  }

  addDays(dateValue: SupportedDateValue, days: number): Date {
    return this.dayJS(dateValue).add(days, 'day').toDate();
  }

  addDaysFromToday(days: number): Date {
    return this.dayJS().add(days, 'day').toDate();
  }

  formatForExport(dateValue: SupportedDateValue) {
    return this.dayJS(dateValue).format('DD.MM.YYYY');
  }

  formatDate(dateValue: SupportedDateValue, dateFormat: string) {
    return this.dayJS(dateValue).locale(this.locale).format(dateFormat);
  }

  transformDate(date: string): string {
    return date
      ? formatDate(date, this.regionalSettings.dateFormat, this.locale)
      : '';
  }

  // Used to set min and max dates in input fields.
  calculateAdjustedDate(days?: number): string {
    if (days) {
      return dayjs().add(days, 'day').format('YYYY-MM-DD');
    }
    return dayjs().format('YYYY-MM-DD');
  }

  isValidDate(dateValue: string) {
    const dateRegex =
      /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])([T\s](\d{2}):(\d{2}):(\d{2})(Z|[+-]\d{2}:\d{2})?)?$/;
    return dateRegex.test(dateValue);
  }

  addTimeZoneInfo(dateValue: string): string {
    const isUTC =
      dateValue.toString().includes('UTC') ||
      dateValue.toString().includes('Z');
    const isGMT = dateValue.toString().includes('GMT');
    const valueUTC = !isUTC && !isGMT ? `${dateValue.toString()}Z` : dateValue;
    return valueUTC;
  }

  isSameOrBefore(
    dateValue: SupportedDateValue,
    otherDateValue: SupportedDateValue,
  ) {
    const date = this.dayJS(dateValue);
    const otherDate = this.dayJS(otherDateValue);
    return date.diff(otherDate, 'minutes') <= 0;
  }

  getWeekday(timestamp: number) {
    const dayNumber = this.dayJS(this.timestampToDate(timestamp)).weekday();
    return this.dayJS().weekday(dayNumber).format('dddd');
  }

  getTodayDateISO8601() {
    return this.dayJS().toISOString();
  }

  getLastMonthDateISO8601() {
    return this.dayJS().subtract(30, 'day').endOf('day').toISOString();
  }

  getLastYearDateISO8601() {
    return this.dayJS().subtract(1, 'year').endOf('day').toISOString();
  }

  getCalendarStartDate(calendarDate: string) {
    return this.dayJS(calendarDate).startOf('day').toISOString();
  }

  getCalendarEndDate(calendarDate: string) {
    return this.dayJS(calendarDate).endOf('day').toISOString();
  }

  getFormattedTimeNgbDateStruct(dateTimeString: string): NgbDateStruct {
    const dateTime = new Date(dateTimeString);
    const year = dateTime.getFullYear();
    const month = dateTime.getMonth() + 1; // Adding 1 because getMonth() returns 0-indexed months (0 for January)
    const day = dateTime.getDate();

    const result = { year, month, day };
    return result;
  }

  isDateInRange(
    dateToCheck: NgbDateStruct,
    endDate: NgbDateStruct,
    monthsBack: number,
  ): boolean {
    const dateToCheckObject = new Date(
      dateToCheck.year,
      dateToCheck.month - monthsBack,
      dateToCheck.day,
    );

    // Subtract months back from dateToCheck
    const oneMonthBeforeDateToCheck = new Date(dateToCheckObject);
    oneMonthBeforeDateToCheck.setMonth(
      oneMonthBeforeDateToCheck.getMonth() - monthsBack,
    );

    // Create a new Date object for endDate
    const endDateObject = new Date(
      endDate.year,
      endDate.month - monthsBack,
      endDate.day,
    );

    // Check if endDate is greater than months back before dateToCheck
    return endDateObject > oneMonthBeforeDateToCheck;
  }

  isTimestampToday(timestamp: number) {
    const millisecondsInDay = 24 * 60 * 60 * 1000;

    return dayjs().diff(timestamp) <= millisecondsInDay - 59 * 60 * 1000;
  }

  isTimestampYesterday(timestamp: number): boolean {
    const milliseconds = 24 * 60 * 60 * 1000 * 2;
    return dayjs().diff(timestamp) <= milliseconds - 59 * 60 * 1000;
  }

  formatNgbDateStruct(date: NgbDateStruct): string {
    const year = date.year.toString().padStart(4, '0');
    const month = date.month.toString().padStart(2, '0');
    const day = date.day.toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
}
