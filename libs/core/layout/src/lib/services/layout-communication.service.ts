import { EventEmitter, Injectable, TemplateRef } from '@angular/core';
import { BehaviorSubject, Observable, ReplaySubject, Subject } from 'rxjs';
import { SidebarLayoutSection } from '../enums/sidebar-layout-section';
import { SidebarLayoutConfiguration } from '../models/sidebar-layout-configuration';

@Injectable({
  providedIn: 'root',
})
export class LayoutCommunicationService {
  private sidebarLayoutConfiguration$$ =
    new Subject<SidebarLayoutConfiguration>();
  sidebarLayoutConfiguration$: Observable<SidebarLayoutConfiguration> =
    this.sidebarLayoutConfiguration$$.asObservable();

  private scrollToTopEvent$$ = new ReplaySubject<void>(1);
  scrollToTopEvent$: Observable<void> = this.scrollToTopEvent$$.asObservable();

  private layoutChanged$$ = new Subject<void>();
  layoutChanged$: Observable<void> = this.layoutChanged$$.asObservable();

  private layoutClosed$$ = new Subject<void>();
  layoutClosed$: Observable<void> = this.layoutClosed$$.asObservable();

  private toggleRightSideOverlayPanel$$ = new Subject<void>();
  toggleRightSideOverlayPanel$ =
    this.toggleRightSideOverlayPanel$$.asObservable();

  private scrollY$$ = new BehaviorSubject(0);
  scrollY$ = this.scrollY$$.asObservable();

  public lockScrollEvent = new EventEmitter<number>();

  emitSidebarLayoutConfiguration(config: SidebarLayoutConfiguration) {
    this.sidebarLayoutConfiguration$$.next(config);
  }

  setRightOverlayPanelTemplate(template: TemplateRef<unknown>): void {
    this.sidebarLayoutConfiguration$$.next({
      side: SidebarLayoutSection.RIGHT_SIDE_OVERLAY_PANEL,
      template,
    });
  }

  clearRightOverlayPanelTemplate(): void {
    this.sidebarLayoutConfiguration$$.next({
      side: SidebarLayoutSection.RIGHT_SIDE_OVERLAY_PANEL,
      template: null,
    });
  }

  emitScrollToTopEvent() {
    this.scrollToTopEvent$$.next();
  }

  lockScroll(duration = 100) {
    this.lockScrollEvent.emit(duration);
  }

  layoutChanged() {
    this.layoutChanged$$.next();
  }

  layoutClosed() {
    this.layoutClosed$$.next();
  }

  toggleRightSideOverlayPanel() {
    this.toggleRightSideOverlayPanel$$.next();
  }

  setLayoutScrollYPosition(position: number) {
    this.scrollY$$.next(position);
  }
}
