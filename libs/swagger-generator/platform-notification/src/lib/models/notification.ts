/* tslint:disable */
/* eslint-disable */
export interface Notification {
  creationDate?: string;
  hidden?: boolean;
  id?: string;
  initiatorUserId?: string;
  lastModifiedDate?: string;
  message?: string;
  parameters?: {
[key: string]: string;
};
  read?: boolean;
  receiverUserId?: string;
  seen?: boolean;
  timestamp?: string;
  type?: 'CHAT_RELATED' | 'DATA_ROOM' | 'CADR' | 'BUSINESS_CASE' | 'UPLOAD_DOCUMENT' | 'USER_ASSIGNMENT' | 'COMPANY_UPDATE';
}
