/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';


export interface TurnOnNotificationsForType$Params {
  userId: string;
  type: 'CHAT_RELATED' | 'DATA_ROOM' | 'CADR' | 'BUSINESS_CASE' | 'UPLOAD_DOCUMENT' | 'USER_ASSIGNMENT' | 'COMPANY_UPDATE';
}

export function turnOnNotificationsForType(http: HttpClient, rootUrl: string, params: TurnOnNotificationsForType$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
  const rb = new RequestBuilder(rootUrl, turnOnNotificationsForType.PATH, 'patch');
  if (params) {
    rb.path('userId', params.userId, {});
    rb.query('type', params.type, {});
  }

  return http.request(
    rb.build({ responseType: 'text', accept: '*/*', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return (r as HttpResponse<any>).clone({ body: undefined }) as StrictHttpResponse<void>;
    })
  );
}

turnOnNotificationsForType.PATH = '/preference/turn-on-notification-for-type/{userId}';
