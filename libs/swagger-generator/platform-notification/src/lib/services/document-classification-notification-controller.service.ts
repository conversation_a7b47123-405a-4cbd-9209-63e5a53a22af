/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { notifyDocumentClassificationReady } from '../fn/document-classification-notification-controller/notify-document-classification-ready';
import { NotifyDocumentClassificationReady$Params } from '../fn/document-classification-notification-controller/notify-document-classification-ready';

@Injectable({ providedIn: 'root' })
export class DocumentClassificationNotificationControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `notifyDocumentClassificationReady()` */
  static readonly NotifyDocumentClassificationReadyPath = '/internal/document-classification/notify';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `notifyDocumentClassificationReady()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  notifyDocumentClassificationReady$Response(params: NotifyDocumentClassificationReady$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return notifyDocumentClassificationReady(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `notifyDocumentClassificationReady$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  notifyDocumentClassificationReady(params: NotifyDocumentClassificationReady$Params, context?: HttpContext): Observable<void> {
    return this.notifyDocumentClassificationReady$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

}
