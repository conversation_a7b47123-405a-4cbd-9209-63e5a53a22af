/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { AddFieldAndEditGroupRequest } from '../../models/add-field-and-edit-group-request';
import { BusinessCase } from '../../models/business-case';

export interface EditAndMergeBusinessCaseGroups$Params {
  businessCaseId: string;
      body: AddFieldAndEditGroupRequest
}

export function editAndMergeBusinessCaseGroups(http: HttpClient, rootUrl: string, params: EditAndMergeBusinessCaseGroups$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
  const rb = new RequestBuilder(rootUrl, editAndMergeBusinessCaseGroups.PATH, 'patch');
  if (params) {
    rb.path('businessCaseId', params.businessCaseId, {});
    rb.body(params.body, 'application/json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<BusinessCase>;
    })
  );
}

editAndMergeBusinessCaseGroups.PATH = '/business-case/{businessCaseId}/add-field-and-edit-and-merge-groups';
