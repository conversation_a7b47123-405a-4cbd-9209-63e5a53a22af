/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { AddFieldAndEditGroupRequest } from '../../models/add-field-and-edit-group-request';
import { BusinessCase } from '../../models/business-case';

export interface AddFieldAndEditBusinessCaseGroups$Params {
  businessCaseId: string;
      body: AddFieldAndEditGroupRequest
}

export function addFieldAndEditBusinessCaseGroups(http: HttpClient, rootUrl: string, params: AddFieldAndEditBusinessCaseGroups$Params, context?: HttpContext): Observable<StrictHttpResponse<BusinessCase>> {
  const rb = new RequestBuilder(rootUrl, addFieldAndEditBusinessCaseGroups.PATH, 'put');
  if (params) {
    rb.path('businessCaseId', params.businessCaseId, {});
    rb.body(params.body, 'application/json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<BusinessCase>;
    })
  );
}

addFieldAndEditBusinessCaseGroups.PATH = '/business-case/{businessCaseId}/add-field-and-edit-groups';
