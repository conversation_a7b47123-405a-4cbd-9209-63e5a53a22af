/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { MigrationResult } from '../../models/migration-result';

export interface MigrateUngroupedFieldsInCases$Params {
  securityCode: string;
}

export function migrateUngroupedFieldsInCases(http: HttpClient, rootUrl: string, params: MigrateUngroupedFieldsInCases$Params, context?: HttpContext): Observable<StrictHttpResponse<MigrationResult>> {
  const rb = new RequestBuilder(rootUrl, migrateUngroupedFieldsInCases.PATH, 'post');
  if (params) {
    rb.query('securityCode', params.securityCode, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<MigrationResult>;
    })
  );
}

migrateUngroupedFieldsInCases.PATH = '/migration/remove-ungrouped-fields/business-cases';
