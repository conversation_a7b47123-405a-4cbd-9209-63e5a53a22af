/* tslint:disable */
/* eslint-disable */
import { <PERSON><PERSON><PERSON><PERSON>, ModuleWithProviders, <PERSON><PERSON><PERSON><PERSON>, Optional } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiConfiguration, ApiConfigurationParams } from './api-configuration';

import { FaqControllerService } from './services/faq-controller.service';
import { TemplateControllerService } from './services/template-controller.service';
import { MigrationFolderStructureInTemplateControllerService } from './services/migration-folder-structure-in-template-controller.service';
import { FacilitiesInternalControllerService } from './services/facilities-internal-controller.service';
import { InformationControllerService } from './services/information-controller.service';
import { FacilitiesControllerService } from './services/facilities-controller.service';
import { BusinessCaseControllerService } from './services/business-case-controller.service';
import { ParticipantControllerService } from './services/participant-controller.service';
import { FolderControllerService } from './services/folder-controller.service';
import { CasePermissionControllerService } from './services/case-permission-controller.service';
import { MigrationUngroupedFieldsControllerService } from './services/migration-ungrouped-fields-controller.service';
import { MigrationNeoGptInformationUpdateControllerService } from './services/migration-neo-gpt-information-update-controller.service';
import { MigrationDeleteCasesControllerService } from './services/migration-delete-cases-controller.service';
import { ManagerControllerService } from './services/manager-controller.service';
import { TransferLeadershipControllerService } from './services/transfer-leadership-controller.service';
import { EngineTestControllerService } from './services/engine-test-controller.service';
import { InterestedCustomersInternalControllerService } from './services/interested-customers-internal-controller.service';
import { InformationInternalControllerService } from './services/information-internal-controller.service';
import { FieldHistoryControllerService } from './services/field-history-controller.service';
import { CaseContextInformationInternalControllerService } from './services/case-context-information-internal-controller.service';
import { BusinessCaseInternalControllerService } from './services/business-case-internal-controller.service';
import { ParticipantInternalControllerService } from './services/participant-internal-controller.service';
import { InterestedCustomersControllerService } from './services/interested-customers-controller.service';
import { DuplicateBusinessCaseControllerService } from './services/duplicate-business-case-controller.service';
import { DocumentClassificationControllerService } from './services/document-classification-controller.service';
import { SingleClusterDemoControllerService } from './services/single-cluster-demo-controller.service';
import { ContactPersonControllerService } from './services/contact-person-controller.service';
import { ExportControllerService } from './services/export-controller.service';
import { CaseContextInformationControllerService } from './services/case-context-information-controller.service';
import { PhysicalDeletionControllerService } from './services/physical-deletion-controller.service';

/**
 * Module that provides all services and configuration.
 */
@NgModule({
  imports: [],
  exports: [],
  declarations: [],
  providers: [
    FaqControllerService,
    TemplateControllerService,
    MigrationFolderStructureInTemplateControllerService,
    FacilitiesInternalControllerService,
    InformationControllerService,
    FacilitiesControllerService,
    BusinessCaseControllerService,
    ParticipantControllerService,
    FolderControllerService,
    CasePermissionControllerService,
    MigrationUngroupedFieldsControllerService,
    MigrationNeoGptInformationUpdateControllerService,
    MigrationDeleteCasesControllerService,
    ManagerControllerService,
    TransferLeadershipControllerService,
    EngineTestControllerService,
    InterestedCustomersInternalControllerService,
    InformationInternalControllerService,
    FieldHistoryControllerService,
    CaseContextInformationInternalControllerService,
    BusinessCaseInternalControllerService,
    ParticipantInternalControllerService,
    InterestedCustomersControllerService,
    DuplicateBusinessCaseControllerService,
    DocumentClassificationControllerService,
    SingleClusterDemoControllerService,
    ContactPersonControllerService,
    ExportControllerService,
    CaseContextInformationControllerService,
    PhysicalDeletionControllerService,
    ApiConfiguration
  ],
})
export class ApiModule {
  static forRoot(params: ApiConfigurationParams): ModuleWithProviders<ApiModule> {
    return {
      ngModule: ApiModule,
      providers: [
        {
          provide: ApiConfiguration,
          useValue: params
        }
      ]
    }
  }

  constructor( 
    @Optional() @SkipSelf() parentModule: ApiModule,
    @Optional() http: HttpClient
  ) {
    if (parentModule) {
      throw new Error('ApiModule is already loaded. Import in your base AppModule only.');
    }
    if (!http) {
      throw new Error('You need to import the HttpClientModule in your AppModule! \n' +
      'See also https://github.com/angular/angular/issues/20575');
    }
  }
}
