/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { migrateUngroupedFieldsInCases } from '../fn/migration-ungrouped-fields-controller/migrate-ungrouped-fields-in-cases';
import { MigrateUngroupedFieldsInCases$Params } from '../fn/migration-ungrouped-fields-controller/migrate-ungrouped-fields-in-cases';
import { migrateUngroupedFieldsInTemplates } from '../fn/migration-ungrouped-fields-controller/migrate-ungrouped-fields-in-templates';
import { MigrateUngroupedFieldsInTemplates$Params } from '../fn/migration-ungrouped-fields-controller/migrate-ungrouped-fields-in-templates';
import { MigrationResult } from '../models/migration-result';

@Injectable({ providedIn: 'root' })
export class MigrationUngroupedFieldsControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `migrateUngroupedFieldsInTemplates()` */
  static readonly MigrateUngroupedFieldsInTemplatesPath = '/migration/remove-ungrouped-fields/templates';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrateUngroupedFieldsInTemplates()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateUngroupedFieldsInTemplates$Response(params: MigrateUngroupedFieldsInTemplates$Params, context?: HttpContext): Observable<StrictHttpResponse<MigrationResult>> {
    return migrateUngroupedFieldsInTemplates(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrateUngroupedFieldsInTemplates$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateUngroupedFieldsInTemplates(params: MigrateUngroupedFieldsInTemplates$Params, context?: HttpContext): Observable<MigrationResult> {
    return this.migrateUngroupedFieldsInTemplates$Response(params, context).pipe(
      map((r: StrictHttpResponse<MigrationResult>): MigrationResult => r.body)
    );
  }

  /** Path part for operation `migrateUngroupedFieldsInCases()` */
  static readonly MigrateUngroupedFieldsInCasesPath = '/migration/remove-ungrouped-fields/business-cases';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrateUngroupedFieldsInCases()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateUngroupedFieldsInCases$Response(params: MigrateUngroupedFieldsInCases$Params, context?: HttpContext): Observable<StrictHttpResponse<MigrationResult>> {
    return migrateUngroupedFieldsInCases(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrateUngroupedFieldsInCases$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateUngroupedFieldsInCases(params: MigrateUngroupedFieldsInCases$Params, context?: HttpContext): Observable<MigrationResult> {
    return this.migrateUngroupedFieldsInCases$Response(params, context).pipe(
      map((r: StrictHttpResponse<MigrationResult>): MigrationResult => r.body)
    );
  }

}
