/* tslint:disable */
/* eslint-disable */
import { ContentReference } from '../models/content-reference';
import { OwnerReference } from '../models/owner-reference';
export interface Document {
  businessCaseId?: string;
  companyId?: string;
  contentBase64?: string;
  contentReference?: ContentReference;
  createdOn?: string;
  digest?: string;
  hasError?: boolean;
  id?: string;
  isContainedInBusinessCase?: boolean;
  isContainedInInbox?: boolean;
  isForwardedToInbox?: boolean;
  isWarningIgnored?: boolean;
  ownerReference?: OwnerReference;
  realmKey?: string;
  state?: 'DELETED' | 'TEMPORARY' | 'CONTENT_UPLOAD_ONGOING' | 'CONTENT_UPLOAD_FAILED' | 'FINAL';
  userCustomerKey?: string;
  userId?: string;
}
