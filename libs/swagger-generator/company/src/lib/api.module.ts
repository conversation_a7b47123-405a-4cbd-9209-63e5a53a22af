/* tslint:disable */
/* eslint-disable */
import { Ng<PERSON><PERSON><PERSON>, ModuleWithProviders, <PERSON><PERSON><PERSON><PERSON>, Optional } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiConfiguration, ApiConfigurationParams } from './api-configuration';

import { MigrationFolderStructureInCadrTemplateControllerService } from './services/migration-folder-structure-in-cadr-template-controller.service';
import { CompanyInternalControllerService } from './services/company-internal-controller.service';
import { InformationControllerService } from './services/information-controller.service';
import { CompanyContactPersonControllerService } from './services/company-contact-person-controller.service';
import { CompanyControllerService } from './services/company-controller.service';
import { CompanyProfileControllerService } from './services/company-profile-controller.service';
import { CompanyBranchControllerService } from './services/company-branch-controller.service';
import { CadrTemplateControllerService } from './services/cadr-template-controller.service';
import { MigrationUngroupedFieldsControllerService } from './services/migration-ungrouped-fields-controller.service';
import { PalturaiNetworkDownloadMigrationControllerService } from './services/palturai-network-download-migration-controller.service';
import { MigrationContactPersonService } from './services/migration-contact-person.service';
import { CadrSharingInternalControllerService } from './services/cadr-sharing-internal-controller.service';
import { SingleClusterDemoControllerService } from './services/single-cluster-demo-controller.service';
import { CompanyGraphStateControllerService } from './services/company-graph-state-controller.service';
import { PalturaiConnectApiControllerService } from './services/palturai-connect-api-controller.service';
import { CadrSharingControllerService } from './services/cadr-sharing-controller.service';
import { CompanyRegistryCityCodeMigrationControllerService } from './services/company-registry-city-code-migration-controller.service';
import { PalturaiInternalControllerService } from './services/palturai-internal-controller.service';
import { InformationInternalControllerService } from './services/information-internal-controller.service';
import { CompanyContactPersonInternalControllerService } from './services/company-contact-person-internal-controller.service';
import { NorthDataInternalControllerService } from './services/north-data-internal-controller.service';
import { IndustryControllerService } from './services/industry-controller.service';
import { NorthDataControllerService } from './services/north-data-controller.service';

/**
 * Module that provides all services and configuration.
 */
@NgModule({
  imports: [],
  exports: [],
  declarations: [],
  providers: [
    MigrationFolderStructureInCadrTemplateControllerService,
    CompanyInternalControllerService,
    InformationControllerService,
    CompanyContactPersonControllerService,
    CompanyControllerService,
    CompanyProfileControllerService,
    CompanyBranchControllerService,
    CadrTemplateControllerService,
    MigrationUngroupedFieldsControllerService,
    PalturaiNetworkDownloadMigrationControllerService,
    MigrationContactPersonService,
    CadrSharingInternalControllerService,
    SingleClusterDemoControllerService,
    CompanyGraphStateControllerService,
    PalturaiConnectApiControllerService,
    CadrSharingControllerService,
    CompanyRegistryCityCodeMigrationControllerService,
    PalturaiInternalControllerService,
    InformationInternalControllerService,
    CompanyContactPersonInternalControllerService,
    NorthDataInternalControllerService,
    IndustryControllerService,
    NorthDataControllerService,
    ApiConfiguration
  ],
})
export class ApiModule {
  static forRoot(params: ApiConfigurationParams): ModuleWithProviders<ApiModule> {
    return {
      ngModule: ApiModule,
      providers: [
        {
          provide: ApiConfiguration,
          useValue: params
        }
      ]
    }
  }

  constructor( 
    @Optional() @SkipSelf() parentModule: ApiModule,
    @Optional() http: HttpClient
  ) {
    if (parentModule) {
      throw new Error('ApiModule is already loaded. Import in your base AppModule only.');
    }
    if (!http) {
      throw new Error('You need to import the HttpClientModule in your AppModule! \n' +
      'See also https://github.com/angular/angular/issues/20575');
    }
  }
}
