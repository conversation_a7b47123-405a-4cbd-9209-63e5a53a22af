/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { AddFieldAndEditGroupRequest } from '../../models/add-field-and-edit-group-request';
import { Company } from '../../models/company';

export interface AddFieldAndEditCompanyGroups$Params {
  companyId: string;
      body: AddFieldAndEditGroupRequest
}

export function addFieldAndEditCompanyGroups(http: HttpClient, rootUrl: string, params: AddFieldAndEditCompanyGroups$Params, context?: HttpContext): Observable<StrictHttpResponse<Company>> {
  const rb = new RequestBuilder(rootUrl, addFieldAndEditCompanyGroups.PATH, 'put');
  if (params) {
    rb.path('companyId', params.companyId, {});
    rb.body(params.body, 'application/json');
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<Company>;
    })
  );
}

addFieldAndEditCompanyGroups.PATH = '/company/{companyId}/add-field-and-edit-groups';
