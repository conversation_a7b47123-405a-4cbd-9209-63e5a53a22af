/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { Summary } from '../../models/summary';

export interface Migrate1$Params {
  securityCode: string;
}

export function migrate1(http: HttpClient, rootUrl: string, params: Migrate1$Params, context?: HttpContext): Observable<StrictHttpResponse<Summary>> {
  const rb = new RequestBuilder(rootUrl, migrate1.PATH, 'get');
  if (params) {
    rb.query('securityCode', params.securityCode, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<Summary>;
    })
  );
}

migrate1.PATH = '/migration/company-register-city-code';
