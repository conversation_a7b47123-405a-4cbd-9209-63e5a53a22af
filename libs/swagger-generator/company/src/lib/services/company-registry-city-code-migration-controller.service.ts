/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { migrate1 } from '../fn/company-registry-city-code-migration-controller/migrate-1';
import { Migrate1$Params } from '../fn/company-registry-city-code-migration-controller/migrate-1';
import { Summary } from '../models/summary';

@Injectable({ providedIn: 'root' })
export class CompanyRegistryCityCodeMigrationControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `migrate1()` */
  static readonly Migrate1Path = '/migration/company-register-city-code';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrate1()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrate1$Response(params: Migrate1$Params, context?: HttpContext): Observable<StrictHttpResponse<Summary>> {
    return migrate1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrate1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrate1(params: Migrate1$Params, context?: HttpContext): Observable<Summary> {
    return this.migrate1$Response(params, context).pipe(
      map((r: StrictHttpResponse<Summary>): Summary => r.body)
    );
  }

}
