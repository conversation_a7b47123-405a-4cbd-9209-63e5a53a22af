/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { addFieldAndEditCompanyGroups } from '../fn/company-controller/add-field-and-edit-company-groups';
import { AddFieldAndEditCompanyGroups$Params } from '../fn/company-controller/add-field-and-edit-company-groups';
import { addFieldToCompany } from '../fn/company-controller/add-field-to-company';
import { AddFieldToCompany$Params } from '../fn/company-controller/add-field-to-company';
import { AttachCompanyTemplateResponse } from '../models/attach-company-template-response';
import { attachTemplate } from '../fn/company-controller/attach-template';
import { AttachTemplate$Params } from '../fn/company-controller/attach-template';
import { Company } from '../models/company';
import { createCompany } from '../fn/company-controller/create-company';
import { CreateCompany$Params } from '../fn/company-controller/create-company';
import { deleteCompany } from '../fn/company-controller/delete-company';
import { DeleteCompany$Params } from '../fn/company-controller/delete-company';
import { findCompany } from '../fn/company-controller/find-company';
import { FindCompany$Params } from '../fn/company-controller/find-company';
import { findCompanyForInternalType } from '../fn/company-controller/find-company-for-internal-type';
import { FindCompanyForInternalType$Params } from '../fn/company-controller/find-company-for-internal-type';
import { getCompanies } from '../fn/company-controller/get-companies';
import { GetCompanies$Params } from '../fn/company-controller/get-companies';
import { getCompaniesForCustomer } from '../fn/company-controller/get-companies-for-customer';
import { GetCompaniesForCustomer$Params } from '../fn/company-controller/get-companies-for-customer';
import { getCompaniesWithSameRegister } from '../fn/company-controller/get-companies-with-same-register';
import { GetCompaniesWithSameRegister$Params } from '../fn/company-controller/get-companies-with-same-register';
import { getCompanyById } from '../fn/company-controller/get-company-by-id';
import { GetCompanyById$Params } from '../fn/company-controller/get-company-by-id';
import { getCompanyByIdForCompanyContactPerson } from '../fn/company-controller/get-company-by-id-for-company-contact-person';
import { GetCompanyByIdForCompanyContactPerson$Params } from '../fn/company-controller/get-company-by-id-for-company-contact-person';
import { getCorrespondingCompanyForCustomer1 } from '../fn/company-controller/get-corresponding-company-for-customer-1';
import { GetCorrespondingCompanyForCustomer1$Params } from '../fn/company-controller/get-corresponding-company-for-customer-1';
import { reactivateDeletedCompany } from '../fn/company-controller/reactivate-deleted-company';
import { ReactivateDeletedCompany$Params } from '../fn/company-controller/reactivate-deleted-company';
import { updateCompany } from '../fn/company-controller/update-company';
import { UpdateCompany$Params } from '../fn/company-controller/update-company';

@Injectable({ providedIn: 'root' })
export class CompanyControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `getCompanyById()` */
  static readonly GetCompanyByIdPath = '/company/{id}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCompanyById()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCompanyById$Response(params: GetCompanyById$Params, context?: HttpContext): Observable<StrictHttpResponse<Company>> {
    return getCompanyById(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCompanyById$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCompanyById(params: GetCompanyById$Params, context?: HttpContext): Observable<Company> {
    return this.getCompanyById$Response(params, context).pipe(
      map((r: StrictHttpResponse<Company>): Company => r.body)
    );
  }

  /** Path part for operation `updateCompany()` */
  static readonly UpdateCompanyPath = '/company/{id}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `updateCompany()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  updateCompany$Response(params: UpdateCompany$Params, context?: HttpContext): Observable<StrictHttpResponse<Company>> {
    return updateCompany(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `updateCompany$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  updateCompany(params: UpdateCompany$Params, context?: HttpContext): Observable<Company> {
    return this.updateCompany$Response(params, context).pipe(
      map((r: StrictHttpResponse<Company>): Company => r.body)
    );
  }

  /** Path part for operation `deleteCompany()` */
  static readonly DeleteCompanyPath = '/company/{id}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `deleteCompany()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteCompany$Response(params: DeleteCompany$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return deleteCompany(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `deleteCompany$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  deleteCompany(params: DeleteCompany$Params, context?: HttpContext): Observable<void> {
    return this.deleteCompany$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `addFieldAndEditCompanyGroups()` */
  static readonly AddFieldAndEditCompanyGroupsPath = '/company/{companyId}/add-field-and-edit-groups';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addFieldAndEditCompanyGroups()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addFieldAndEditCompanyGroups$Response(params: AddFieldAndEditCompanyGroups$Params, context?: HttpContext): Observable<StrictHttpResponse<Company>> {
    return addFieldAndEditCompanyGroups(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addFieldAndEditCompanyGroups$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  addFieldAndEditCompanyGroups(params: AddFieldAndEditCompanyGroups$Params, context?: HttpContext): Observable<Company> {
    return this.addFieldAndEditCompanyGroups$Response(params, context).pipe(
      map((r: StrictHttpResponse<Company>): Company => r.body)
    );
  }

  /** Path part for operation `getCompanies()` */
  static readonly GetCompaniesPath = '/company';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCompanies()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCompanies$Response(params?: GetCompanies$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<Company>>> {
    return getCompanies(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCompanies$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCompanies(params?: GetCompanies$Params, context?: HttpContext): Observable<Array<Company>> {
    return this.getCompanies$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<Company>>): Array<Company> => r.body)
    );
  }

  /** Path part for operation `createCompany()` */
  static readonly CreateCompanyPath = '/company';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `createCompany()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createCompany$Response(params: CreateCompany$Params, context?: HttpContext): Observable<StrictHttpResponse<Company>> {
    return createCompany(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `createCompany$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  createCompany(params: CreateCompany$Params, context?: HttpContext): Observable<Company> {
    return this.createCompany$Response(params, context).pipe(
      map((r: StrictHttpResponse<Company>): Company => r.body)
    );
  }

  /** Path part for operation `attachTemplate()` */
  static readonly AttachTemplatePath = '/company/{companyId}/attach-template';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `attachTemplate()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  attachTemplate$Response(params: AttachTemplate$Params, context?: HttpContext): Observable<StrictHttpResponse<AttachCompanyTemplateResponse>> {
    return attachTemplate(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `attachTemplate$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   */
  attachTemplate(params: AttachTemplate$Params, context?: HttpContext): Observable<AttachCompanyTemplateResponse> {
    return this.attachTemplate$Response(params, context).pipe(
      map((r: StrictHttpResponse<AttachCompanyTemplateResponse>): AttachCompanyTemplateResponse => r.body)
    );
  }

  /** Path part for operation `addFieldToCompany()` */
  static readonly AddFieldToCompanyPath = '/company/{companyId}/add-field';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `addFieldToCompany()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   *
   * @deprecated
   */
  addFieldToCompany$Response(params: AddFieldToCompany$Params, context?: HttpContext): Observable<StrictHttpResponse<Company>> {
    return addFieldToCompany(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `addFieldToCompany$Response()` instead.
   *
   * This method sends `application/json` and handles request body of type `application/json`.
   *
   * @deprecated
   */
  addFieldToCompany(params: AddFieldToCompany$Params, context?: HttpContext): Observable<Company> {
    return this.addFieldToCompany$Response(params, context).pipe(
      map((r: StrictHttpResponse<Company>): Company => r.body)
    );
  }

  /** Path part for operation `reactivateDeletedCompany()` */
  static readonly ReactivateDeletedCompanyPath = '/company/reactivate/{companyId}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `reactivateDeletedCompany()` instead.
   *
   * This method doesn't expect any request body.
   */
  reactivateDeletedCompany$Response(params: ReactivateDeletedCompany$Params, context?: HttpContext): Observable<StrictHttpResponse<Company>> {
    return reactivateDeletedCompany(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `reactivateDeletedCompany$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  reactivateDeletedCompany(params: ReactivateDeletedCompany$Params, context?: HttpContext): Observable<Company> {
    return this.reactivateDeletedCompany$Response(params, context).pipe(
      map((r: StrictHttpResponse<Company>): Company => r.body)
    );
  }

  /** Path part for operation `getCorrespondingCompanyForCustomer1()` */
  static readonly GetCorrespondingCompanyForCustomer1Path = '/company/{originCompanyId}/corresponding-company-for-customer/{customerKey}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCorrespondingCompanyForCustomer1()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCorrespondingCompanyForCustomer1$Response(params: GetCorrespondingCompanyForCustomer1$Params, context?: HttpContext): Observable<StrictHttpResponse<Company>> {
    return getCorrespondingCompanyForCustomer1(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCorrespondingCompanyForCustomer1$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCorrespondingCompanyForCustomer1(params: GetCorrespondingCompanyForCustomer1$Params, context?: HttpContext): Observable<Company> {
    return this.getCorrespondingCompanyForCustomer1$Response(params, context).pipe(
      map((r: StrictHttpResponse<Company>): Company => r.body)
    );
  }

  /** Path part for operation `getCompaniesWithSameRegister()` */
  static readonly GetCompaniesWithSameRegisterPath = '/company/{companyId}/companies-with-same-register';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCompaniesWithSameRegister()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCompaniesWithSameRegister$Response(params: GetCompaniesWithSameRegister$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<Company>>> {
    return getCompaniesWithSameRegister(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCompaniesWithSameRegister$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCompaniesWithSameRegister(params: GetCompaniesWithSameRegister$Params, context?: HttpContext): Observable<Array<Company>> {
    return this.getCompaniesWithSameRegister$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<Company>>): Array<Company> => r.body)
    );
  }

  /** Path part for operation `findCompany()` */
  static readonly FindCompanyPath = '/company/search';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `findCompany()` instead.
   *
   * This method doesn't expect any request body.
   */
  findCompany$Response(params: FindCompany$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<Company>>> {
    return findCompany(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `findCompany$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  findCompany(params: FindCompany$Params, context?: HttpContext): Observable<Array<Company>> {
    return this.findCompany$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<Company>>): Array<Company> => r.body)
    );
  }

  /** Path part for operation `findCompanyForInternalType()` */
  static readonly FindCompanyForInternalTypePath = '/company/search-for-internal-customer-type';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `findCompanyForInternalType()` instead.
   *
   * This method doesn't expect any request body.
   */
  findCompanyForInternalType$Response(params: FindCompanyForInternalType$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<Company>>> {
    return findCompanyForInternalType(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `findCompanyForInternalType$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  findCompanyForInternalType(params: FindCompanyForInternalType$Params, context?: HttpContext): Observable<Array<Company>> {
    return this.findCompanyForInternalType$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<Company>>): Array<Company> => r.body)
    );
  }

  /** Path part for operation `getCompaniesForCustomer()` */
  static readonly GetCompaniesForCustomerPath = '/company/customer';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCompaniesForCustomer()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCompaniesForCustomer$Response(params?: GetCompaniesForCustomer$Params, context?: HttpContext): Observable<StrictHttpResponse<Array<Company>>> {
    return getCompaniesForCustomer(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCompaniesForCustomer$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  getCompaniesForCustomer(params?: GetCompaniesForCustomer$Params, context?: HttpContext): Observable<Array<Company>> {
    return this.getCompaniesForCustomer$Response(params, context).pipe(
      map((r: StrictHttpResponse<Array<Company>>): Array<Company> => r.body)
    );
  }

  /** Path part for operation `getCompanyByIdForCompanyContactPerson()` */
  static readonly GetCompanyByIdForCompanyContactPersonPath = '/company/company-name-search/{id}';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `getCompanyByIdForCompanyContactPerson()` instead.
   *
   * This method doesn't expect any request body.
   *
   * @deprecated
   */
  getCompanyByIdForCompanyContactPerson$Response(params: GetCompanyByIdForCompanyContactPerson$Params, context?: HttpContext): Observable<StrictHttpResponse<Company>> {
    return getCompanyByIdForCompanyContactPerson(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `getCompanyByIdForCompanyContactPerson$Response()` instead.
   *
   * This method doesn't expect any request body.
   *
   * @deprecated
   */
  getCompanyByIdForCompanyContactPerson(params: GetCompanyByIdForCompanyContactPerson$Params, context?: HttpContext): Observable<Company> {
    return this.getCompanyByIdForCompanyContactPerson$Response(params, context).pipe(
      map((r: StrictHttpResponse<Company>): Company => r.body)
    );
  }

}
