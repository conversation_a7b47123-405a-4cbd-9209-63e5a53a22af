/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { BaseService } from '../base-service';
import { ApiConfiguration } from '../api-configuration';
import { StrictHttpResponse } from '../strict-http-response';

import { checkComplexFields } from '../fn/migration-folder-structure-in-cadr-template-controller/check-complex-fields';
import { CheckComplexFields$Params } from '../fn/migration-folder-structure-in-cadr-template-controller/check-complex-fields';
import { fetchFieldsWithoutGroupsForCadrTemplate } from '../fn/migration-folder-structure-in-cadr-template-controller/fetch-fields-without-groups-for-cadr-template';
import { FetchFieldsWithoutGroupsForCadrTemplate$Params } from '../fn/migration-folder-structure-in-cadr-template-controller/fetch-fields-without-groups-for-cadr-template';
import { fetchFieldsWithoutGroupsForCompany } from '../fn/migration-folder-structure-in-cadr-template-controller/fetch-fields-without-groups-for-company';
import { FetchFieldsWithoutGroupsForCompany$Params } from '../fn/migration-folder-structure-in-cadr-template-controller/fetch-fields-without-groups-for-company';
import { migrateFolderStructureInCompany } from '../fn/migration-folder-structure-in-cadr-template-controller/migrate-folder-structure-in-company';
import { MigrateFolderStructureInCompany$Params } from '../fn/migration-folder-structure-in-cadr-template-controller/migrate-folder-structure-in-company';
import { migrateFolderStructureInTemplate } from '../fn/migration-folder-structure-in-cadr-template-controller/migrate-folder-structure-in-template';
import { MigrateFolderStructureInTemplate$Params } from '../fn/migration-folder-structure-in-cadr-template-controller/migrate-folder-structure-in-template';
import { MigrationResult } from '../models/migration-result';
import { replaceSpecialCharactersInInformationsAndRevisions } from '../fn/migration-folder-structure-in-cadr-template-controller/replace-special-characters-in-informations-and-revisions';
import { ReplaceSpecialCharactersInInformationsAndRevisions$Params } from '../fn/migration-folder-structure-in-cadr-template-controller/replace-special-characters-in-informations-and-revisions';

@Injectable({ providedIn: 'root' })
export class MigrationFolderStructureInCadrTemplateControllerService extends BaseService {
  constructor(config: ApiConfiguration, http: HttpClient) {
    super(config, http);
  }

  /** Path part for operation `replaceSpecialCharactersInInformationsAndRevisions()` */
  static readonly ReplaceSpecialCharactersInInformationsAndRevisionsPath = '/migration/folder/replace-special-characters-informations-and-revisions';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `replaceSpecialCharactersInInformationsAndRevisions()` instead.
   *
   * This method doesn't expect any request body.
   */
  replaceSpecialCharactersInInformationsAndRevisions$Response(params: ReplaceSpecialCharactersInInformationsAndRevisions$Params, context?: HttpContext): Observable<StrictHttpResponse<void>> {
    return replaceSpecialCharactersInInformationsAndRevisions(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `replaceSpecialCharactersInInformationsAndRevisions$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  replaceSpecialCharactersInInformationsAndRevisions(params: ReplaceSpecialCharactersInInformationsAndRevisions$Params, context?: HttpContext): Observable<void> {
    return this.replaceSpecialCharactersInInformationsAndRevisions$Response(params, context).pipe(
      map((r: StrictHttpResponse<void>): void => r.body)
    );
  }

  /** Path part for operation `migrateFolderStructureInCompany()` */
  static readonly MigrateFolderStructureInCompanyPath = '/migration/folder/company-template';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrateFolderStructureInCompany()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateFolderStructureInCompany$Response(params: MigrateFolderStructureInCompany$Params, context?: HttpContext): Observable<StrictHttpResponse<MigrationResult>> {
    return migrateFolderStructureInCompany(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrateFolderStructureInCompany$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateFolderStructureInCompany(params: MigrateFolderStructureInCompany$Params, context?: HttpContext): Observable<MigrationResult> {
    return this.migrateFolderStructureInCompany$Response(params, context).pipe(
      map((r: StrictHttpResponse<MigrationResult>): MigrationResult => r.body)
    );
  }

  /** Path part for operation `migrateFolderStructureInTemplate()` */
  static readonly MigrateFolderStructureInTemplatePath = '/migration/folder/cadr-template';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `migrateFolderStructureInTemplate()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateFolderStructureInTemplate$Response(params: MigrateFolderStructureInTemplate$Params, context?: HttpContext): Observable<StrictHttpResponse<MigrationResult>> {
    return migrateFolderStructureInTemplate(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `migrateFolderStructureInTemplate$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  migrateFolderStructureInTemplate(params: MigrateFolderStructureInTemplate$Params, context?: HttpContext): Observable<MigrationResult> {
    return this.migrateFolderStructureInTemplate$Response(params, context).pipe(
      map((r: StrictHttpResponse<MigrationResult>): MigrationResult => r.body)
    );
  }

  /** Path part for operation `checkComplexFields()` */
  static readonly CheckComplexFieldsPath = '/migration/folder/complex-fields';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `checkComplexFields()` instead.
   *
   * This method doesn't expect any request body.
   */
  checkComplexFields$Response(params: CheckComplexFields$Params, context?: HttpContext): Observable<StrictHttpResponse<MigrationResult>> {
    return checkComplexFields(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `checkComplexFields$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  checkComplexFields(params: CheckComplexFields$Params, context?: HttpContext): Observable<MigrationResult> {
    return this.checkComplexFields$Response(params, context).pipe(
      map((r: StrictHttpResponse<MigrationResult>): MigrationResult => r.body)
    );
  }

  /** Path part for operation `fetchFieldsWithoutGroupsForCompany()` */
  static readonly FetchFieldsWithoutGroupsForCompanyPath = '/migration/folder/company-fetch-fields-without-groups';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `fetchFieldsWithoutGroupsForCompany()` instead.
   *
   * This method doesn't expect any request body.
   */
  fetchFieldsWithoutGroupsForCompany$Response(params: FetchFieldsWithoutGroupsForCompany$Params, context?: HttpContext): Observable<StrictHttpResponse<MigrationResult>> {
    return fetchFieldsWithoutGroupsForCompany(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `fetchFieldsWithoutGroupsForCompany$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  fetchFieldsWithoutGroupsForCompany(params: FetchFieldsWithoutGroupsForCompany$Params, context?: HttpContext): Observable<MigrationResult> {
    return this.fetchFieldsWithoutGroupsForCompany$Response(params, context).pipe(
      map((r: StrictHttpResponse<MigrationResult>): MigrationResult => r.body)
    );
  }

  /** Path part for operation `fetchFieldsWithoutGroupsForCadrTemplate()` */
  static readonly FetchFieldsWithoutGroupsForCadrTemplatePath = '/migration/folder/cadr-template-fetch-fields-without-groups';

  /**
   * This method provides access to the full `HttpResponse`, allowing access to response headers.
   * To access only the response body, use `fetchFieldsWithoutGroupsForCadrTemplate()` instead.
   *
   * This method doesn't expect any request body.
   */
  fetchFieldsWithoutGroupsForCadrTemplate$Response(params: FetchFieldsWithoutGroupsForCadrTemplate$Params, context?: HttpContext): Observable<StrictHttpResponse<MigrationResult>> {
    return fetchFieldsWithoutGroupsForCadrTemplate(this.http, this.rootUrl, params, context);
  }

  /**
   * This method provides access only to the response body.
   * To access the full response (for headers, for example), `fetchFieldsWithoutGroupsForCadrTemplate$Response()` instead.
   *
   * This method doesn't expect any request body.
   */
  fetchFieldsWithoutGroupsForCadrTemplate(params: FetchFieldsWithoutGroupsForCadrTemplate$Params, context?: HttpContext): Observable<MigrationResult> {
    return this.fetchFieldsWithoutGroupsForCadrTemplate$Response(params, context).pipe(
      map((r: StrictHttpResponse<MigrationResult>): MigrationResult => r.body)
    );
  }

}
