/* tslint:disable */
/* eslint-disable */
import { HttpClient, HttpContext, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { StrictHttpResponse } from '../../strict-http-response';
import { RequestBuilder } from '../../request-builder';

import { DocumentEntity } from '../../models/document-entity';

export interface DuplicateDocumentForCompany$Params {
  newCompanyId: string;
  documentId?: string;
}

export function duplicateDocumentForCompany(http: HttpClient, rootUrl: string, params: DuplicateDocumentForCompany$Params, context?: HttpContext): Observable<StrictHttpResponse<DocumentEntity>> {
  const rb = new RequestBuilder(rootUrl, duplicateDocumentForCompany.PATH, 'post');
  if (params) {
    rb.query('newCompanyId', params.newCompanyId, {});
    rb.query('documentId', params.documentId, {});
  }

  return http.request(
    rb.build({ responseType: 'json', accept: 'application/hal+json', context })
  ).pipe(
    filter((r: any): r is HttpResponse<any> => r instanceof HttpResponse),
    map((r: HttpResponse<any>) => {
      return r as StrictHttpResponse<DocumentEntity>;
    })
  );
}

duplicateDocumentForCompany.PATH = '/documents/company/duplicate';
