/* tslint:disable */
/* eslint-disable */
import { Ng<PERSON><PERSON><PERSON>, ModuleWithProviders, <PERSON><PERSON><PERSON>elf, Optional } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiConfiguration, ApiConfigurationParams } from './api-configuration';

import { IntegrationTemplateControllerService } from './services/integration-template-controller.service';
import { DocumentSyncControllerService } from './services/document-sync-controller.service';
import { DracoonManagementControllerService } from './services/dracoon-management-controller.service';
import { NextFolderManagementControllerService } from './services/next-folder-management-controller.service';
import { MissingFileExtensionMigrationControllerService } from './services/missing-file-extension-migration-controller.service';
import { OrganizationLogoInternalControllerService } from './services/organization-logo-internal-controller.service';
import { NeoshareAiMigrationInternalControllerService } from './services/neoshare-ai-migration-internal-controller.service';
import { InboxDocumentInternalControllerService } from './services/inbox-document-internal-controller.service';
import { DocumentSyncInternalControllerService } from './services/document-sync-internal-controller.service';
import { DuplicateDocumentInternalControllerService } from './services/duplicate-document-internal-controller.service';
import { DocumentInternalControllerService } from './services/document-internal-controller.service';
import { DocumentEmailAttachmentsInternalControllerService } from './services/document-email-attachments-internal-controller.service';
import { DocumentContractInternalControllerService } from './services/document-contract-internal-controller.service';
import { InboxDocumentControllerService } from './services/inbox-document-controller.service';
import { DocumentControllerService } from './services/document-controller.service';
import { DocumentCompanyControllerService } from './services/document-company-controller.service';
import { CoreBankingIntegrationPublicControllerService } from './services/core-banking-integration-public-controller.service';
import { ProfilePictureControllerService } from './services/profile-picture-controller.service';
import { OrganizationLogoControllerService } from './services/organization-logo-controller.service';
import { UpdateFloatingDocumentWithCustomerKeyMigrationService } from './services/update-floating-document-with-customer-key-migration.service';
import { UpdateDocumentWithCustomerKeyMigrationService } from './services/update-document-with-customer-key-migration.service';
import { DocumentForwardedToInboxMigrationControllerService } from './services/document-forwarded-to-inbox-migration-controller.service';
import { DocumentCommunicationControllerService } from './services/document-communication-controller.service';
import { DocumentBusinessCaseControllerService } from './services/document-business-case-controller.service';
import { DocumentCategoryControllerService } from './services/document-category-controller.service';
import { ChatDocumentControllerService } from './services/chat-document-controller.service';
import { BucketControllerService } from './services/bucket-controller.service';
import { DocumentRemovalControllerService } from './services/document-removal-controller.service';

/**
 * Module that provides all services and configuration.
 */
@NgModule({
  imports: [],
  exports: [],
  declarations: [],
  providers: [
    IntegrationTemplateControllerService,
    DocumentSyncControllerService,
    DracoonManagementControllerService,
    NextFolderManagementControllerService,
    MissingFileExtensionMigrationControllerService,
    OrganizationLogoInternalControllerService,
    NeoshareAiMigrationInternalControllerService,
    InboxDocumentInternalControllerService,
    DocumentSyncInternalControllerService,
    DuplicateDocumentInternalControllerService,
    DocumentInternalControllerService,
    DocumentEmailAttachmentsInternalControllerService,
    DocumentContractInternalControllerService,
    InboxDocumentControllerService,
    DocumentControllerService,
    DocumentCompanyControllerService,
    CoreBankingIntegrationPublicControllerService,
    ProfilePictureControllerService,
    OrganizationLogoControllerService,
    UpdateFloatingDocumentWithCustomerKeyMigrationService,
    UpdateDocumentWithCustomerKeyMigrationService,
    DocumentForwardedToInboxMigrationControllerService,
    DocumentCommunicationControllerService,
    DocumentBusinessCaseControllerService,
    DocumentCategoryControllerService,
    ChatDocumentControllerService,
    BucketControllerService,
    DocumentRemovalControllerService,
    ApiConfiguration
  ],
})
export class ApiModule {
  static forRoot(params: ApiConfigurationParams): ModuleWithProviders<ApiModule> {
    return {
      ngModule: ApiModule,
      providers: [
        {
          provide: ApiConfiguration,
          useValue: params
        }
      ]
    }
  }

  constructor( 
    @Optional() @SkipSelf() parentModule: ApiModule,
    @Optional() http: HttpClient
  ) {
    if (parentModule) {
      throw new Error('ApiModule is already loaded. Import in your base AppModule only.');
    }
    if (!http) {
      throw new Error('You need to import the HttpClientModule in your AppModule! \n' +
      'See also https://github.com/angular/angular/issues/20575');
    }
  }
}
