import { DatePipe } from '@angular/common';
import { Notification } from '@fincloud/swagger-generator/platform-notification';
import { NotificationType } from '@fincloud/types/enums';
import { NotificationConfig } from '@fincloud/types/models';

export const NOTIFICATION_CONFIG: NotificationConfig = {
  [NotificationType.CHAT_RELATED]: {
    settingsLabel: $localize`:@@notificationSystem.settings.chat:Chat`,
    messages: {
      'platformNotification.user.sent.message.for.case': {
        icon: 'home',
        translationFn: (notification: Notification) => {
          return $localize`:@@notificationSystem.notification.chatRelated.messageSent:<strong>${notification.parameters['name']}</strong> hat im Finanzierungsfall <strong>${notification.parameters['autoGeneratedBusinessCaseName']}</strong> eine neue Nachricht gesendet.`;
        },
      },
      'platformNotification.user.tagged.in.message.for.case': {
        icon: 'home',
        translationFn: (notification: Notification) => {
          return $localize`:@@notificationSystem.notification.chatRelated.userTagged:<strong>${notification.parameters['name']}</strong> hat Sie im Chat im Finanzierungsfall <strong>${notification.parameters['autoGeneratedBusinessCaseName']}</strong> getaggt.`;
        },
      },
      'platformNotification.chat.archived.automatically': {
        icon: 'home',
        translationFn: (notification: Notification) => {
          return $localize`:@@notificationSystem.notification.chatRelated.archivedAutomatically:Im Finanzierungsfall <strong>${notification.parameters['autoGeneratedBusinessCaseName']}</strong> wurde ein Chat automatisch archiviert.`;
        },
      },
      'platformNotification.user.manually.archived.chat': {
        icon: 'home',
        translationFn: (notification: Notification) => {
          return $localize`:@@notificationSystem.notification.chatRelated.archivedManually:<strong>${notification.parameters['name']}</strong> hat einen Chat im Finanzierungsfall <strong>${notification.parameters['autoGeneratedBusinessCaseName']}</strong> archiviert.`;
        },
      },
      'platformNotification.chat.reactivated.by.user': {
        icon: 'home',
        translationFn: (notification: Notification) => {
          return $localize`:@@notificationSystem.notification.chatRelated.reactivatedByUser:<strong>${notification.parameters['name']}</strong> hat einen Chat im Finanzierungsfall <strong>${notification.parameters['autoGeneratedBusinessCaseName']}</strong> reaktiviert.`;
        },
      },
      'platformNotification.chat.created.automatically': {
        icon: 'home',
        translationFn: (notification: Notification) => {
          return $localize`:@@notificationSystem.notification.chatRelated.createdAutomatically:Im Finanzierungsfall <strong>${notification.parameters['autoGeneratedBusinessCaseName']}</strong> wurde ein Chat automatisch erstellt.`;
        },
      },
      'platformNotification.chat.reactivated.automatically': {
        icon: 'home',
        translationFn: (notification: Notification) => {
          return $localize`:@@notificationSystem.notification.chatRelated.reactivatedAutomatically:Im Finanzierungsfall <strong>${notification.parameters['autoGeneratedBusinessCaseName']}</strong> wurde ein Chat automatisch reaktiviert.`;
        },
      },
    },
  },
  [NotificationType.USER_ASSIGNMENT]: {
    settingsLabel: $localize`:@@notificationSystem.settings.userAssignment:To-do`,
    messages: {
      'platformNotification.user.assignment.expiry.after.one': {
        icon: 'home',
        translationFn: (notification: Notification) => {
          return $localize`:@@notificationSystem.notification.userAssignment.expiryAfterOne:Das mit <strong>${notification.parameters['autoGeneratedBusinessCaseName']}</strong> verknüpfte To-do ist bis heute zu erledigen. Stellen Sie bitte sicher, dass es erledigt wird`;
        },
      },
      'platformNotification.user.assignment.expiry.after.three': {
        icon: 'home',
        translationFn: (notification: Notification, datePipe: DatePipe) => {
          return $localize`:@@notificationSystem.notification.userAssignment.expiryAfterThree:Das mit <strong>${notification.parameters['autoGeneratedBusinessCaseName']}</strong> verknüpfte To-do ist bis zum <strong>${datePipe.transform(notification.parameters['userAssignmentDueDate'])}</strong> zu erledigen.`;
        },
      },
      'platformNotification.user.assignment.expiry.before.five': {
        icon: 'home',
        translationFn: (notification: Notification, datePipe: DatePipe) => {
          return $localize`:@@notificationSystem.notification.userAssignment.expiryBeforeFive:Das mit <strong>${notification.parameters['autoGeneratedBusinessCaseName']}</strong> verknüpfte To-do war bis <strong>${datePipe.transform(notification.parameters['userAssignmentDueDate'])}</strong> zu erledigen.`;
        },
      },
      'platformNotification.user.assignment.reassigned': {
        icon: 'home',
        translationFn: (notification: Notification) => {
          return $localize`:@@notificationSystem.notification.userAssignment.reassigned:Eine Aufgabe für <strong>${notification.parameters['autoGeneratedBusinessCaseName']}</strong> wurde von <strong>${notification.parameters['creatorUserFullName']}</strong> neu zugewiesen. Für diese Aufgabe ist keine Aktion Ihrerseits mehr erforderlich.`;
        },
      },
      'platformNotification.user.assignment.cancelled': {
        icon: 'home',
        translationFn: (notification: Notification) => {
          return $localize`:@@notificationSystem.notification.manualTodo.todoCanceled:Eine Aufgabe für <strong>${notification.parameters['autoGeneratedBusinessCaseName']}</strong> wurde von <strong>${notification.parameters['closedByUserFullName']}</strong> storniert.`;
        },
      },
    },
  },
};
