import { DataType, DocumentMimeType } from '@fincloud/types/enums';

export const getMimeTypesByDataType = (
  type: string,
): DocumentMimeType[] | string[] => {
  switch (type) {
    case DataType.DOCUMENT:
      return [
        DocumentMimeType.CSV,
        DocumentMimeType.PDF,
        DocumentMimeType.PDF_UTF8,
        DocumentMimeType.DOC,
        DocumentMimeType.DOCX,
        DocumentMimeType.TXT,
        DocumentMimeType.RTF,
        DocumentMimeType.ODT,
      ];
    case DataType.EXCEL:
      return [
        DocumentMimeType.XLS,
        DocumentMimeType.XLSX,
        DocumentMimeType.XLSX_UTF8,
        DocumentMimeType.ODS,
      ];
    case DataType.PRESENTATION:
      return [
        DocumentMimeType.PPT,
        DocumentMimeType.PPTX,
        DocumentMimeType.ODP,
      ];
    case DataType.ARCHIVE:
      return [
        DocumentMimeType.ZIP,
        DocumentMimeType.ZIP_ALT_1,
        DocumentMimeType.ZIP_ALT_2,
        DocumentMimeType.SEVEN_ZIP,
        DocumentMimeType.ARC,
        DocumentMimeType.GZ,
        DocumentMimeType.RAR,
        DocumentMimeType.TAR,
      ];
    case DataType.AUDIO:
      return [
        DocumentMimeType.AAC,
        DocumentMimeType.MP3,
        DocumentMimeType.WAV,
        DocumentMimeType.WEBM,
      ];
    case DataType.VIDEO:
      return [
        DocumentMimeType.MP4,
        DocumentMimeType.MOV,
        DocumentMimeType.AVI,
        DocumentMimeType.MPEG,
      ];
    case DataType.IMAGE:
      return [
        DocumentMimeType.JPG,
        DocumentMimeType.PNG,
        DocumentMimeType.SVG,
        DocumentMimeType.BMP,
        DocumentMimeType.GIF,
        DocumentMimeType.TIFF,
        DocumentMimeType.WEBP,
      ];
    case DataType.EMAIL:
      return [DocumentMimeType.MSG, DocumentMimeType.EML];
    case DataType.CONFIGURATION:
      return [
        DocumentMimeType.JSON,
        DocumentMimeType.XML,
        DocumentMimeType.YAML,
      ];
    case DataType.DIAGRAM:
      return [DocumentMimeType.DWG, DocumentMimeType.VSD];
    default:
      return [DataType.PLACEHOLDER];
  }
};
