import { Folder } from '@fincloud/swagger-generator/exchange';
import { DEFAULT_USER_NAME } from './default-user-name';

export function extractUserIdsFromFolder(folder: Folder): string[] {
  const userIds: string[] = [];

  if (folder.updatedById && folder.updatedById !== DEFAULT_USER_NAME) {
    userIds.push(folder.updatedById);
  }

  folder.children?.forEach((child) => {
    userIds.push(...extractUserIdsFromFolder(child));
  });

  return userIds;
}
