import { Folder } from '@fincloud/swagger-generator/business-case-manager';
import { FolderStructureListViewColumn } from '@fincloud/types/enums';
import { FolderListViewSort } from '@fincloud/types/models';
import { orderBy } from 'lodash-es';

export const folderStructureSortFolderSubfolders = (
  folders: Folder[],
  sort: FolderListViewSort,
): Folder[] => {
  if (!sort) {
    return folders;
  }

  switch (sort.prop) {
    case FolderStructureListViewColumn.NAME:
      return orderBy(folders, (folder) => folder.name.toLocaleLowerCase(), [
        sort.dir,
      ]);
    case FolderStructureListViewColumn.DATE_UPDATED:
      return orderBy(
        folders,
        (folder) => new Date(folder.lastModifiedDate).getTime(),
        [sort.dir],
      );
    case FolderStructureListViewColumn.SIZE:
      return orderBy(
        folders,
        (folder) =>
          (folder.children?.length || 0) + (folder.fields?.length || 0),
        [sort.dir],
      );
    default:
      return folders;
  }
};
