import { DocumentEntity } from '@fincloud/swagger-generator/document';
import { FolderStructureListViewColumn } from '@fincloud/types/enums';
import {
  FolderListViewSort,
  TemplateFieldViewModel,
} from '@fincloud/types/models';
import { orderBy } from 'lodash-es';

export const folderStructureSortFolderDocuments = (
  documentFields: TemplateFieldViewModel[],
  documentsFiles: DocumentEntity[],
  sort: FolderListViewSort,
): TemplateFieldViewModel[] => {
  if (!sort) {
    return documentFields;
  }

  switch (sort.prop) {
    case FolderStructureListViewColumn.NAME:
      return orderBy(
        documentFields,
        (doc) => doc.field.label.toLocaleLowerCase(),
        [sort.dir],
      );
    case FolderStructureListViewColumn.DATE_UPDATED:
      return orderBy(
        documentFields,
        (doc) => new Date(doc.information.lastModifiedDate).getTime(),
        [sort.dir],
      );
    case FolderStructureListViewColumn.SIZE:
      return orderBy(
        documentFields,
        (doc) =>
          documentsFiles.find((docFile) => docFile.id === doc.information.value)
            ?.contentReference?.contentSize || 0,
        [sort.dir],
      );
    default:
      return documentFields;
  }
};
