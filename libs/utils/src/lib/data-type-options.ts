import { DataType } from '@fincloud/types/enums';

export const DATA_TYPE_OPTIONS = [
  {
    label: $localize`:@@documentInbox.folder:Ordner`,
    value: DataType.FOLDER,
    iconPath: 'assets/svg/directory.svg',
  },
  {
    label: $localize`:@@dataType.document:Dokument`,
    value: DataType.DOCUMENT,
    iconPath: 'assets/svg/svgDocumentIcon.svg',
  },
  {
    label: $localize`:@@dataType.image:Bild`,
    value: DataType.IMAGE,
    iconPath: 'assets/svg/svgImageIcon.svg',
  },
  {
    label: $localize`:@@dataType.video:Video`,
    value: DataType.VIDEO,
    iconPath: 'assets/svg/svgVideoIcon.svg',
  },
  {
    label: $localize`:@@dataType.audio:Audio`,
    value: DataType.AUDIO,
    iconPath: 'assets/svg/svgAudioIcon.svg',
  },
  {
    label: $localize`:@@archiveChat.header:Archiv`,
    value: DataType.ARCHIVE,
    iconPath: 'assets/svg/svgZipIcon.svg',
  },
  {
    label: $localize`:@@dataType.presentation:Präsentation`,
    value: DataType.PRESENTATION,
    iconPath: 'assets/svg/svgPresentationIcon.svg',
  },
  {
    label: $localize`:@@dataType.configuration:Konfiguration`,
    value: DataType.CONFIGURATION,
    iconPath: 'assets/svg/svgConfigIcon.svg',
  },
  {
    label: $localize`:@@userSettings.settingsSection.mail:E-Mail`,
    value: DataType.EMAIL,
    iconPath: 'assets/svg/svgEmailIcon.svg',
  },
  {
    label: $localize`:@@dataType.diagram:Diagramm`,
    value: DataType.DIAGRAM,
    iconPath: 'assets/svg/svgDiagramIcon.svg',
  },
  {
    label: $localize`:@@dataType.spreadsheet:Spreadsheet`,
    value: DataType.EXCEL,
    iconPath: 'assets/svg/svgExcelIcon.svg',
  },
  {
    label: $localize`:@@label.placeholder:Platzhalter`,
    value: DataType.PLACEHOLDER,
    iconPath: 'assets/svg/svgPlaceholderIcon.svg',
  },
];
