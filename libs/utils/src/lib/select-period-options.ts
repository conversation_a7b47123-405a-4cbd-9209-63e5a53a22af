import { PeriodOption } from '@fincloud/types/enums';
import { FinDropdownOption } from '@fincloud/ui/dropdown';

export const SELECT_PERIOD_OPTIONS: FinDropdownOption[] = [
  {
    value: PeriodOption.TODAY,
    label: $localize`:@@calendar.today:Heute`,
  },
  {
    value: PeriodOption.SINCE_YESTERDAY,
    label: $localize`:@@selectPeriod.sinceYesterday:Seit gestern`,
  },
  {
    value: PeriodOption.LAST_7_DAYS,
    label: $localize`:@@selectPeriod.last7Days:Letzte 7 Tage`,
  },
  {
    value: PeriodOption.LAST_30_DAYS,
    label: $localize`:@@selectPeriod.last30Days:Letzte 30 Tage`,
  },
  {
    value: PeriodOption.LAST_3_MONTHS,
    label: $localize`:@@selectPeriod.last3Months:Letzte 3 Monate`,
  },
  {
    value: PeriodOption.CUSTOM_RANGE,
    label: $localize`:@@selectPeriod.customRange:Individueller Zeitraum`,
  },
];
