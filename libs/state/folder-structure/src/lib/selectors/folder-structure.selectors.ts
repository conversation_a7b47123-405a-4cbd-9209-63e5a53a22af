import { selectDocumentsFiles } from '@fincloud/state/business-case';
import { FolderStructureState } from '@fincloud/types/models';
import { createSelector } from '@ngrx/store';
import { BaseSelectors } from '@ngrx/store/src/feature_creator';

// eslint-disable-next-line @ngrx/prefix-selectors-with-select
export const folderStructureSelectors = (
  folderStructureFeature: BaseSelectors<
    'folderStructure',
    FolderStructureState
  >,
) => {
  const selectCurrentFolderFieldKeysPerGroup = createSelector(
    folderStructureFeature.selectCurrentFolderPerGroup,
    (currentFoldersPerGroup) => {
      return Object.keys(currentFoldersPerGroup).reduce(
        (acc, key) => {
          acc[key] = currentFoldersPerGroup[key].fields;
          return acc;
        },
        {} as { [groupKey: string]: string[] },
      );
    },
  );

  const selectMoveFolderOrDocumentLoading = createSelector(
    folderStructureFeature.selectMoveFolderLoading,
    folderStructureFeature.selectMoveDocumentLoading,
    (moveFolderLoading, moveDocumentLoading) => {
      return moveFolderLoading || moveDocumentLoading;
    },
  );

  const selectCurrentFolderDocumentFieldsPerGroup = createSelector(
    folderStructureFeature.selectCurrentFolderPerGroup,
    folderStructureFeature.selectSortingPerGroup,
    selectDocumentsFiles,
    (currentFoldersPerGroup, sortingPerGroup, documentsFiles) => {
      const fieldKeysPerGroup = Object.keys(currentFoldersPerGroup).reduce(
        (acc, key) => {
          acc[key] = currentFoldersPerGroup[key].fields;
          return acc;
        },
        {} as { [groupKey: string]: string[] },
      );

      return {
        fieldKeysPerGroup,
        sortingPerGroup,
        documentsFiles,
      };
    },
  );

  return {
    selectCurrentFolderFieldKeysPerGroup,
    selectMoveFolderOrDocumentLoading,
    selectCurrentFolderDocumentFieldsPerGroup,
  };
};
