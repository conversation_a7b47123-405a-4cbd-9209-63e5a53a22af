import {
  FieldDto,
  Folder,
  Group,
} from '@fincloud/swagger-generator/business-case-manager';
import { BusinessCaseGroup } from '@fincloud/swagger-generator/exchange';
import { FieldType } from '@fincloud/types/enums';
import { FinBreadcrumbItem } from '@fincloud/ui/breadcrumb';
import { createAction, props } from '@ngrx/store';

export const addFolder = createAction(
  '[Folder Structure - Folder Page] Add folder',
  props<{ name: string; groupKey: string }>(),
);

export const renameFolder = createAction(
  '[Folder Structure - Folder Page] Rename folder',
  props<{ name: string; id: string }>(),
);

export const openRenameFolderModal = createAction(
  '[Folder Structure - Folder Page] Open rename folder modal',
  props<{ groupKey: string; name: string; id: string }>(),
);

export const deleteFolder = createAction(
  '[Folder Structure - Folder Page] Delete folder',
  props<{ folderId: string; folderGroupKey: string }>(),
);

export const moveFolder = createAction(
  '[Folder Structure - Folder Page] Move folder',
  props<{
    folderId: string;
    targetFolderId: string;
    targetFolderGroup: Group;
    sourceFolderGroup: Group;
  }>(),
);

export const setRootFoldersPerGroup = createAction(
  '[Folder Structure - Folder Page] Set root folders per group',
  props<{
    groupsOrdered: BusinessCaseGroup[];
  }>(),
);

export const folderStructureChanged = createAction(
  '[Folder Structure - Folder Page] Folder structure changed',
  props<{
    groupKey: string;
    rootFolder: Folder;
    searchMode: boolean;
    filterMode: boolean;
  }>(),
);

export const setCurrentFolderForGroup = createAction(
  '[Folder Structure - Folder Page] Set current folder for group',
  props<{ groupKey: string; folder: Folder; folderIndex: number }>(),
);

export const navigateToFolderOnBreadcrumbClick = createAction(
  '[Folder Structure - Folder Page] Navigate to folder on breadcrumb click',
  props<{
    groupKey: string;
    selectedBreadcrumb: FinBreadcrumbItem;
  }>(),
);

export const locateFolderOnHighlight = createAction(
  '[Folder Structure - Folder Page] Locate folder on highlight',
  props<{
    groupKey: string;
    target: string;
    highlight: string;
    fieldType: FieldType;
  }>(),
);

export const openMoveFolderModal = createAction(
  '[Folder Structure - Folder Page] Open move folder modal',
  props<{ folderId: string; groupKey: string }>(),
);

export const openMoveDocumentModal = createAction(
  '[Folder Structure - Folder Page] Open move document modal',
  props<{ documentField: FieldDto; groupKey: string }>(),
);
