import {
  FieldDto,
  Group,
} from '@fincloud/swagger-generator/business-case-manager';
import { FieldType } from '@fincloud/types/enums';
import { createAction, props } from '@ngrx/store';

export const moveDocument = createAction(
  '[Folder Structure - Document Page] Move Document',
  props<{
    documentField: FieldDto;
    targetFolderId: string;
    targetFolderGroup: Group;
    sourceFolderGroup: Group;
  }>(),
);

export const addDocument = createAction(
  '[Folder Structure - Document Page] Add Document',
  props<{ fieldDto: FieldDto; groupKey: string }>(),
);

export const locateDocumentOnHighlight = createAction(
  '[Folder Structure - Folder Page] Locate document on highlight',
  props<{
    groupKey: string;
    target: string;
    highlight: string;
    fieldType: FieldType;
  }>(),
);
