import {
  FolderListViewSort,
  FolderStructureState,
} from '@fincloud/types/models';
import { FinBreadcrumbItem } from '@fincloud/ui/breadcrumb';
import {
  findFolderByFieldKey,
  findFolderById,
  findFolderBySubfolderId,
  folderStructureSortFolderSubfolders,
  rootFolderBreadcrumbs,
} from '@fincloud/utils';
import { createFeature, createReducer, on } from '@ngrx/store';
import { cloneDeep, get, last } from 'lodash-es';
import {
  StateLibFolderStructureDocumentApiActions,
  StateLibFolderStructureDocumentPageActions,
  StateLibFolderStructureFolderApiActions,
  StateLibFolderStructureFolderPageActions,
  StateLibFolderStructurePageActions,
} from '../actions';
import { folderStructureSelectors } from '../selectors/folder-structure.selectors';

const initialState: FolderStructureState = {
  currentFolderPerGroup: {},
  rootFolderPerGroup: {},
  sortingPerGroup: {},
  moveFolderLoading: false,
  moveDocumentLoading: false,
};

export const folderStructureFeature = createFeature({
  name: 'folderStructure',
  reducer: createReducer(
    initialState,
    on(
      StateLibFolderStructureFolderPageActions.setRootFoldersPerGroup,
      (state, { groupsOrdered }): FolderStructureState => {
        let updatedState = cloneDeep(state);

        groupsOrdered
          .filter((group) => !!group.rootFolder)
          .forEach((group) => {
            const rootFolderBreadcrumbItems = rootFolderBreadcrumbs(
              group.rootFolder.id,
            );

            updatedState = {
              ...updatedState,
              rootFolderPerGroup: {
                ...updatedState.rootFolderPerGroup,
                [group.key]: group.rootFolder,
              },
              currentFolderPerGroup: {
                ...updatedState.currentFolderPerGroup,
                [group.key]: {
                  ...group.rootFolder,
                  breadcrumbs: rootFolderBreadcrumbItems,
                },
              },
            };
          });

        return updatedState;
      },
    ),
    on(
      StateLibFolderStructureFolderPageActions.folderStructureChanged,
      (
        state,
        { groupKey, rootFolder, searchMode, filterMode },
      ): FolderStructureState => {
        const rootFolderBreadcrumbItems = rootFolderBreadcrumbs(rootFolder.id);

        const updatedRootFolderState = {
          ...state,
          rootFolderPerGroup: {
            ...state.rootFolderPerGroup,
            [groupKey]: {
              ...rootFolder,
              breadcrumbs: rootFolderBreadcrumbItems,
            },
          },
        };

        const rootFolderSorted = {
          ...rootFolder,
          children: folderStructureSortFolderSubfolders(
            rootFolder.children,
            state.sortingPerGroup[groupKey],
          ),
        };

        const currentFolderForGroup = state.currentFolderPerGroup[groupKey];
        if (
          !currentFolderForGroup ||
          currentFolderForGroup.id === rootFolder.id ||
          searchMode ||
          filterMode
        ) {
          return {
            ...updatedRootFolderState,
            currentFolderPerGroup: {
              ...updatedRootFolderState.currentFolderPerGroup,
              [groupKey]: {
                ...rootFolderSorted,
                breadcrumbs: rootFolderBreadcrumbItems,
              },
            },
          };
        }

        const { folder, breadcrumbs } = findFolderById(
          rootFolder,
          currentFolderForGroup.id,
        );

        if (!folder) {
          return {
            ...updatedRootFolderState,
            currentFolderPerGroup: {
              ...updatedRootFolderState.currentFolderPerGroup,
              [groupKey]: {
                ...rootFolderSorted,
                breadcrumbs: rootFolderBreadcrumbItems,
              },
            },
          };
        }

        const folderSorted = {
          ...folder,
          children: folderStructureSortFolderSubfolders(
            folder.children,
            state.sortingPerGroup[groupKey],
          ),
        };

        return {
          ...updatedRootFolderState,
          currentFolderPerGroup: {
            ...updatedRootFolderState.currentFolderPerGroup,
            [groupKey]: {
              ...folderSorted,
              breadcrumbs,
            },
          },
        };
      },
    ),
    on(
      StateLibFolderStructureFolderPageActions.setCurrentFolderForGroup,
      (state, { groupKey, folder, folderIndex }): FolderStructureState => {
        const currentFolderBreadcrumbs =
          state.currentFolderPerGroup[groupKey].breadcrumbs;
        const lastBreadcrumb = last(currentFolderBreadcrumbs);

        const newBreadcrumbPath = lastBreadcrumb.data.path
          ? `${lastBreadcrumb.data.path}.children[${folderIndex}]`
          : `children[${folderIndex}]`;

        const newBreadcrumb = {
          label: folder.name,
          data: {
            folderId: folder.id,
            path: newBreadcrumbPath,
          },
        };

        const folderSorted = {
          ...folder,
          children: folderStructureSortFolderSubfolders(
            folder.children,
            state.sortingPerGroup[groupKey],
          ),
        };

        return {
          ...state,
          currentFolderPerGroup: {
            ...state.currentFolderPerGroup,
            [groupKey]: {
              ...folderSorted,
              breadcrumbs: [...currentFolderBreadcrumbs, newBreadcrumb],
            },
          },
        };
      },
    ),
    on(
      StateLibFolderStructureFolderPageActions.navigateToFolderOnBreadcrumbClick,
      (state, { groupKey, selectedBreadcrumb }): FolderStructureState => {
        const groupRootFolder = state.rootFolderPerGroup[groupKey];

        if (selectedBreadcrumb.data.folderId === groupRootFolder.id) {
          const groupRootFolderSorted = {
            ...groupRootFolder,
            children: folderStructureSortFolderSubfolders(
              groupRootFolder.children,
              state.sortingPerGroup[groupKey],
            ),
          };

          return {
            ...state,
            currentFolderPerGroup: {
              ...state.currentFolderPerGroup,
              [groupKey]: {
                ...groupRootFolderSorted,
                breadcrumbs: rootFolderBreadcrumbs(groupRootFolder.id),
              },
            },
          };
        }

        const currentFolderBreadcrumbs =
          state.currentFolderPerGroup[groupKey].breadcrumbs;

        const selectedBreadcrumbIndex = currentFolderBreadcrumbs.findIndex(
          (breadcrumb: FinBreadcrumbItem): boolean =>
            breadcrumb.data.folderId === selectedBreadcrumb.data.folderId,
        );

        const folder = get(groupRootFolder, selectedBreadcrumb.data.path);

        const folderSorted = {
          ...folder,
          children: folderStructureSortFolderSubfolders(
            folder.children,
            state.sortingPerGroup[groupKey],
          ),
        };

        return {
          ...state,
          currentFolderPerGroup: {
            ...state.currentFolderPerGroup,
            [groupKey]: {
              ...folderSorted,
              breadcrumbs: currentFolderBreadcrumbs.slice(
                0,
                selectedBreadcrumbIndex + 1,
              ),
            },
          },
        };
      },
    ),
    on(
      StateLibFolderStructureFolderPageActions.locateFolderOnHighlight,
      (state, { groupKey, target }): FolderStructureState => {
        const isLocatedFolderAlreadyInView = !!state.currentFolderPerGroup[
          groupKey
        ]?.children?.find((subFolder) => subFolder.id === target);

        if (isLocatedFolderAlreadyInView) {
          return state;
        }

        const groupRootFolder = state.rootFolderPerGroup[groupKey];

        if (target === groupRootFolder.id) {
          const groupRootFolderSorted = {
            ...groupRootFolder,
            children: folderStructureSortFolderSubfolders(
              groupRootFolder.children,
              state.sortingPerGroup[groupKey],
            ),
          };

          return {
            ...state,
            currentFolderPerGroup: {
              ...state.currentFolderPerGroup,
              [groupKey]: {
                ...groupRootFolderSorted,
                breadcrumbs: rootFolderBreadcrumbs(groupRootFolder.id),
              },
            },
          };
        }

        const { folder, breadcrumbs } = findFolderBySubfolderId(
          groupRootFolder,
          target,
        );

        if (!folder) {
          return state;
        }

        const folderSorted = {
          ...folder,
          children: folderStructureSortFolderSubfolders(
            folder.children,
            state.sortingPerGroup[groupKey],
          ),
        };

        return {
          ...state,
          currentFolderPerGroup: {
            ...state.currentFolderPerGroup,
            [groupKey]: {
              ...folderSorted,
              breadcrumbs,
            },
          },
        };
      },
    ),
    on(
      StateLibFolderStructureDocumentPageActions.locateDocumentOnHighlight,
      (state, { groupKey, target }): FolderStructureState => {
        const currentFolder = state.currentFolderPerGroup[groupKey];

        if (!currentFolder || currentFolder?.fields?.includes(target)) {
          return state;
        }

        const groupRootFolder = state.rootFolderPerGroup[groupKey];

        if (groupRootFolder.fields?.includes(target)) {
          const groupRootFolderSorted = {
            ...groupRootFolder,
            children: folderStructureSortFolderSubfolders(
              groupRootFolder.children,
              state.sortingPerGroup[groupKey],
            ),
          };

          return {
            ...state,
            currentFolderPerGroup: {
              ...state.currentFolderPerGroup,
              [groupKey]: {
                ...groupRootFolderSorted,
                breadcrumbs: rootFolderBreadcrumbs(groupRootFolder.id),
              },
            },
          };
        }

        const { folder, breadcrumbs } = findFolderByFieldKey(
          groupRootFolder,
          target,
        );

        if (!folder) {
          return state;
        }

        const folderSorted = {
          ...folder,
          children: folderStructureSortFolderSubfolders(
            folder.children,
            state.sortingPerGroup[groupKey],
          ),
        };

        return {
          ...state,
          currentFolderPerGroup: {
            ...state.currentFolderPerGroup,
            [groupKey]: {
              ...folderSorted,
              breadcrumbs,
            },
          },
        };
      },
    ),
    on(
      StateLibFolderStructureFolderPageActions.moveFolder,
      (state): FolderStructureState => ({
        ...state,
        moveFolderLoading: true,
      }),
    ),
    on(
      StateLibFolderStructureFolderApiActions.moveFolderSuccess,
      StateLibFolderStructureFolderApiActions.moveFolderFailure,
      (state): FolderStructureState => ({
        ...state,
        moveFolderLoading: false,
      }),
    ),
    on(
      StateLibFolderStructureDocumentPageActions.moveDocument,
      (state): FolderStructureState => ({
        ...state,
        moveDocumentLoading: true,
      }),
    ),
    on(
      StateLibFolderStructureDocumentApiActions.moveDocumentSuccess,
      StateLibFolderStructureDocumentApiActions.moveDocumentFailure,
      (state): FolderStructureState => ({
        ...state,
        moveDocumentLoading: false,
      }),
    ),
    on(
      StateLibFolderStructurePageActions.setFolderStructureSort,
      (state, { groupKey, columnName }): FolderStructureState => {
        const currentFolderSort = state.sortingPerGroup[groupKey];

        const newDirection =
          !currentFolderSort ||
          (currentFolderSort && currentFolderSort.prop !== columnName) ||
          currentFolderSort.dir === 'desc'
            ? 'asc'
            : 'desc';

        const updatedSort: FolderListViewSort = {
          prop: columnName,
          dir: newDirection,
        };

        const currentFolder = state.currentFolderPerGroup[groupKey];

        if (!currentFolder?.children?.length) {
          return {
            ...state,
            sortingPerGroup: {
              ...state.sortingPerGroup,
              [groupKey]: updatedSort,
            },
          };
        }

        const currentFolderWithSortedSubfolders = {
          ...currentFolder,
          children: folderStructureSortFolderSubfolders(
            currentFolder.children,
            updatedSort,
          ),
        };

        return {
          ...state,
          sortingPerGroup: {
            ...state.sortingPerGroup,
            [groupKey]: updatedSort,
          },
          currentFolderPerGroup: {
            ...state.currentFolderPerGroup,
            [groupKey]: currentFolderWithSortedSubfolders,
          },
        };
      },
    ),
  ),
  extraSelectors: folderStructureSelectors,
});
