import { Injectable } from '@angular/core';
import { Toast } from '@fincloud/core/toast';
import {
  StateLibBusinessCasePageActions,
  selectBusinessCaseId,
  selectFieldKeysToBeIncluded,
} from '@fincloud/state/business-case';
import { FolderControllerService } from '@fincloud/swagger-generator/business-case-manager';
import { TemplateErrorCode } from '@fincloud/types/enums';
import { FinModalService } from '@fincloud/ui/modal';
import { FinToastService } from '@fincloud/ui/toast';
import { templateToastError } from '@fincloud/utils';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { catchError, map, of, switchMap, tap } from 'rxjs';
import {
  StateLibFolderStructureDocumentApiActions,
  StateLibFolderStructureDocumentPageActions,
} from '../actions';
import { folderStructureFeature } from '../reducers/folder-structure.reducer';

@Injectable()
export class FolderStructureAddDocumentEffects {
  constructor(
    private actions$: Actions,
    private store: Store,
    private toastService: FinToastService,
    private folderControllerService: FolderControllerService,
    private finModalService: FinModalService,
  ) {}

  addDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibFolderStructureDocumentPageActions.addDocument),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCaseId),
        this.store.select(folderStructureFeature.selectCurrentFolderPerGroup),
        this.store.select(selectFieldKeysToBeIncluded),
      ]),
      switchMap(
        ([
          { fieldDto, groupKey },
          businessCaseId,
          currentFolderPerGroup,
          fieldKeysToBeIncluded,
        ]) => {
          return this.folderControllerService
            .addFieldToFolder({
              businessCaseId,
              body: {
                fieldKeysToBeIncluded,
                fieldDto,
                folderId: currentFolderPerGroup[groupKey].id,
              },
            })
            .pipe(
              map((businessCase) =>
                StateLibFolderStructureDocumentApiActions.addDocumentSuccess({
                  businessCase,
                }),
              ),
              catchError((error) =>
                of(
                  StateLibFolderStructureDocumentApiActions.addDocumentFailure({
                    error,
                  }),
                ),
              ),
            );
        },
      ),
    ),
  );

  addDocumentSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibFolderStructureDocumentApiActions.addDocumentSuccess),
      tap(() => {
        this.toastService.show(Toast.success());
        this.finModalService.closeAll();
      }),
      map(({ businessCase }) =>
        StateLibBusinessCasePageActions.setBusinessCaseGroups({
          payload: businessCase.businessCaseTemplate.template.groupsOrdered,
        }),
      ),
    ),
  );

  reloadBusinessCaseAfterDocumentAdded$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibFolderStructureDocumentApiActions.addDocumentSuccess),
      map(({ businessCase }) =>
        StateLibBusinessCasePageActions.loadBusinessCase({
          payload: businessCase.id,
        }),
      ),
    ),
  );

  addDocumentFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibFolderStructureDocumentApiActions.addDocumentFailure),
        tap(({ error }) => {
          const errorCode: TemplateErrorCode = error?.error?.code;

          if (Object.values(TemplateErrorCode).includes(errorCode)) {
            this.toastService.show(
              Toast.error(templateToastError(error?.error?.message)[errorCode]),
            );
          } else {
            this.toastService.show(Toast.error());
          }
        }),
      ),
    { dispatch: false },
  );
}
