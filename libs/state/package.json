{"name": "@fincloud/state", "version": "0.0.1", "peerDependencies": {"@fincloud/types": "0.0.1", "@fincloud/core": "0.0.1", "@fincloud/swagger-generator": "0.0.1", "@angular/core": "17.3.11", "@angular/common": "17.3.11", "@ngrx/store": "17.2.0", "@ngrx/entity": "17.2.0", "lodash-es": "4.17.21", "@ngrx/effects": "17.2.0", "@ngrx/operators": "17.2.0", "rxjs": "7.8.1", "@angular/router": "17.3.11", "ngx-webstorage": "13.0.1", "uuid": "9.0.1", "ngrx-store-localstorage": "17.0.0", "dayjs": "1.11.11", "@ngrx/router-store": "17.2.0", "ngx-permissions": "17.1.0", "@sentry/angular": "8.48.0", "@fincloud/ui": "0.0.674", "@fincloud/utils": "0.0.1"}, "sideEffects": false}