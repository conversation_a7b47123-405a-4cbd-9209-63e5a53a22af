import { HttpErrorResponse } from '@angular/common/http';
import { BusinessCaseParticipantCustomer } from '@fincloud/swagger-generator/exchange';
import {
  FinStructureTeaserResponse,
  FinancingStructure,
} from '@fincloud/swagger-generator/financing-details';
import { createAction, props } from '@ngrx/store';

export const updateMyParticipationAmountSuccess = createAction(
  '[State Lib Business case real estate API] Update My Participation Amount Success',
  props<{ payload: BusinessCaseParticipantCustomer }>(),
);

export const updateMyParticipationAmountFailure = createAction(
  '[State Lib Business case real estate API] Update My Participation Amount Failure',
  props<{ payload: HttpErrorResponse }>(),
);

export const getMyFinStructureTeaserSuccess = createAction(
  '[State Lib Business case real estate API] Get my financing structure for teaser export Success',
  props<{ response: FinStructureTeaserResponse }>(),
);
export const getMyFinStructureTeaserFailure = createAction(
  '[State Lib Business case real estate API] Get my financing structure for teaser export Failure',
  props<{ err: HttpErrorResponse }>(),
);

export const getCustomerFinStructureTeaserSuccess = createAction(
  '[State Lib Business case real estate API] Get customer financing structure for teaser export Success',
  props<{ response: FinStructureTeaserResponse }>(),
);

export const getCustomerFinStructureTeaserFailure = createAction(
  '[State Lib Business case real estate API] Get customer financing structure for teaser export Failure',
  props<{ err: HttpErrorResponse }>(),
);

export const addDynamycFieldsetToFinancingStructureSuccess = createAction(
  '[State Lib Business case real estate API] Add dynamic fieldset Success',
  props<{
    financingStructure: FinancingStructure;
    blockId: string;
    blockName: string;
  }>(),
);

export const addDynamycFieldsetToFinancingStructureFailure = createAction(
  '[State Lib Business case real estate API] Add dynamic fieldset Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const removeDynamicFieldSetFromCaseSuccess = createAction(
  '[State Lib Business case real estate API] Remove Dynamic FieldSet From Case Success',
  props<{ financingStructure: FinancingStructure }>(),
);

export const removeDynamicFieldSetFromCaseFailure = createAction(
  '[State Lib Business case real estate API] Remove Dynamic FieldSet From Case Failure',
  props<{ payload: HttpErrorResponse }>(),
);

export const updateRefsFieldDescriptionFailure = createAction(
  '[State Lib Business case real estate API] Update Refs Field Description Failure',
);

export const updateRefsFieldDescriptionSuccess = createAction(
  '[State Lib Business case real estate API] Update Refs Field Description Success',
  props<{
    financingStructure: FinancingStructure;
  }>(),
);

export const addValueToFinStructureFieldSuccess = createAction(
  '[State Lib Business case real estate API] Add Value To FinStructure Field Success',
  props<{
    financingStructure: FinancingStructure;
    skipToast?: boolean;
    fieldKey?: string;
  }>(),
);

export const addValueToFinStructureFieldFailure = createAction(
  '[State Lib Business case real estate API] Add Value To FinStructure Field Failure',
  props<{
    err: HttpErrorResponse;
    fieldKey?: string;
  }>(),
);

export const restoreFinStructureFieldRevisionSuccess = createAction(
  '[State Lib Business case real estate API] Restore Fin Structure Field Revision Success',
  props<{ financingStructure: FinancingStructure }>(),
);

export const restoreFinStructureFieldRevisionFailure = createAction(
  '[State Lib Business case real estate API] Restore Fin Structure Field Revision Failure',
  props<{ error: HttpErrorResponse }>(),
);
