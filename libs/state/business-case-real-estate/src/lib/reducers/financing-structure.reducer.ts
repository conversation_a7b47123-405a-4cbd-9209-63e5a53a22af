import { FinancingStructure } from '@fincloud/swagger-generator/financing-details';
import { INITIAL_FINANCING_STRUCTURE_STATE } from '@fincloud/utils';
import { Action, ActionReducer, createReducer, on } from '@ngrx/store';
import {
  StateLibBusinessCaseRealEstateApiActions,
  StateLibFinancingStructureApiActions,
  StateLibFinancingStructurePageActions,
} from '../actions';

export const financingStructureReducer: ActionReducer<
  Required<FinancingStructure>,
  Action
> = createReducer(
  INITIAL_FINANCING_STRUCTURE_STATE,
  on(
    StateLibFinancingStructureApiActions.loadFinancingStructureSuccess,
    StateLibBusinessCaseRealEstateApiActions.removeDynamicFieldSetFromCaseSuccess,
    StateLibBusinessCaseRealEstateApiActions.addDynamycFieldsetToFinancingStructureSuccess,
    StateLibBusinessCaseRealEstateApiActions.updateRefsFieldDescriptionSuccess,
    StateLibBusinessCaseRealEstateApiActions.addValueToFinStructureFieldSuccess,
    StateLibBusinessCaseRealEstateApiActions.restoreFinStructureFieldRevisionSuccess,
    (state, action): Required<FinancingStructure> => {
      return {
        ...state,
        ...action.financingStructure,
      };
    },
  ),
  on(
    StateLibFinancingStructurePageActions.clearFinancingStructure,
    StateLibFinancingStructurePageActions.loadFinancingStructure,
    (): Required<FinancingStructure> => INITIAL_FINANCING_STRUCTURE_STATE,
  ),
);
