import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';

import { AppState } from '@fincloud/types/models';

@Injectable({
  providedIn: 'root',
})
export class NotificationSystemFacade {
  constructor(private store: Store<AppState>) {}

  // notifications$ = this.store.select(selectNotifications);

  // notificationsIsLoading$ = this.store.select(selectNotificationsIsLoading);

  // isOnboardingCompleted$ = this.store.select(selectIsOnboardingCompleted);

  // isLoadingNext$ = this.store.select(selectNotificationsIsLoadingNext);

  // hasMore$ = this.store.select(selectNotificationsHasMore);

  // incomingNotifications$ = this.store.select(selectIncomingNotifications);

  // isWebSocketConnected$ = this.store.select(selectIsWebSocketConnected);

  // isWebSocketConnecting$ = this.store.select(selectIsWebSocketConnecting);

  // notificationsList$ = this.store.select(selectNotificationsList);

  // settings$ = this.store.select(selectNotificationsSettings);

  // settingsIsLoading$ = this.store.select(selectNotificationsSettingsIsLoading);

  // getUnreadNotificationsCount$ = this.store.select(
  //   selectUnreadNotificationsCount,
  // );

  // isShowAllEnabled$ = this.store.select(selectNotificationsShowAllEnabled);

  // pagingNumber$ = this.store.select(selectNotificationsPagingNumber);

  // unseenNotificationsCount$ = this.store.select(selectUnseenNotificationsCount);

  // userId$ = this.store.select(selectUserId);

  // businessCaseNames$ = this.store.select(selectNotificationsBusinessCaseNames);

  // customerKey$ = this.store.select(selectUserCustomerKey);

  // toggleShowAllEnabled(enabled: boolean) {
  //   return this.store.dispatch(
  //     StateLibNotificationsPageActions.toggleShowAllEnabled({ enabled }),
  //   );
  // }

  // fetchSettings() {
  //   return this.store.dispatch(
  //     StateLibNotificationsPageActions.fetchSettings(),
  //   );
  // }

  // updateSettings(notificationType: NotificationType, isEnabled: boolean) {
  //   this.store.dispatch(
  //     StateLibNotificationsPageActions.updateNotificationSettings({
  //       notificationType,
  //       isEnabled,
  //     }),
  //   );
  // }

  // fetchNotifications() {
  //   return this.store.dispatch(
  //     StateLibNotificationsPageActions.fetchNotifications({
  //       pagingNumber: 0,
  //       size: 20,
  //     }),
  //   );
  // }

  // fetchNextNotifications(pagingNumber: number) {
  //   return this.store.dispatch(
  //     StateLibNotificationsPageActions.fetchNextNotifications({
  //       pagingNumber,
  //       size: 20,
  //     }),
  //   );
  // }

  // setIncomingNotification(notification: Notification) {
  //   return this.store.dispatch(
  //     StateLibNotificationsPageActions.incomingNotifications({
  //       notifications: [notification],
  //     }),
  //   );
  // }

  // toggleReadStatus(notification: Notification) {
  //   return this.store.dispatch(
  //     StateLibNotificationsPageActions.updateNotificationStatus({
  //       notification,
  //       isRead: !notification.read,
  //     }),
  //   );
  // }

  // markAllAsRead() {
  //   return this.store.dispatch(
  //     StateLibNotificationsPageActions.updateNotificationsStatus({
  //       isRead: true,
  //     }),
  //   );
  // }

  // markAllAsSeen() {
  //   return this.store.dispatch(
  //     StateLibNotificationsPageActions.updateNotificationsStatus({
  //       isSeen: true,
  //     }),
  //   );
  // }

  // connectWebSocket() {
  //   return this.store.dispatch(
  //     StateLibNotificationsPageActions.connectWebSocket(),
  //   );
  // }

  // fetchUnreadNotifications() {
  //   return this.store.dispatch(
  //     StateLibNotificationsPageActions.fetchUnreadNotifications({
  //       pagingNumber: 0,
  //       size: 20,
  //     }),
  //   );
  // }

  // clearUnreadNotifications() {
  //   this.store.dispatch(
  //     StateLibNotificationsPageActions.clearUnreadNotifications(),
  //   );
  // }

  // hideNotification(notification: Notification) {
  //   this.store.dispatch(
  //     StateLibNotificationsPageActions.updateNotificationStatus({
  //       notification,
  //       isHidden: true,
  //     }),
  //   );
  // }

  // finishOnboardingTour() {
  //   this.store.dispatch(
  //     StateLibUserOnboardingPageActions.updateUserOnboardingNotifications(),
  //   );
  // }
}
