import { createFeature, createReducer, on } from '@ngrx/store';

import {
  Notification,
  NotificationPreferences,
} from '@fincloud/swagger-generator/platform-notification';
import {
  NotificationSystemTab,
  NotificationSystemView,
} from '@fincloud/types/enums';
import { NotificationSystemState } from '@fincloud/types/models';
import { EntityAdapter, createEntityAdapter } from '@ngrx/entity';
import {
  StateLibNotificationSystemApiActions,
  StateLibNotificationSystemPageActions,
} from '../actions';

export function selectNotificationId(notification: Notification): string {
  //In this case this would be optional since primary key is id
  return notification.id;
}

export const adapter: EntityAdapter<Notification> =
  createEntityAdapter<Notification>({
    selectId: selectNotificationId,
  });

export const initialNotificationSystemState: NotificationSystemState = {
  isNotificationPanelOpen: false,
  currentView: NotificationSystemView.NOTIFICATIONS,
  currentTab: NotificationSystemTab.ALL,

  // Settings
  settings: {
    isLoading: false,
    notificationPreferences: {} as NotificationPreferences,
  },

  notifications: {
    isLoading: false,
    filters: {
      size: 10,
    },
    notifications: adapter.getInitialState(),
  },

  unseenNotifications: 0,
  businessCaseNames: [],

  // old version

  // showAllEnabled: true,

  // incomingNotifications: [],
  // isWebSocketConnected: false,
  // isWebSocketConnecting: false,

  // failedUpdatedSetting: null,

  // businessCaseNames: [],
  // unreadNotifications: [],
};

export const notificationSystemFeature = createFeature({
  name: 'notificationSystem',
  reducer: createReducer(
    initialNotificationSystemState,
    /* ----------- Fetch Notifications ----------- */
    // on(
    //   StateLibNotificationSystemPageActions.setNotifications,
    //   (state, { notifications }): NotificationSystemState => {
    //     return {
    //       ...state,
    //       notificationsList: { ...state.notificationsList, ...notifications },
    //       notificationsIsLoading: false,
    //       isLoadingNext: false,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemPageActions.toggleShowAllEnabled,
    //   (state, { enabled }): NotificationSystemState => {
    //     return {
    //       ...state,
    //       notifications: [],
    //       notificationsList: {},
    //       showAllEnabled: enabled,
    //       pagingNumber: null,
    //       hasMore: false,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemPageActions.fetchNotifications,
    //   (state): NotificationSystemState => {
    //     return {
    //       ...state,
    //       notificationsIsLoading: true,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemApiActions.fetchNotificationsSuccess,
    //   (state, { notifications, hasMore }): NotificationSystemState => {
    //     return {
    //       ...state,
    //       notifications: [...notifications],
    //       pagingNumber: state.pagingNumber + 1,
    //       hasMore,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemPageActions.fetchNotificationsFail,
    //   (state, { error }): NotificationSystemState => {
    //     return {
    //       ...state,
    //       notifications: [],
    //       notificationsIsLoading: false,
    //       pagingNumber: null,
    //       hasMore: false,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemPageActions.fetchNextNotifications,
    //   (state): NotificationSystemState => {
    //     return {
    //       ...state,
    //       isLoadingNext: true,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemApiActions.fetchNextNotificationsSuccess,
    //   (state, { notifications, hasMore }): NotificationSystemState => {
    //     return {
    //       ...state,
    //       notifications: [...state.notifications, ...notifications],
    //       isLoadingNext: false,
    //       pagingNumber: state.pagingNumber + 1,
    //       hasMore,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemPageActions.fetchNextNotificationsFail,
    //   (state, { error }): NotificationSystemState => {
    //     return {
    //       ...state,
    //       notifications: [],
    //       isLoadingNext: false,
    //       pagingNumber: null,
    //       hasMore: false,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemApiActions.fetchUnreadNotificationsSuccess,
    //   (state, { unreadNotifications }): NotificationSystemState => {
    //     return {
    //       ...state,
    //       unreadNotifications,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemPageActions.clearUnreadNotifications,
    //   (state): NotificationSystemState => {
    //     return {
    //       ...state,
    //       unreadNotifications: [],
    //     };
    //   },
    // ),

    // /* ----------- Update Notifications ----------- */
    // on(
    //   StateLibNotificationSystemPageActions.updateNotificationsStatus,
    //   (state): NotificationSystemState => {
    //     return {
    //       ...state,
    //       notificationsIsLoading: true,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemApiActions.updateNotificationsStatusSuccess,
    //   (state, { notifications }): NotificationSystemState => {
    //     return {
    //       ...state,
    //       notificationsList: notifications,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemApiActions.updateNotificationStatusSuccess,
    //   (state, { notification, shouldBeRemoved }): NotificationSystemState => {
    //     const list = { ...state.notificationsList };
    //     list[notification.id] = notification;

    //     if (shouldBeRemoved) {
    //       delete list[notification.id];
    //     }

    //     let unreadNotifications = [...state.unreadNotifications];
    //     if (notification.read || notification.hidden) {
    //       unreadNotifications = unreadNotifications.filter(
    //         (unreadNotification) => unreadNotification.id !== notification.id,
    //       );
    //     }

    //     if (!notification.read && !notification.hidden) {
    //       unreadNotifications.push(notification);
    //     }

    //     return {
    //       ...state,
    //       notificationsList: list,
    //       unreadNotifications,
    //     };
    //   },
    // ),

    // /* ----------- Websocket events ----------- */

    // on(
    //   StateLibNotificationSystemPageActions.incomingNotifications,
    //   (state, { notifications }): NotificationSystemState => {
    //     return {
    //       ...state,
    //       incomingNotifications: notifications,
    //     };
    //   },
    // ),

    // on(
    //   StateLibNotificationSystemPageActions.connectWebSocket,
    //   (state): NotificationSystemState => {
    //     return {
    //       ...state,
    //       isWebSocketConnected: false,
    //       isWebSocketConnecting: true,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemApiActions.connectWebSocketSuccess,
    //   (state): NotificationSystemState => {
    //     return {
    //       ...state,
    //       isWebSocketConnected: true,
    //       isWebSocketConnecting: false,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemPageActions.connectWebSocketFail,
    //   (state): NotificationSystemState => {
    //     return {
    //       ...state,
    //       isWebSocketConnected: false,
    //       isWebSocketConnecting: false,
    //     };
    //   },
    // ),

    // on(
    //   StateLibNotificationSystemApiActions.fetchBusinessCaseNamesSuccess,
    //   (state, { businessCaseNames }): NotificationSystemState => {
    //     const updatedBusinessCaseNames = [
    //       ...state.businessCaseNames,
    //       ...businessCaseNames,
    //     ];
    //     return {
    //       ...state,
    //       businessCaseNames: updatedBusinessCaseNames,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemPageActions.fetchBusinessCaseNamesFail,
    //   (state): NotificationSystemState => {
    //     return {
    //       ...state,
    //       businessCaseNames: [],
    //     };
    //   },
    // ),

    /* ----------- NEW VERSION ----------- */

    /* ----------- Notification system badge ---------- */
    on(
      StateLibNotificationSystemPageActions.toggleNotificationSystemPanel,
      (state): NotificationSystemState => {
        // let notifications = { ...state.notifications };

        // if (!state.isNotificationPanelOpen) {
        //   notifications = { ...initialNotificationSystemState.notifications };
        // }

        return {
          ...state,
          isNotificationPanelOpen: !state.isNotificationPanelOpen,
          currentView: NotificationSystemView.NOTIFICATIONS,
          // notifications,
        };
      },
    ),
    on(
      StateLibNotificationSystemPageActions.closeNotificationSystemPanel,
      (state): NotificationSystemState => {
        return {
          ...state,
          isNotificationPanelOpen: false,
          currentView: NotificationSystemView.NOTIFICATIONS,
          // notifications: {
          //   ...initialNotificationSystemState.notifications,
          // },
        };
      },
    ),
    on(
      StateLibNotificationSystemPageActions.changeCurrentView,
      (state, { view }): NotificationSystemState => {
        return { ...state, currentView: view };
      },
    ),

    /* ----------- Notification system tabs ----------- */
    on(
      StateLibNotificationSystemPageActions.changeCurrentTab,
      (state, { tab }): NotificationSystemState => {
        return {
          ...state,
          currentTab: tab,
          notifications: {
            ...initialNotificationSystemState.notifications,
            isLoading: true,
          },
        };
      },
    ),

    /* ----------- Settings ----------- */

    on(
      StateLibNotificationSystemPageActions.getSettings,
      (state): NotificationSystemState => {
        return {
          ...state,
          settings: {
            isLoading: true,
            notificationPreferences:
              initialNotificationSystemState.settings.notificationPreferences,
          },
        };
      },
    ),
    on(
      StateLibNotificationSystemApiActions.getSettingsSuccess,
      (state, { notificationPreferences }): NotificationSystemState => {
        return {
          ...state,
          settings: {
            isLoading: false,
            notificationPreferences,
          },
        };
      },
    ),
    on(
      StateLibNotificationSystemApiActions.getSettingsFailure,
      (state): NotificationSystemState => {
        return {
          ...state,
          settings: {
            isLoading: false,
            notificationPreferences:
              initialNotificationSystemState.settings.notificationPreferences,
          },
        };
      },
    ),

    /* ----------- Notifications ----------- */
    on(
      StateLibNotificationSystemPageActions.getNotifications,
      (state): NotificationSystemState => {
        return {
          ...state,
          notifications: {
            ...state.notifications,
            isLoading: true,
          },
        };
      },
    ),
    on(
      StateLibNotificationSystemApiActions.getNotificationsSuccess,
      (state, { notificationPage }): NotificationSystemState => {
        const updatedNotifications = adapter.addMany(
          notificationPage.content || [],
          state.notifications.notifications,
        );

        return {
          ...state,
          notifications: {
            ...state.notifications,
            notifications: updatedNotifications,
            isLoading: false,
          },
        };
      },
    ),
    on(
      StateLibNotificationSystemApiActions.getUpdatedNotificationsSuccess,
      (state, { notificationPage }): NotificationSystemState => {
        const updatedNotifications = adapter.setAll(
          notificationPage.content || [],
          state.notifications.notifications,
        );

        return {
          ...state,
          notifications: {
            ...state.notifications,
            notifications: updatedNotifications,
          },
        };
      },
    ),
    on(
      StateLibNotificationSystemApiActions.getNotificationsFailure,
      (state): NotificationSystemState => {
        return {
          ...state,
          notifications: { ...state.notifications, isLoading: false },
        };
      },
    ),
    on(
      StateLibNotificationSystemPageActions.markAsRead,
      (state, { notification }): NotificationSystemState => {
        const updatedNotifications = adapter.updateOne(
          {
            id: notification.id,
            changes: {
              seen: true,
              read: true,
            },
          },
          state.notifications.notifications,
        );

        return {
          ...state,
          notifications: {
            ...state.notifications,
            notifications: updatedNotifications,
          },
        };
      },
    ),
    on(
      StateLibNotificationSystemPageActions.markAsUnread,
      (state, { notification }): NotificationSystemState => {
        const updatedNotifications = adapter.updateOne(
          {
            id: notification.id,
            changes: {
              seen: true,
              read: false,
            },
          },
          state.notifications.notifications,
        );

        return {
          ...state,
          notifications: {
            ...state.notifications,
            notifications: updatedNotifications,
          },
        };
      },
    ),
    on(
      StateLibNotificationSystemPageActions.deleteNotification,
      (state, { notification }): NotificationSystemState => {
        const updatedNotifications = adapter.removeOne(
          notification.id,
          state.notifications.notifications,
        );

        return {
          ...state,
          notifications: {
            ...state.notifications,
            notifications: updatedNotifications,
          },
        };
      },
    ),

    // on(
    //   StateLibNotificationSystemPageActions.updateNotificationSettings,
    //   (state): NotificationSystemState => {
    //     return {
    //       ...state,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemApiActions.updateNotificationSettingsSuccess,
    //   (state, { notificationType, isEnabled }): NotificationSystemState => {
    //     const settings = { ...state.settings };
    //     settings[notificationType] = isEnabled;
    //     let unreadNotifications = [...state.unreadNotifications];
    //     if (!isEnabled) {
    //       unreadNotifications = unreadNotifications.filter(
    //         (notification) => notification.type !== notificationType,
    //       );
    //     }

    //     return {
    //       ...state,
    //       settings,
    //       unreadNotifications,
    //     };
    //   },
    // ),
    // on(
    //   StateLibNotificationSystemPageActions.updateNotificationSettingsFail,
    //   (state): NotificationSystemState => {
    //     return {
    //       ...state,
    //     };
    //   },
    // ),
  ),
});
