import { Injectable } from '@angular/core';
import {
  NotificationEditDto,
  NotificationsControllerService,
  PreferencesControllerService,
} from '@fincloud/swagger-generator/platform-notification';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';

import { TokenManagementService } from '@fincloud/core/auth';
import { SocketService } from '@fincloud/core/socket';
import { Toast } from '@fincloud/core/toast';
import { selectUserId } from '@fincloud/state/user';
import { ExchangeService } from '@fincloud/swagger-generator/exchange';
import { NotificationSystemTab } from '@fincloud/types/enums';
import { AppState } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { concatLatestFrom } from '@ngrx/operators';
import { catchError, filter, map, of, switchMap, tap } from 'rxjs';
import {
  StateLibNotificationSystemApiActions,
  StateLibNotificationSystemPageActions,
} from '../actions';
import { notificationSystemFeature } from '../reducers/notification-system.reducer';
import {
  selectIsLastNotificationsPage,
  selectNotificationRequestProps,
  selectUnseenNotificationsCount,
  selectUpdateNotificationsRequestProps,
} from '../selectors/notification-system.selectors';
import { NotificationSystemFacade } from '../services/notification-system.service';

@Injectable()
export class NotificationSystemEffects {
  constructor(
    private store: Store<AppState>,
    private actions$: Actions,
    private facade: NotificationSystemFacade,
    private notificationsService: NotificationsControllerService,
    private settingsService: PreferencesControllerService,
    private socketService: SocketService,
    private finToastService: FinToastService,
    private exchangeService: ExchangeService,
    private tokenManagementService: TokenManagementService,
  ) {}

  // setNotifications$ = createEffect(() =>
  //   this.actions$.pipe(
  //     ofType(
  //       StateLibNotificationSystemApiActions.fetchNotificationsSuccess,
  //       StateLibNotificationSystemApiActions.fetchNextNotificationsSuccess,
  //       StateLibNotificationsPageActions.incomingNotifications,
  //     ),
  //     switchMap(({ notifications }) =>
  //       of(notifications).pipe(
  //         map((notifications) =>
  //           StateLibNotificationsPageActions.setNotifications({
  //             notifications: notifications.reduce(
  //               (acc, notification) => {
  //                 acc[notification.id] = notification;

  //                 return acc;
  //               },
  //               {} as { [id: string]: Notification },
  //             ),
  //           }),
  //         ),
  //       ),
  //     ),
  //   ),
  // );

  // fetchBusinessCaseNames$ = createEffect(() =>
  //   this.actions$.pipe(
  //     ofType(
  //       StateLibNotificationSystemApiActions.fetchNotificationsSuccess,
  //       StateLibNotificationSystemApiActions.fetchNextNotificationsSuccess,
  //       StateLibNotificationsPageActions.incomingNotifications,
  //     ),
  //     concatLatestFrom(() => this.facade.businessCaseNames$),
  //     switchMap(([{ notifications }, caseNames]) => {
  //       const caseIds = notifications.map(
  //         (notification) => notification.parameters.caseId,
  //       );
  //       const uniqueCaseIds = [...new Set(caseIds)];
  //       const currentCaseIds = caseNames.map((caseName) => caseName.id);
  //       const newCaseIds = uniqueCaseIds.filter(
  //         (caseId) => !currentCaseIds.includes(caseId),
  //       );

  //       if (!newCaseIds.length) {
  //         return of(StateLibNoopPageActions.noop());
  //       }

  //       const body: ExchangeBusinessCaseDto = {
  //         businessCaseIds: newCaseIds,
  //       };
  //       return this.exchangeService
  //         .exchangeControllerLoadBusinessCasesBasicInfoByIds({ body })
  //         .pipe(
  //           map((businessCaseNames) =>
  //             StateLibNotificationSystemApiActions.fetchBusinessCaseNamesSuccess(
  //               { businessCaseNames },
  //             ),
  //           ),
  //           catchError(() =>
  //             of(StateLibNotificationsPageActions.fetchBusinessCaseNamesFail()),
  //           ),
  //         );
  //     }),
  //   ),
  // );

  // markChatNotificationsAsRead$ = createEffect(() =>
  //   this.actions$.pipe(
  //     ofType(StateLibNotificationsPageActions.markChatNotificationsAsRead),
  //     concatLatestFrom(() => this.facade.notificationsList$),
  //     switchMap(([{ chatId }, notifications]) => {
  //       const updatedNotificationsList = JSON.parse(
  //         JSON.stringify(notifications),
  //       );
  //       const body = Object.values(notifications).reduce((acc, val) => {
  //         if (!val.read && val.parameters.chatId === chatId) {
  //           acc.push({
  //             id: val.id,
  //             receiverUserId: val.receiverUserId,
  //             isRead: true,
  //             isSeen: true,
  //           });

  //           updatedNotificationsList[val.id].read = true;
  //           updatedNotificationsList[val.id].seen = true;
  //         }

  //         return acc;
  //       }, [] as NotificationEditDto[]);

  //       if (!body.length) {
  //         return of(StateLibNoopPageActions.noop());
  //       }

  //       return this.notificationsService
  //         .editNotificationsForUser({ body })
  //         .pipe(
  //           map(() =>
  //             StateLibNotificationSystemApiActions.updateNotificationsStatusSuccess(
  //               {
  //                 notifications: updatedNotificationsList,
  //               },
  //             ),
  //           ),
  //           catchError((error) =>
  //             of(
  //               StateLibNotificationsPageActions.updateNotificationsStatusFail({
  //                 error,
  //               }),
  //             ),
  //           ),
  //         );
  //     }),
  //   ),
  // );

  // fetchUnreadNotifications$ = createEffect(() =>
  //   this.actions$.pipe(
  //     ofType(StateLibNotificationsPageActions.fetchUnreadNotifications),
  //     concatLatestFrom(() => this.facade.userId$),
  //     switchMap(([options, userId]) => {
  //       const params = {
  //         userId,
  //         page: options.pagingNumber,
  //         size: options.size,
  //       };

  //       return this.notificationsService
  //         .getAllUnreadNotificationsForUser(params)
  //         .pipe(
  //           map(({ content }) =>
  //             StateLibNotificationSystemApiActions.fetchUnreadNotificationsSuccess(
  //               { unreadNotifications: content },
  //             ),
  //           ),
  //           catchError((error) => of(error)),
  //         );
  //     }),
  //   ),
  // );

  // connectWebSocket$ = createEffect(() =>
  //   this.actions$.pipe(
  //     ofType(StateLibNotificationsPageActions.connectWebSocket),
  //     concatLatestFrom(() => [
  //       this.facade.userId$,
  //       this.store.select(selectCustomerKey),
  //     ]),
  //     switchMap(([_, userId, customerKey]) => {
  //       const accessToken =
  //         this.tokenManagementService.getToken(customerKey).tokenRaw
  //           .accessToken;
  //       return of(
  //         this.socketService.joinRoomAndReceiveMessagesByDestination(
  //           `user-${userId}`,
  //           NOTIFICATION_DESTINATION,
  //           SocketType.PLATFORM_NOTIFICATION,
  //         ),
  //       ).pipe(
  //         map(() =>
  //           StateLibNotificationSystemApiActions.connectWebSocketSuccess(),
  //         ),
  //         catchError(() =>
  //           of(StateLibNotificationsPageActions.connectWebSocketFail()),
  //         ),
  //       );
  //     }),
  //   ),
  // );

  // toggleNotificationSettingsForType(
  //   userId: string,
  //   type: NotificationType,
  //   isEnabled: boolean,
  // ) {
  //   if (isEnabled) {
  //     return this.settingsService.turnOnNotificationsForType({ userId, type });
  //   }

  //   return this.settingsService.turnOffNotificationsForType({ userId, type });
  // }

  // mapToSettings(
  //   turnedOffSettings: NotificationType[],
  // ): NotificationSystemSettings {
  //   const settings = { ...INITIAL_NOTIFICATIONS_SETTINGS };

  //   if (!turnedOffSettings?.length) {
  //     return settings;
  //   }

  //   turnedOffSettings.forEach((setting) => (settings[setting] = false));
  //   return settings;
  // }

  // ============ NEW VERSION ============

  /* ---------- Notification system settings ---------- */
  getSettings$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibNotificationSystemPageActions.getSettings,
        StateLibNotificationSystemApiActions.updateNotificationSettingsSuccess,
        StateLibNotificationSystemApiActions.updateNotificationSettingsFailure,
      ),
      concatLatestFrom(() => this.store.select(selectUserId)),
      switchMap(([, userId]) =>
        this.settingsService.getUserNotificationPreferences({ userId }).pipe(
          map((notificationPreferences) =>
            StateLibNotificationSystemApiActions.getSettingsSuccess({
              notificationPreferences,
            }),
          ),
          catchError(() =>
            of(StateLibNotificationSystemApiActions.getSettingsFailure()),
          ),
        ),
      ),
    ),
  );

  updateSettings$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibNotificationSystemPageActions.updateNotificationSettings),
      concatLatestFrom(() => this.store.select(selectUserId)),
      switchMap(([{ notificationType, isEnabled }, userId]) => {
        let request = this.settingsService.turnOffNotificationsForType({
          userId,
          type: notificationType,
        });

        if (isEnabled) {
          request = this.settingsService.turnOnNotificationsForType({
            userId,
            type: notificationType,
          });
        }

        return request.pipe(
          map(() => {
            return StateLibNotificationSystemApiActions.updateNotificationSettingsSuccess();
          }),
          catchError(() => {
            return of(
              StateLibNotificationSystemApiActions.updateNotificationSettingsFailure(),
            );
          }),
        );
      }),
    ),
  );

  refreshCurrentNotifications$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibNotificationSystemApiActions.markAllAsReadSuccess,
        StateLibNotificationSystemApiActions.markAllAsSeenSuccess,
        StateLibNotificationSystemApiActions.markAsReadFailure,
        StateLibNotificationSystemApiActions.markAsUnreadFailure,
        StateLibNotificationSystemApiActions.deleteNotificationFailure,
      ),
      concatLatestFrom(() => [
        this.store.select(notificationSystemFeature.selectCurrentTab),
        this.store.select(selectUpdateNotificationsRequestProps),
      ]),
      switchMap(([, currentTab, props]) => {
        let request =
          this.notificationsService.getAllNotificationsForUser(props);

        if (currentTab === NotificationSystemTab.UNREAD) {
          request =
            this.notificationsService.getAllUnreadNotificationsForUser(props);
        }

        return request.pipe(
          map((notificationPage) =>
            StateLibNotificationSystemApiActions.getUpdatedNotificationsSuccess(
              {
                notificationPage,
              },
            ),
          ),
          catchError(() =>
            of(
              StateLibNotificationSystemApiActions.getUpdatedNotificationsFailure(),
            ),
          ),
        );
      }),
    ),
  );

  getInitialOrNextPageNotifications$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibNotificationSystemPageActions.getNotifications,
        StateLibNotificationSystemPageActions.changeCurrentTab,
      ),
      concatLatestFrom(() => [
        this.store.select(selectIsLastNotificationsPage),
        this.store.select(notificationSystemFeature.selectCurrentTab),
        this.store.select(selectNotificationRequestProps),
      ]),
      filter(([, isLastPage]) => !isLastPage),
      switchMap(([, , currentTab, props]) => {
        let request =
          this.notificationsService.getAllNotificationsForUser(props);

        if (currentTab === NotificationSystemTab.UNREAD) {
          request =
            this.notificationsService.getAllUnreadNotificationsForUser(props);
        }

        return request.pipe(
          map((notificationPage) =>
            StateLibNotificationSystemApiActions.getNotificationsSuccess({
              notificationPage,
            }),
          ),
          catchError(() =>
            of(StateLibNotificationSystemApiActions.getNotificationsFailure()),
          ),
        );
      }),
    ),
  );

  markAllNotificationsAsSeenOnNotificationPanelOpen$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibNotificationSystemPageActions.toggleNotificationSystemPanel,
      ),
      concatLatestFrom(() => [
        this.store.select(
          notificationSystemFeature.selectIsNotificationPanelOpen,
        ),
        this.store.select(selectUnseenNotificationsCount),
      ]),
      filter(
        ([, isNotificationPanelOpen, unseenNotificationsCount]) =>
          isNotificationPanelOpen && unseenNotificationsCount > 0,
      ),
      map(() => StateLibNotificationSystemPageActions.markAllAsSeen()),
    ),
  );

  markAllAsSeen$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibNotificationSystemPageActions.markAllAsSeen),
      concatLatestFrom(() => this.store.select(selectUserId)),
      switchMap(([, userId]) =>
        this.notificationsService.markAllSeenForUser({ userId }).pipe(
          map(() =>
            StateLibNotificationSystemApiActions.markAllAsSeenSuccess(),
          ),
          catchError(() =>
            of(StateLibNotificationSystemApiActions.markAllAsSeenFailure()),
          ),
        ),
      ),
    ),
  );

  markAllAsRead$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibNotificationSystemPageActions.markAllAsRead),
      concatLatestFrom(() => this.store.select(selectUserId)),
      switchMap(([, userId]) =>
        this.notificationsService.markAllReadForUser({ userId }).pipe(
          map(() =>
            StateLibNotificationSystemApiActions.markAllAsReadSuccess(),
          ),
          catchError(() =>
            of(StateLibNotificationSystemApiActions.markAllAsReadFailure()),
          ),
        ),
      ),
    ),
  );

  markAsRead$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibNotificationSystemPageActions.markAsRead),
      switchMap(({ notification }) => {
        const body: NotificationEditDto[] = [
          {
            id: notification.id,
            receiverUserId: notification.receiverUserId,
            isSeen: true,
            isRead: true,
          },
        ];

        return this.notificationsService
          .editNotificationsForUser({ body })
          .pipe(
            map(() => StateLibNotificationSystemApiActions.markAsReadSuccess()),
            catchError(() =>
              of(StateLibNotificationSystemApiActions.markAsReadFailure()),
            ),
          );
      }),
    ),
  );

  markAsUnread$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibNotificationSystemPageActions.markAsUnread),
      switchMap(({ notification }) => {
        const body: NotificationEditDto[] = [
          {
            id: notification.id,
            receiverUserId: notification.receiverUserId,
            isSeen: true,
            isRead: false,
          },
        ];

        return this.notificationsService
          .editNotificationsForUser({ body })
          .pipe(
            map(() =>
              StateLibNotificationSystemApiActions.markAsUnreadSuccess(),
            ),
            catchError(() =>
              of(StateLibNotificationSystemApiActions.markAsUnreadFailure()),
            ),
          );
      }),
    ),
  );

  deleteNotification$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibNotificationSystemPageActions.deleteNotification),
      switchMap(({ notification }) => {
        const body: NotificationEditDto[] = [
          {
            id: notification.id,
            receiverUserId: notification.receiverUserId,
            isSeen: true,
            isHidden: true,
          },
        ];

        return this.notificationsService
          .editNotificationsForUser({ body })
          .pipe(
            map(() =>
              StateLibNotificationSystemApiActions.deleteNotificationSuccess(),
            ),
            catchError(() =>
              of(
                StateLibNotificationSystemApiActions.deleteNotificationFailure(),
              ),
            ),
          );
      }),
    ),
  );

  showGeneralError = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          StateLibNotificationSystemApiActions.markAllAsSeenFailure,
          StateLibNotificationSystemApiActions.markAllAsReadFailure,
          StateLibNotificationSystemApiActions.markAsReadFailure,
          StateLibNotificationSystemApiActions.markAsUnreadFailure,
          StateLibNotificationSystemApiActions.deleteNotificationFailure,
          StateLibNotificationSystemApiActions.updateNotificationSettingsFailure,
          StateLibNotificationSystemApiActions.getNotificationsFailure,
          StateLibNotificationSystemApiActions.getUpdatedNotificationsFailure,
        ),
        tap(() => this.finToastService.show(Toast.error())),
      ),
    {
      dispatch: false,
    },
  );
}
