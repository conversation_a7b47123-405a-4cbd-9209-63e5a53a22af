import { NotificationType } from '@fincloud/types/enums';
import { NotificationSystemState } from '@fincloud/types/models';
import { NOTIFICATION_CONFIG } from '@fincloud/utils';

import { selectUserId } from '@fincloud/state/user';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import {
  adapter,
  notificationSystemFeature,
} from '../reducers/notification-system.reducer';

export const selectSystemNotificationsState =
  createFeatureSelector<NotificationSystemState>('notificationSystem');

export const selectNotificationConfig = createSelector(() => {
  return NOTIFICATION_CONFIG;
});

export const selectIsSettingsLoading = createSelector(
  notificationSystemFeature.selectSettings,
  ({ isLoading }) => isLoading,
);

export const selectSettingsNotificationPreferences = createSelector(
  notificationSystemFeature.selectSettings,
  ({ notificationPreferences }) => notificationPreferences,
);

export const selectSettingsData = createSelector(
  selectSettingsNotificationPreferences,
  selectNotificationConfig,
  ({ turnedOffNotificationTypes }, notificationConfig) => {
    return (Object.keys(notificationConfig) as NotificationType[]).map(
      (key) => {
        return {
          key: key as NotificationType,
          label: notificationConfig[key].settingsLabel,
          value: !turnedOffNotificationTypes.includes(key as NotificationType),
        };
      },
    );
  },
);

export const selectIsNotificationsLoading = createSelector(
  notificationSystemFeature.selectNotifications,
  ({ isLoading }) => isLoading,
);

export const selectNotificationFilters = createSelector(
  notificationSystemFeature.selectNotifications,
  ({ filters }) => filters,
);

export const selectNotificationList = createSelector(
  notificationSystemFeature.selectNotifications,
  ({ notifications }) => notifications,
);

export const {
  selectAll: selectAllNotifications,
  selectEntities: selectNotificationEntities,
  selectIds: selectNotificationIds,
  selectTotal: selectNotificationTotal,
} = adapter.getSelectors(selectNotificationList);

export const selectNotificationsContent = createSelector(
  selectAllNotifications,
  (notifications) => {
    return notifications;
  },
);

export const selectNotificationsCount = createSelector(
  selectNotificationsContent,
  (notifications) => {
    return notifications.length;
  },
);

export const selectNotificationRequestProps = createSelector(
  selectUserId,
  selectNotificationFilters,
  selectNotificationsCount,
  (userId, { size }, notificationsCount) => ({
    userId,
    size,
    page: Math.ceil(notificationsCount / size),
  }),
);

export const selectUpdateNotificationsRequestProps = createSelector(
  selectUserId,
  selectNotificationFilters,
  selectNotificationList,
  (userId, { size }, notifications) => ({
    userId,
    size: (notifications.pageable?.offset ?? 0) + size,
    page: 0,
  }),
);

export const selectUnseenNotificationsCount = createSelector(
  selectNotificationsContent,
  (notifications) => {
    return notifications.filter((notification) => !notification.seen).length;
  },
);

export const selectBadgeCounter = createSelector(
  notificationSystemFeature.selectIsNotificationPanelOpen,
  selectUnseenNotificationsCount,
  (isPanelOpen, unseenCount) => {
    return {
      isCounterVisible: !isPanelOpen && unseenCount > 0,
      unseenCount,
    };
  },
);

export const selectIsLastNotificationsPage = createSelector(
  selectNotificationList,
  (notifications) => {
    return notifications.last ?? false;
  },
);

// This is when the first request is not returned
export const selectIsEmptyNotificationContent = createSelector(
  selectNotificationTotal,
  (notificationCount) => {
    return notificationCount === 0;
  },
);

export const selectNotificationPanelData = createSelector(
  selectIsEmptyNotificationContent,
  selectIsNotificationsLoading,
  selectIsLastNotificationsPage,
  notificationSystemFeature.selectCurrentTab,
  (isEmptyContent, isLoading, isLastPage, currentTab) => {
    return { isEmptyContent, isLoading, isLastPage, currentTab };
  },
);

// ============ OLD SELECTORS ============

// export const selectNotificationsList = createSelector(
//   selectSystemNotificationsState,
//   (state) => {
//     return state.notificationsList;
//   },
// );

// export const selectNotifications = createSelector(
//   selectSystemNotificationsState,
//   (state) => {
//     return state.notifications;
//   },
// );

// export const selectUnreadNotifications = createSelector(
//   selectSystemNotificationsState,
//   (state) => state.unreadNotifications,
// );

// export const selectNotificationsIsLoading = createSelector(
//   selectSystemNotificationsState,
//   (state) => {
//     return state.notificationsIsLoading;
//   },
// );

// export const selectIncomingNotifications = createSelector(
//   selectSystemNotificationsState,
//   (state) => {
//     return state.incomingNotifications;
//   },
// );

// export const selectIsWebSocketConnected = createSelector(
//   selectSystemNotificationsState,
//   (state) => {
//     return state.isWebSocketConnected;
//   },
// );

// export const selectIsWebSocketConnecting = createSelector(
//   selectSystemNotificationsState,
//   (state) => {
//     return state.isWebSocketConnecting;
//   },
// );

// export const selectNotificationsSettings = createSelector(
//   selectSystemNotificationsState,
//   (state) => {
//     return state.settings;
//   },
// );

// export const selectNotificationsSettingsIsLoading = createSelector(
//   selectSystemNotificationsState,
//   (state) => {
//     return state.settingsIsLoading;
//   },
// );

// export const selectNotificationsIsLoadingNext = createSelector(
//   selectSystemNotificationsState,
//   (state) => {
//     return state.isLoadingNext;
//   },
// );

// export const selectNotificationsHasMore = createSelector(
//   selectSystemNotificationsState,
//   (state) => {
//     return state.hasMore;
//   },
// );

// export const selectNotificationsPagingNumber = createSelector(
//   selectSystemNotificationsState,
//   (state) => {
//     return state.pagingNumber;
//   },
// );

// export const selectNotificationsShowAllEnabled = createSelector(
//   selectSystemNotificationsState,
//   (state) => {
//     return state.showAllEnabled;
//   },
// );

// export const selectUnreadNotificationsCount = createSelector(
//   selectUnreadNotifications,
//   (unreadNotifications) => {
//     return unreadNotifications.length;
//   },
// );

// export const selectUnseenNotificationsCount = createSelector(
//   selectNotificationsList,
//   (notificationsList) => {
//     return Object.values(notificationsList).filter(
//       (notification) => !notification.seen,
//     ).length;
//   },
// );
// // TODO: Notifications
// export const selectNotificationsBusinessCaseNames = createSelector(
//   selectSystemNotificationsState,
//   (state) => {
//     return state.businessCaseNames;
//   },
// );
