import { Notification } from '@fincloud/swagger-generator/platform-notification';
import {
  NotificationSystemAction,
  NotificationSystemTab,
  NotificationSystemView,
  NotificationType,
} from '@fincloud/types/enums';
import { createAction, props } from '@ngrx/store';

// import { FeatureOnboarding } from '@fincloud/swagger-generator/user/models';

// export const setNotifications = createAction(
//   NotificationSystemAction.SET_NOTIFICATIONS,
//   props<{ notifications: { [id: string]: Notification } }>(),
// );
// export const incomingNotifications = createAction(
//   NotificationSystemAction.INCOMING_NOTIFICATIONS,
//   props<{ notifications: Notification[] }>(),
// );
// export const toggleShowAllEnabled = createAction(
//   NotificationSystemAction.TOGGLE_SHOW_ALL_ENABLED,
//   props<{ enabled: boolean }>(),
// );

// /* ---------- Fetch notifications ---------- */

// export const fetchNotifications = createAction(
//   NotificationSystemAction.FETCH_NOTIFICATIONS,
//   props<{ pagingNumber?: number; size?: number }>(),
// );
// export const fetchNotificationsFail = createAction(
//   NotificationSystemAction.FETCH_NOTIFICATIONS_FAIL,
//   props<{ error: Error }>(),
// );

// /* ---------- Fetch next notifications ---------- */

// export const fetchNextNotifications = createAction(
//   NotificationSystemAction.FETCH_NEXT_NOTIFICATIONS,
//   props<{ pagingNumber?: number; size?: number }>(),
// );
// export const fetchNextNotificationsFail = createAction(
//   NotificationSystemAction.FETCH_NEXT_NOTIFICATIONS_FAIL,
//   props<{ error: Error }>(),
// );

// /* ---------- Update notifications ---------- */

// export const updateNotificationsStatus = createAction(
//   NotificationSystemAction.UPDATE_NOTIFICATIONS_STATUS,
//   props<{
//     isRead?: boolean;
//     isSeen?: boolean;
//   }>(),
// );
// export const updateNotificationsStatusFail = createAction(
//   NotificationSystemAction.UPDATE_NOTIFICATIONS_STATUS_FAIL,
//   props<{ error: Error }>(),
// );

// export const updateNotificationStatus = createAction(
//   NotificationSystemAction.UPDATE_NOTIFICATION_STATUS,
//   props<{ notification: Notification; isRead?: boolean; isHidden?: boolean }>(),
// );
// export const updateNotificationStatusFail = createAction(
//   NotificationSystemAction.UPDATE_NOTIFICATION_STATUS_FAIL,
//   props<{ error: Error }>(),
// );

export const markChatNotificationsAsRead = createAction(
  NotificationSystemAction.MARK_CHAT_NOTIFICATIONS_AS_READ,
  props<{ chatId: string }>(),
);

// /* ---------- Connect websocket ---------- */

// export const connectWebSocket = createAction(
//   NotificationSystemAction.CONNECT_WEB_SOCKET,
// );
// export const connectWebSocketFail = createAction(
//   NotificationSystemAction.CONNECT_WEB_SOCKET_FAIL,
// );

// /* ----------  Notifications settings ---------- */

// export const clearFailedUpdatedSetting = createAction(
//   NotificationSystemAction.CLEAR_FAILED_UPDATED_SETTING,
// );
// export const fetchBusinessCaseNamesFail = createAction(
//   NotificationSystemAction.FETCH_BUSINESS_CASE_NAMES_FAIL,
// );

// export const fetchUnreadNotifications = createAction(
//   NotificationSystemAction.FETCH_UNREAD_NOTIFICATIONS,
//   props<{ pagingNumber?: number; size?: number }>(),
// );
// export const clearUnreadNotifications = createAction(
//   NotificationSystemAction.CLEAR_UNREAD_NOTIFICATIONS,
// );

// ========= NEW VERSION =========

/* ---------- Notification system badge ---------- */
export const toggleNotificationSystemPanel = createAction(
  '[Notification System Page] Toggle notification system panel',
);

export const closeNotificationSystemPanel = createAction(
  '[Notification System Page] Close notification system panel',
);

/* ---------- Notification system tabs ---------- */
export const changeCurrentTab = createAction(
  '[Notification System Page] Change current tab',
  props<{ tab: NotificationSystemTab }>(),
);

/* ---------- Notification system general ---------- */
export const changeCurrentView = createAction(
  '[Notification System Page] Set current view',
  props<{ view: NotificationSystemView }>(),
);

/* ---------- Notification system settings ---------- */
export const getSettings = createAction(
  '[Notification System Page] Get settings',
);

export const updateNotificationSettings = createAction(
  '[Notification System Page] Update settings',
  props<{ notificationType: NotificationType; isEnabled: boolean }>(),
);

/* ---------- Get notifications ---------- */
export const getNotifications = createAction(
  '[Notification System Page] Get notifications',
);

/* ---------- Notification actions ---------- */
export const markAllAsSeen = createAction(
  '[Notification System Page] Mark all as seen',
);

export const markAllAsRead = createAction(
  '[Notification System Page] Mark all as read',
);

export const markAsRead = createAction(
  '[Notification System Page] Mark as read',
  props<{ notification: Notification }>(),
);

export const markAsUnread = createAction(
  '[Notification System Page] Mark as unread',
  props<{ notification: Notification }>(),
);

export const deleteNotification = createAction(
  '[Notification System Page] Delete notification',
  props<{ notification: Notification }>(),
);
