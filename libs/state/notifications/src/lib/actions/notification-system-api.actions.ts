import {
  NotificationPreferences,
  PageNotification,
} from '@fincloud/swagger-generator/platform-notification';
import { PageNotificationWithEntities } from '@fincloud/types/models';
import { createAction, props } from '@ngrx/store';

// export const fetchNotificationsSuccess = createAction(
//   NotificationSystemAction.FETCH_NOTIFICATIONS_SUCCESS,
//   props<{ notifications: Notification[]; hasMore: boolean }>(),
// );
// export const fetchNextNotificationsSuccess = createAction(
//   NotificationSystemAction.FETCH_NEXT_NOTIFICATIONS_SUCCESS,
//   props<{ notifications: Notification[]; hasMore: boolean }>(),
// );
// export const updateNotificationsStatusSuccess = createAction(
//   NotificationSystemAction.UPDATE_NOTIFICATIONS_STATUS_SUCCESS,
//   props<{
//     notifications: Dictionary<Notification>;
//     isRead?: boolean;
//     isSeen?: boolean;
//   }>(),
// );
// export const updateNotificationStatusSuccess = createAction(
//   NotificationSystemAction.UPDATE_NOTIFICATION_STATUS_SUCCESS,
//   props<{ notification: Notification; shouldBeRemoved: boolean }>(),
// );
// export const connectWebSocketSuccess = createAction(
//   NotificationSystemAction.CONNECT_WEB_SOCKET_SUCCESS,
// );
// export const fetchSettingsSuccess = createAction(
//   NotificationSystemAction.FETCH_SETTINGS_SUCCESS,
//   props<{ settings: NotificationSystemSettings }>(),
// );
// export const updateNotificationSettingsSuccess = createAction(
//   NotificationSystemAction.UPDATE_SETTINGS_STATUS_SUCCESS,
//   props<{ notificationType: NotificationType; isEnabled: boolean }>(),
// );
// export const fetchBusinessCaseNamesSuccess = createAction(
//   NotificationSystemAction.FETCH_BUSINESS_CASE_NAMES_SUCCESS,
//   props<{ businessCaseNames: BusinessCaseBasicInfoDto[] }>(),
// );
// export const fetchUnreadNotificationsSuccess = createAction(
//   NotificationSystemAction.FETCH_UNREAD_NOTIFICATIONS_SUCCESS,
//   props<{ unreadNotifications: Notification[] }>(),
// );

// ========= NEW VERSION =========

export const getSettingsSuccess = createAction(
  '[Notification System API] Get settings Success',
  props<{ notificationPreferences: NotificationPreferences }>(),
);

export const getSettingsFailure = createAction(
  '[Notification System API] Get settings Failure',
);

export const updateNotificationSettingsSuccess = createAction(
  '[Notification System API] Update settings Success',
);

export const updateNotificationSettingsFailure = createAction(
  '[Notification System API] Update settings Failure',
);

export const getNotificationsSuccess = createAction(
  '[Notification System API] Get notifications Success',
  props<{
    notificationPage: PageNotificationWithEntities | PageNotification;
  }>(),
);

export const getNotificationsFailure = createAction(
  '[Notification System API] Get notifications Failure',
);

export const getUpdatedNotificationsSuccess = createAction(
  '[Notification System API] Get updated notifications Success',
  props<{
    notificationPage: PageNotificationWithEntities | PageNotification;
  }>(),
);

export const getUpdatedNotificationsFailure = createAction(
  '[Notification System API] Get updated notifications Failure',
);

/* ---------- Mark all as seen ---------- */
export const markAllAsSeenSuccess = createAction(
  '[Notification System API] Mark all as seen Success',
);

export const markAllAsSeenFailure = createAction(
  '[Notification System API] Mark all as seen Failure',
);

/* ---------- Mark all as read ---------- */
export const markAllAsReadSuccess = createAction(
  '[Notification System API] Mark all as read Success',
);

export const markAllAsReadFailure = createAction(
  '[Notification System API] Mark all as read Failure',
);

/* ---------- Mark as read ---------- */
export const markAsReadSuccess = createAction(
  '[Notification System API] Mark as read Success',
);

export const markAsReadFailure = createAction(
  '[Notification System API] Mark as read Failure',
);

/* ---------- Mark as unread ---------- */
export const markAsUnreadSuccess = createAction(
  '[Notification System API] Mark as unread Success',
);

export const markAsUnreadFailure = createAction(
  '[Notification System API] Mark as unread Failure',
);

/* ---------- Delete notification ---------- */
export const deleteNotificationSuccess = createAction(
  '[Notification System API] Delete notification Success',
);

export const deleteNotificationFailure = createAction(
  '[Notification System API] Delete notification Failure',
);
