import { Invitation } from '@fincloud/swagger-generator/business-case-manager';
import { InvitationStatus } from '@fincloud/types/enums';
import { InvitationState } from '@fincloud/types/models';
import { createFeatureSelector, createSelector } from '@ngrx/store';

const selectInvitationsState =
  createFeatureSelector<InvitationState>('invitations');

export const selectAllCustomerInvitations = createSelector(
  selectInvitationsState,
  (state) => {
    return state.invitations;
  },
);

export const selectValidGuestCustomerInvitations = createSelector(
  selectAllCustomerInvitations,
  (invitations: Invitation[]) => {
    if (!invitations) {
      return null;
    }
    // These statuses grant a guest customer access to the platform
    return (
      invitations?.filter(
        (i) =>
          i.invitationStatus === InvitationStatus.ACCEPTED ||
          i.invitationStatus === InvitationStatus.DECLINED ||
          i.invitationStatus === InvitationStatus.PENDING,
      ) ?? []
    );
  },
);

export const selectBusinessCaseIdFromUrl = createSelector(
  selectInvitationsState,
  (state) => state.businessCaseIdFromUrl,
);
