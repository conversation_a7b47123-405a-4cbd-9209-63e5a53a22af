import { StateLibNeogptChatPageActions } from '@fincloud/state/neogpt-chat';
import { SideNavigationsState } from '@fincloud/types/models';
import { createFeature, createReducer, on } from '@ngrx/store';
import { StateLibSideNavigationsPageActions } from '../actions';
import { sideNavigationsSelectors } from '../selectors/side-navigations.selectors';

export const initialState: SideNavigationsState = {
  pageNavigationIsOpen: true,
  platformNavigationIsOpen: false,
  mainPanelWidth: 0,
};

const reducer = createReducer(
  initialState,
  on(
    StateLibSideNavigationsPageActions.toggleOpenForPageNavigation,
    (state: SideNavigationsState): SideNavigationsState => {
      return {
        ...state,
        pageNavigationIsOpen: !state.pageNavigationIsOpen,
      };
    },
  ),
  on(
    StateLibSideNavigationsPageActions.toggleOpenForPlatformNavigation,
    (state: SideNavigationsState): SideNavigationsState => {
      return {
        ...state,
        platformNavigationIsOpen: !state.platformNavigationIsOpen,
      };
    },
  ),

  on(
    StateLibNeogptChatPageActions.openChat,
    (state: SideNavigationsState): SideNavigationsState => {
      return {
        ...state,
        platformNavigationIsOpen:
          state.platformNavigationIsOpen && state.pageNavigationIsOpen
            ? false
            : state.platformNavigationIsOpen,
      };
    },
  ),
  on(
    StateLibSideNavigationsPageActions.setMainPanelWidth,
    (state: SideNavigationsState, { width }): SideNavigationsState => ({
      ...state,
      mainPanelWidth: width,
    }),
  ),
);

export const sideNavigationsFeature = createFeature({
  name: 'sideNavigation',
  reducer,
  extraSelectors: sideNavigationsSelectors,
});
