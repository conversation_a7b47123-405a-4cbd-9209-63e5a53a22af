import { CadrGroup } from '@fincloud/swagger-generator/company';
import {
  BusinessCaseGroup,
  Folder,
  InformationRecord,
} from '@fincloud/swagger-generator/exchange';
import { DataRoomGroupTemplateName } from '@fincloud/types/enums';
import { FinTreeNode } from '@fincloud/ui/tree-menu';
import { DATA_ROOM_FIELDS_NOT_ALLOWED_TO_BE_SHOWN } from '@fincloud/utils';
import { keyBy } from 'lodash-es';
import { DataRoomTemplateField } from '../models/data-room-template-field';
import { DataRoomTreeOrderedGroup } from '../models/data-room-tree-ordered-group';
import { generateDocumentsTree } from './generate-sidebar-tree';
import { markExpandedNodes } from './mark-expanded-nodes';
import { getRootFolder } from './root-folder';
import { syncFieldValues } from './sync-field-values';

export const mapDataRoomOrderedGroup = (
  group: BusinessCaseGroup | CadrGroup,
  fields: DataRoomTemplateField[],
  information: InformationRecord,
  extendedGroups: { [key: string]: boolean },
  highlightedTopGroupKey: string,
): DataRoomTreeOrderedGroup => {
  const fieldsToBeShown = fields.filter(
    (f) => !DATA_ROOM_FIELDS_NOT_ALLOWED_TO_BE_SHOWN.includes(f.key),
  );
  // Value of the fields is not always available, so we need to synchronize them with the information values prop
  const synchronizedFields = syncFieldValues(information, fieldsToBeShown);
  const fieldsAsObject = keyBy(synchronizedFields, 'key');
  const children: FinTreeNode<
    | Folder
    | DataRoomTemplateField
    | (DataRoomTemplateField | { groupKey: string })
  >[] = group.fields
    .filter(
      (fieldKey) =>
        !DATA_ROOM_FIELDS_NOT_ALLOWED_TO_BE_SHOWN.includes(fieldKey),
    )
    .map((field) => ({
      ...fieldsAsObject[field],
      templateName: DataRoomGroupTemplateName.FIELD,
      groupKey: group.key,
    }))
    .filter((field) => field.fieldType !== 'DOCUMENT' && !field.isHidden);
  const rootFolderData = getRootFolder(group);

  if (Object.keys(rootFolderData).length > 0) {
    const documentTree = generateDocumentsTree(
      rootFolderData,
      group.key,
      fieldsToBeShown,
      extendedGroups,
    );
    documentTree.templateName = DataRoomGroupTemplateName.ROOT_FOLDER_GROUP;
    children.push(documentTree);
  }

  markExpandedNodes(children, extendedGroups);

  return {
    ...group,
    expanded: extendedGroups[group.key] ?? false,
    templateName: DataRoomGroupTemplateName.ROOT_GROUP,
    children,
    active: group.key === highlightedTopGroupKey,
  };
};
