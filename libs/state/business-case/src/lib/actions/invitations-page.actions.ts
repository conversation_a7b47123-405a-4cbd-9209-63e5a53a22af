import { Invitation } from '@fincloud/swagger-generator/application';
import { UserData } from '@fincloud/swagger-generator/business-case-manager';
import { createAction, props } from '@ngrx/store';
export const rejectInvitation = createAction(
  '[Invitations Page] Reject',
  props<{ payload: Invitation }>(),
);
export const cancelInvitation = createAction(
  '[Invitations Page] Cancel',
  props<{ payload: Invitation }>(),
);
export const resendCancelledInvitation = createAction(
  '[Invitations Page] Resend Cancelled',
  props<{ payload: Invitation }>(),
);
export const updateInvitation = createAction(
  '[Invitations Page] Update selected',
  props<{ payload: Invitation }>(),
);
export const startInvitationPolling = createAction(
  '[Invitations Page] Start invitation polling',
  props<{ payload: boolean }>(),
);
export const setCollaborationsInvitationsModalLoading = createAction(
  '[Invitations Page] set loading state in modal',
  props<{ payload: boolean }>(),
);
export const inviteUsersToGuestCustomer = createAction(
  '[Invitations Page] Invite users to guest customer',
  props<{
    payload: {
      businessCaseId: string;
      customerKey: string;
      body: UserData[];
    };
  }>(),
);
export const clearAcceptedInvitations = createAction(
  '[Invitation Page] Clear Accepted Invitations',
);
