import { Folder } from '@fincloud/swagger-generator/exchange';
import { DataRoomGroupTemplateName } from '@fincloud/types/enums';
import { FinTreeNode } from '@fincloud/ui/tree-menu';
import { DataRoomGroup } from './data-room-group';
import { DataRoomTemplateField } from './data-room-template-field';

export type DataRoomTreeOrderedGroup = {
  expanded: boolean;
  active: boolean;
  templateName: DataRoomGroupTemplateName.ROOT_GROUP;
  children: FinTreeNode<
    | Folder
    | DataRoomTemplateField
    | (DataRoomTemplateField | { groupKey: string })
  >[];
} & DataRoomGroup;
