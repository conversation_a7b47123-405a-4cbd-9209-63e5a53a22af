import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Toast } from '@fincloud/core/toast';
import { selectCustomerKey } from '@fincloud/state/customer';
import {
  StateLibInvitationApiActions,
  StateLibInvitationPageActions,
} from '@fincloud/state/invitation';
import { selectUrl } from '@fincloud/state/router';
import { InvitationControllerService } from '@fincloud/swagger-generator/application';
import {
  CustomerType,
  InvitationFlowStrategy,
  InvitationStatus,
} from '@fincloud/types/enums';
import { FinToastService } from '@fincloud/ui/toast';
import { CUSTOMER_NOT_A_COLLABORATOR } from '@fincloud/utils';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { catchError, filter, map, mergeMap, of, switchMap } from 'rxjs';
import {
  StateLibInvitationsApiActions,
  StateLibInvitationsPageActions,
} from '../actions';
import {
  selectFailedAcceptedInvitations,
  selectSuccessfulAcceptedInvitations,
} from '../selectors/business-case-invitation.selectors';
import { selectSectionInvitationData } from '../selectors/invitations.selectors';

@Injectable()
export class StateLibBusinessCaseInvitationEffects {
  acceptInvitation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibInvitationPageActions.acceptInvitation),
      concatLatestFrom(() => [this.store.select(selectSectionInvitationData)]),
      mergeMap(([, { businessCase, invitation, userCustomerKey, customer }]) =>
        this.invitationControllerService
          .acceptInvitations({
            businessCaseId: businessCase.id,
            body: [
              {
                invitationId: invitation.id,
                customerKey: userCustomerKey,
                customerStatus: customer.customerStatus,
                totalParticipationAmount: 0,
              },
            ],
          })
          .pipe(
            map((result) =>
              StateLibInvitationApiActions.acceptInvitationSuccess({
                result,
              }),
            ),
            catchError((err) =>
              of(StateLibInvitationApiActions.acceptInvitationFailure({ err })),
            ),
          ),
      ),
    ),
  );
  // TODO: this is triggered everytime update on business case comes (as probably much more effects)
  loadInvitationSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibInvitationsApiActions.loadInvitationsSuccess),
        concatLatestFrom(() => [
          this.store.select(selectUrl),
          this.store.select(selectCustomerKey),
        ]),
        filter(
          ([action, , customerKey]) =>
            !!action.payload.find(
              (invitation) =>
                invitation.invitedCustomerKey === customerKey &&
                invitation.invitationStatus === InvitationStatus.ACCEPTED &&
                invitation.invitationType ===
                  InvitationFlowStrategy.INVITE_TO_APPLY_STRATEGY,
            ),
        ),
        map(([, routerUrl, customerKey]) => {
          if (customerKey)
            // When invitation is accepted we have to switch from invitation -> application
            // there for we have to navigate by same url and run again guards, were invitation guard
            // will return false, but application - true and we have correct displayed component
            this.router.navigateByUrl(routerUrl, {
              onSameUrlNavigation: 'reload',
            });
        }),
      ),
    { dispatch: false },
  );

  getInvitation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibInvitationApiActions.acceptInvitationSuccess),
      concatLatestFrom(() => [
        this.store.select(selectSectionInvitationData),
        this.store.select(selectFailedAcceptedInvitations),
      ]),
      filter(([, , failedInvivations]) => !failedInvivations.length),
      switchMap(([, { businessCase, invitation }]) =>
        this.invitationControllerService
          .getInvitation({ id: invitation.id, businessCaseId: businessCase.id })
          .pipe(
            map((invitation) =>
              StateLibInvitationApiActions.getInvitationSuccess({ invitation }),
            ),
            catchError((err) =>
              of(StateLibInvitationApiActions.getInvitationFailure({ err })),
            ),
          ),
      ),
    ),
  );

  getInvitationSuccessWithBankOnJoin$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibInvitationApiActions.getInvitationSuccess),
        concatLatestFrom(() => this.store.select(selectSectionInvitationData)),
        filter(
          ([{ invitation }, { customer }]) =>
            customer.customerType === CustomerType.BANK &&
            invitation.invitationType ===
              InvitationFlowStrategy.INVITE_TO_JOIN_STRATEGY,
        ),
        map(([{ invitation }, { userCustomerKey }]) =>
          this.router.navigate([
            userCustomerKey,
            'business-case',
            invitation.subBusinessCaseId ?? invitation.businessCaseId,
          ]),
        ),
      ),
    { dispatch: false },
  );

  getInvitationSuccessWithNonBankNavigation$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibInvitationApiActions.getInvitationSuccess),
        concatLatestFrom(() => this.store.select(selectSectionInvitationData)),
        filter(
          ([, { customer }]) => customer.customerType !== CustomerType.BANK,
        ),
        map(([{ invitation }, { userCustomerKey }]) => {
          this.finToastService.show(Toast.success());

          this.router.navigate([
            userCustomerKey,
            'business-case',
            invitation.subBusinessCaseId ?? invitation.businessCaseId,
          ]);
        }),
      ),
    { dispatch: false },
  );

  declineInvitation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibInvitationPageActions.declineInvitation),
      concatLatestFrom(() => this.store.select(selectSectionInvitationData)),
      mergeMap(([, { businessCase, invitation }]) =>
        this.invitationControllerService
          .declineInvitation({
            businessCaseId: businessCase.id,
            invitationId: invitation.id,
          })
          .pipe(
            map(() =>
              StateLibInvitationApiActions.declineInvitationSuccess({
                payload: invitation,
              }),
            ),
            catchError((err) =>
              of(
                StateLibInvitationApiActions.declineInvitationFailure({ err }),
              ),
            ),
          ),
      ),
    ),
  );

  declineInvitationSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibInvitationApiActions.declineInvitationSuccess),
      concatLatestFrom(() => this.store.select(selectSectionInvitationData)),
      map(([{ payload }, { userCustomerKey }]) => {
        {
          this.router.navigate([userCustomerKey, 'cases']);

          return StateLibInvitationsPageActions.rejectInvitation({
            payload,
          });
        }
      }),
    ),
  );

  getInvitationFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibInvitationApiActions.getInvitationFailure),
        concatLatestFrom(() => [
          this.store.select(selectSuccessfulAcceptedInvitations),
          this.store.select(selectCustomerKey),
        ]),
        map(([{ err }, acceptedInvitations, userCustomerKey]) => {
          if (err.error.code === CUSTOMER_NOT_A_COLLABORATOR) {
            const acceptedInvitation = acceptedInvitations[0];
            this.router.navigate([
              '/',
              userCustomerKey,
              'business-case',
              acceptedInvitation?.subBusinessCaseId,
            ]);
          }
        }),
      ),
    { dispatch: false },
  );

  constructor(
    private actions$: Actions,
    private invitationControllerService: InvitationControllerService,
    private finToastService: FinToastService,
    private store: Store,
    private router: Router,
  ) {}
}
