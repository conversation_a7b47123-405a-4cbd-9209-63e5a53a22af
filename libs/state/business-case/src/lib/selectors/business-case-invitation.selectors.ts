import {
  selectCanApplyForBusinessCase,
  selectIsGranularEmployeeOfLeadCustomer,
  selectIsGranularEmployeeOfParticipatingCustomer,
} from '@fincloud/state/access';
import {
  selectCustomerKey,
  selectIsCustomerGuest,
} from '@fincloud/state/customer';
import { selectAllCustomerInvitations } from '@fincloud/state/invitation';
import { selectUserToken } from '@fincloud/state/user';
import { Invitation } from '@fincloud/swagger-generator/application';
import { InvitationStatus } from '@fincloud/types/enums';
import { UserToken } from '@fincloud/types/models';
import { createSelector } from '@ngrx/store';
import {
  selectBusinessCaseDashboardState,
  selectBusinessCaseId,
  selectIsBusinessCaseHeaderInView,
} from './business-case.selectors';
import { selectCanPlatformManagerAddHimself } from './collaboration-managements.selectors';
import { selectSelectedFinancingDetailsMyParticipation } from './navigation.selectors';

export const selectHasReceivedInvitation = createSelector(
  selectAllCustomerInvitations,
  selectUserToken,
  selectBusinessCaseId,
  (
    invitations: Invitation[],
    token: UserToken,
    businessCaseId: string,
  ): boolean => {
    return invitations?.length && token && businessCaseId
      ? !!invitations.find(
          (i) =>
            i.invitedCustomerKey === token.customer_key &&
            i.invitationStatus === InvitationStatus.PENDING &&
            i.invitedUsers.some((iu) => iu.userId === token.sub) &&
            i.businessCaseId === businessCaseId,
        )
      : false;
  },
);

export const selectCanShowHeaderInvitationNavigation = createSelector(
  selectHasReceivedInvitation,
  selectIsCustomerGuest,
  (hasPendingInvitations, isGuestCustomer) =>
    hasPendingInvitations && isGuestCustomer,
);

export const selectInvitationIsVisibleAndHeaderIsExpanded = createSelector(
  selectCanShowHeaderInvitationNavigation,
  selectIsBusinessCaseHeaderInView,
  (invitationMessageIsVisible, headerIsExpanded) => {
    return {
      invitationMessageIsVisible,
      headerIsExpanded,
    };
  },
);

export const selectAcceptedInvitations = createSelector(
  selectBusinessCaseDashboardState,
  (state) => state.acceptedInvitations,
);

export const selectSuccessfulAcceptedInvitations = createSelector(
  selectAcceptedInvitations,
  (invitations) => invitations.successful,
);

export const selectFailedAcceptedInvitations = createSelector(
  selectAcceptedInvitations,
  (invitations) => invitations.failed,
);

export const selectInvitationAccepted = createSelector(
  selectSuccessfulAcceptedInvitations,
  selectCustomerKey,
  selectBusinessCaseId,
  (invitations, customerKey, businessCaseId) =>
    invitations.some(
      (invitation) =>
        invitation.invitedCustomerKey === customerKey &&
        invitation.businessCaseId === businessCaseId,
    ),
);

export const selectShowApplicationInvitationButton = createSelector(
  selectSelectedFinancingDetailsMyParticipation,
  selectIsGranularEmployeeOfParticipatingCustomer,
  selectIsGranularEmployeeOfLeadCustomer,
  selectHasReceivedInvitation,
  selectCanApplyForBusinessCase,
  selectCanPlatformManagerAddHimself,
  selectInvitationAccepted,
  (
    myParticipation,
    isGranularEmployeeOfParticipatingCustomer,
    isGranularEmployeeOfLeadCustomer,
    receivedInvitation,
    canApplyForBusinessCase,
    canPlatformManagerAddHimself,
    invitationAccepted,
  ) => {
    return (
      (canApplyForBusinessCase || receivedInvitation) &&
      !(
        !!myParticipation ||
        isGranularEmployeeOfLeadCustomer ||
        isGranularEmployeeOfParticipatingCustomer
      ) &&
      !canPlatformManagerAddHimself &&
      !invitationAccepted
    );
  },
);
