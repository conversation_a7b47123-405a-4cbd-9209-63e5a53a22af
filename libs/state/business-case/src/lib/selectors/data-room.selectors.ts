import { selectHasDataRoomWriteAccess } from '@fincloud/state/access';
import { BUSINESS_CASE_DASHBOARD_FEATURE_KEY } from '@fincloud/state/utils';
import {
  BusinessCaseGroup,
  BusinessCaseTemplate,
  Company,
  CompanyTemplate,
  ExchangeBusinessCase,
} from '@fincloud/swagger-generator/exchange';
import { DataRoomDraggedItemType } from '@fincloud/types/enums';
import { BusinessCaseDashboardState } from '@fincloud/types/models';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import {
  selectBusinessCaseInformation,
  selectEditTemplateMode,
} from './business-case.selectors';

import { Application } from '@fincloud/swagger-generator/application';
import { Invitation } from '@fincloud/swagger-generator/business-case-manager';
import { CadrGroup } from '@fincloud/swagger-generator/company';
import { FinTreeNode } from '@fincloud/ui/tree-menu';
import { collapseNodeIfParentNodeIsCollapse } from '../utils/colapse-group-tree';
import { mapDataRoomOrderedGroup } from '../utils/map-data-room-ordered-group';

const selectBusinessCaseDashboardState =
  createFeatureSelector<BusinessCaseDashboardState>(
    BUSINESS_CASE_DASHBOARD_FEATURE_KEY,
  );

const selectDashBoardBusinessCase = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): ExchangeBusinessCase => {
    return state?.businessCase;
  },
);

export const selectDataRoomEditToggle = createSelector(
  selectEditTemplateMode,
  selectHasDataRoomWriteAccess,
  (stateEditToggle, hasDataRoomWriteAccess) => {
    if (!hasDataRoomWriteAccess) {
      return false;
    }

    return stateEditToggle;
  },
);

export const selectGetDraggerDataRoomItemType = createSelector(
  selectBusinessCaseDashboardState,
  (state) => {
    return state.dataRoomDraggerItemType;
  },
);

export const selectIsDraggerDataRoomItemTypeFolder = createSelector(
  selectGetDraggerDataRoomItemType,
  (itemType) => {
    return itemType === DataRoomDraggedItemType.FOLDER;
  },
);

export const selectIsDraggerDataRoomItemTypeDocument = createSelector(
  selectGetDraggerDataRoomItemType,
  (itemType) => {
    return itemType === DataRoomDraggedItemType.DOCUMENT;
  },
);

export const selectIsDraggerDataRoomItemTypeOther = createSelector(
  selectGetDraggerDataRoomItemType,
  (dragType) => {
    return dragType === DataRoomDraggedItemType.OTHER;
  },
);

export const selectIsDraggerDataRoomItemTypeDocumentOrFolder = createSelector(
  selectIsDraggerDataRoomItemTypeFolder,
  selectIsDraggerDataRoomItemTypeDocument,
  (isFolderType, isDocumentType) => {
    return isFolderType || isDocumentType;
  },
);

export const selectIsDataRoomItemDragginActive = createSelector(
  selectGetDraggerDataRoomItemType,
  (dragType) => {
    return dragType !== DataRoomDraggedItemType.NONE;
  },
);

export const selectBusinessCaseMainTemplate = createSelector(
  selectDashBoardBusinessCase,
  (businessCase: ExchangeBusinessCase) => {
    return businessCase?.businessCaseTemplate;
  },
);

export const selectBusinessCaseTemplate = createSelector(
  selectBusinessCaseMainTemplate,
  (businessCaseTemplate: BusinessCaseTemplate) => {
    return businessCaseTemplate?.template;
  },
);

export const selectDashBoardBusinessCaseCompany = createSelector(
  selectDashBoardBusinessCase,
  (businessCase: ExchangeBusinessCase) => {
    return businessCase?.company;
  },
);

export const selectDashBoardBusinessCaseCdrCompanyTemplate = createSelector(
  selectDashBoardBusinessCaseCompany,
  (company: Company) => {
    return company?.companyTemplate;
  },
);

export const selectBusinessCaseCadrTemplate = createSelector(
  selectDashBoardBusinessCaseCdrCompanyTemplate,
  (companyTemplate: CompanyTemplate) => {
    return companyTemplate?.template;
  },
);

export const selectBusinessCaseCompanyGroups = createSelector(
  selectBusinessCaseCadrTemplate,
  (businessCaseCadrTemplate) => businessCaseCadrTemplate?.groupsOrdered || [],
);

export const selectBusinessCaseTemplateOrderedFields = createSelector(
  selectBusinessCaseTemplate,
  (businessCaseTemplate) => {
    return businessCaseTemplate?.groupsOrdered || [];
  },
);

export const selectExtendedGroups = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => {
    return state.extendedGroups;
  },
);

export const selectHighlightedTopGroupKey = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState) => {
    return state.highlightedTopGroupKey;
  },
);

export const selectBusinessCaseTreeMenuData = createSelector(
  selectBusinessCaseTemplate,
  selectExtendedGroups,
  selectBusinessCaseInformation,
  selectHighlightedTopGroupKey,
  (
    businessCaseTemplate,
    extendedGroups,
    information,
    highlightedTopGroupKey,
  ): FinTreeNode<BusinessCaseGroup>[] => {
    if (
      !businessCaseTemplate?.groupsOrdered ||
      businessCaseTemplate?.groupsOrdered.length === 0
    ) {
      return [];
    }

    return businessCaseTemplate?.groupsOrdered
      ?.map((group: BusinessCaseGroup) =>
        mapDataRoomOrderedGroup(
          group,
          businessCaseTemplate.fields,
          information,
          extendedGroups,
          highlightedTopGroupKey,
        ),
      )
      .map(
        collapseNodeIfParentNodeIsCollapse,
      ) as FinTreeNode<BusinessCaseGroup>[];
  },
);

export const selectBusinessCaseCadrTreeMenuData = createSelector(
  selectBusinessCaseCadrTemplate,
  selectExtendedGroups,
  selectBusinessCaseInformation,
  selectHighlightedTopGroupKey,
  (
    businessCaseCadrTemplate,
    extendedGroups,
    information,
    highlightedTopGroupKey,
  ): FinTreeNode<CadrGroup>[] => {
    if (
      !businessCaseCadrTemplate?.groupsOrdered ||
      businessCaseCadrTemplate?.groupsOrdered?.length === 0
    ) {
      return [];
    }

    return businessCaseCadrTemplate?.groupsOrdered
      ?.map((group: CadrGroup) =>
        mapDataRoomOrderedGroup(
          group,
          businessCaseCadrTemplate.fields,
          information,
          extendedGroups,
          highlightedTopGroupKey,
        ),
      )
      .map(collapseNodeIfParentNodeIsCollapse) as FinTreeNode<CadrGroup>[];
  },
);

const selectApplications = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): Application[] => state.applications,
);

const selectInvitations = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): Invitation[] => state.invitations,
);

export const selectIsCommissionFieldDisabled = createSelector(
  selectApplications,
  selectInvitations,
  (applications: Application[], invitations: Invitation[]): boolean => {
    return (
      applications?.some(
        (app) => app.state === 'SUBMITTED' || app.state === 'ACCEPTED',
      ) ||
      invitations?.some(
        (inv) =>
          inv.invitationStatus === 'PENDING' ||
          inv.invitationStatus === 'ACCEPTED',
      )
    );
  },
);
