import { createSelector } from '@ngrx/store';
import { selectShowApplicationView } from './applications.selectors';
import {
  selectBusinessCaseDashboardState,
  selectCustomerNamesByKey,
  selectIsBusinessCaseMiscellaneous,
  selectLastVisitedUrl,
  selectLeadPartner,
  selectParticipationTabMiscellaneousVisibility,
} from './business-case.selectors';
import {
  selectIsCustomerLeadAndContactPersons,
  selectIsCustomerParticipantAndCanManageOwnCaseUsers,
} from './collaboration-managements.selectors';
import { selectInvitationTabVisibility } from './invitations.selectors';

export const selectAdministrationActivateGuardData = createSelector(
  selectLastVisitedUrl,
  selectIsCustomerParticipantAndCanManageOwnCaseUsers,
  selectIsCustomerLeadAndContactPersons,
  (
    lastUrl,
    isCustomerParticipantAndCanManageOwnCaseUsers,
    isCustomerLeadAnCanManagePortalUsersAndContactPersons,
  ) => ({
    lastUrl,
    isCustomerParticipantAndCanManageOwnCaseUsers,
    isCustomerLeadAnCanManagePortalUsersAndContactPersons,
  }),
);

export const selectFinancingDetailsMiscCaseWithParticipantRealEstateVisibility =
  createSelector(
    selectIsBusinessCaseMiscellaneous,
    selectInvitationTabVisibility,
    selectShowApplicationView,
    selectParticipationTabMiscellaneousVisibility,
    (
      isMisc,
      invitationVisibility,
      applicationVisibility,
      participationVisibility,
    ) => {
      if (!isMisc) {
        return true;
      }
      return (
        invitationVisibility || applicationVisibility || participationVisibility
      );
    },
  );

export const selectFinancingPartners = createSelector(
  selectBusinessCaseDashboardState,
  (state) => state.financingPartners,
);

export const selectFinancingPartnersData = createSelector(
  selectFinancingPartners,
  selectLeadPartner,
  selectCustomerNamesByKey,
  (financingPartnersData, leadPartner, customerNamesByKey) => {
    const lead = customerNamesByKey[leadPartner?.customerKey] || { name: '' };
    return financingPartnersData
      .map((financingPartner) => ({
        ...financingPartner,
        lead: financingPartner.partnerName === lead.name,
      }))
      .sort((a, b) => b.participationAmount - a.participationAmount);
  },
);
