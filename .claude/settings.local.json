{"permissions": {"allow": ["mcp__nx-mcp__nx_workspace", "mcp__nx-mcp__nx_project_details", "Bash(npx nx show:*)", "Bash(npx nx graph:*)", "mcp__fincloud-ui__search_components", "mcp__fincloud-ui__get_component_documentation", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__fin-ui-mcp__search_components", "mcp__fin-ui-mcp__get_component_documentation", "mcp__fin-ui-mcp__get_import_suggestions", "Bash(mv psot-hog-track-metadata.ts post-hog-track-metadata.ts)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["nx-mcp", "context7", "fincloud-ui"]}