{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@fincloud/components": ["libs/components/src/index.ts"], "@fincloud/components/alerts": ["libs/components/alerts/src/index.ts"], "@fincloud/components/animations": ["libs/components/animations/src/index.ts"], "@fincloud/components/avatar": ["libs/components/avatar/src/index.ts"], "@fincloud/components/azure-map": ["libs/components/azure-map/src/index.ts"], "@fincloud/components/backgrounds": ["libs/components/backgrounds/src/index.ts"], "@fincloud/components/booleans": ["libs/components/booleans/src/index.ts"], "@fincloud/components/business-case": ["libs/components/business-case/src/index.ts"], "@fincloud/components/buttons": ["libs/components/buttons/src/index.ts"], "@fincloud/components/charts": ["libs/components/charts/src/index.ts"], "@fincloud/components/chat": ["libs/components/chat/src/index.ts"], "@fincloud/components/color-picker": ["libs/components/color-picker/src/index.ts"], "@fincloud/components/composite": ["libs/components/composite/src/index.ts"], "@fincloud/components/copy-text": ["libs/components/copy-text/src/index.ts"], "@fincloud/components/dashboard": ["libs/components/dashboard/src/index.ts"], "@fincloud/components/data-grid": ["libs/components/data-grid/src/index.ts"], "@fincloud/components/data-room": ["libs/components/data-room/src/index.ts"], "@fincloud/components/date": ["libs/components/date/src/index.ts"], "@fincloud/components/directives": ["libs/components/directives/src/index.ts"], "@fincloud/components/dots-loader": ["libs/components/dots-loader/src/index.ts"], "@fincloud/components/dropdowns": ["libs/components/dropdowns/src/index.ts"], "@fincloud/components/element-count": ["libs/components/element-count/src/index.ts"], "@fincloud/components/error": ["libs/components/error/src/index.ts"], "@fincloud/components/expandable-filter": ["libs/components/expandable-filter/src/index.ts"], "@fincloud/components/feedback": ["libs/components/feedback/src/index.ts"], "@fincloud/components/field-label-and-actions": ["libs/components/field-label-and-actions/src/index.ts"], "@fincloud/components/files": ["libs/components/files/src/index.ts"], "@fincloud/components/formly": ["libs/components/formly/src/index.ts"], "@fincloud/components/forms": ["libs/components/forms/src/index.ts"], "@fincloud/components/group-visibility-status-info": ["libs/components/group-visibility-status-info/src/index.ts"], "@fincloud/components/guide-step": ["libs/components/guide-step/src/index.ts"], "@fincloud/components/hint": ["libs/components/hint/src/index.ts"], "@fincloud/components/horizontal-divider": ["libs/components/horizontal-divider/src/index.ts"], "@fincloud/components/icons": ["libs/components/icons/src/index.ts"], "@fincloud/components/image-cropper": ["libs/components/image-cropper/src/index.ts"], "@fincloud/components/input-base": ["libs/components/input-base/src/index.ts"], "@fincloud/components/layout": ["libs/components/layout/src/index.ts"], "@fincloud/components/linked-field-banner": ["libs/components/linked-field-banner/src/index.ts"], "@fincloud/components/lists": ["libs/components/lists/src/index.ts"], "@fincloud/components/lists-filters": ["libs/components/lists-filters/src/index.ts"], "@fincloud/components/loaders": ["libs/components/loaders/src/index.ts"], "@fincloud/components/location": ["libs/components/location/src/index.ts"], "@fincloud/components/message-panel": ["libs/components/message-panel/src/index.ts"], "@fincloud/components/message-status": ["libs/components/message-status/src/index.ts"], "@fincloud/components/modals": ["libs/components/modals/src/index.ts"], "@fincloud/components/navigation": ["libs/components/navigation/src/index.ts"], "@fincloud/components/number": ["libs/components/number/src/index.ts"], "@fincloud/components/onboarding-tips": ["libs/components/onboarding-tips/src/index.ts"], "@fincloud/components/organization-logo-renderer": ["libs/components/organization-logo-renderer/src/index.ts"], "@fincloud/components/pdf": ["libs/components/pdf/src/index.ts"], "@fincloud/components/progress-bar": ["libs/components/progress-bar/src/index.ts"], "@fincloud/components/refs": ["libs/components/refs/src/index.ts"], "@fincloud/components/refs-sidebar": ["libs/components/refs-sidebar/src/index.ts"], "@fincloud/components/search-filter": ["libs/components/search-filter/src/index.ts"], "@fincloud/components/section-selection-tree": ["libs/components/section-selection-tree/src/index.ts"], "@fincloud/components/select-group-tree": ["libs/components/select-group-tree/src/index.ts"], "@fincloud/components/selects": ["libs/components/selects/src/index.ts"], "@fincloud/components/slider": ["libs/components/slider/src/index.ts"], "@fincloud/components/status-badge": ["libs/components/status-badge/src/index.ts"], "@fincloud/components/stepper": ["libs/components/stepper/src/index.ts"], "@fincloud/components/styles": ["libs/components/styles/src/index.ts"], "@fincloud/components/text": ["libs/components/text/src/index.ts"], "@fincloud/components/third-party-modules": ["libs/components/third-party-modules/src/index.ts"], "@fincloud/components/tooltip": ["libs/components/tooltip/src/index.ts"], "@fincloud/components/trees": ["libs/components/trees/src/index.ts"], "@fincloud/components/truncated-text": ["libs/components/truncated-text/src/index.ts"], "@fincloud/components/user": ["libs/components/user/src/index.ts"], "@fincloud/components/utils": ["libs/components/utils/src/index.ts"], "@fincloud/core": ["libs/core/src/index.ts"], "@fincloud/core-state": ["libs/core-state/src/index.ts"], "@fincloud/core-state/auth-tokens": ["libs/core-state/auth-tokens/src/index.ts"], "@fincloud/core-state/customer": ["libs/core-state/customer/src/index.ts"], "@fincloud/core-state/facilities": ["libs/core-state/facilities/src/index.ts"], "@fincloud/core-state/neo-gpt": ["libs/core-state/neo-gpt/src/index.ts"], "@fincloud/core-state/user": ["libs/core-state/user/src/index.ts"], "@fincloud/core/aspect-ratio": ["libs/core/aspect-ratio/src/index.ts"], "@fincloud/core/auth": ["libs/core/auth/src/index.ts"], "@fincloud/core/billing": ["libs/core/billing/src/index.ts"], "@fincloud/core/business-case": ["libs/core/business-case/src/index.ts"], "@fincloud/core/collections": ["libs/core/collections/src/index.ts"], "@fincloud/core/company": ["libs/core/company/src/index.ts"], "@fincloud/core/config": ["libs/core/config/src/index.ts"], "@fincloud/core/contract": ["libs/core/contract/src/index.ts"], "@fincloud/core/data-structure": ["libs/core/data-structure/src/index.ts"], "@fincloud/core/date": ["libs/core/date/src/index.ts"], "@fincloud/core/demo-snapshot": ["libs/core/demo-snapshot/src/index.ts"], "@fincloud/core/directives": ["libs/core/directives/src/index.ts"], "@fincloud/core/document": ["libs/core/document/src/index.ts"], "@fincloud/core/drag-drop": ["libs/core/drag-drop/src/index.ts"], "@fincloud/core/dynamic-form-fields": ["libs/core/dynamic-form-fields/src/index.ts"], "@fincloud/core/error-handling": ["libs/core/error-handling/src/index.ts"], "@fincloud/core/feedback": ["libs/core/feedback/src/index.ts"], "@fincloud/core/files": ["libs/core/files/src/index.ts"], "@fincloud/core/filters": ["libs/core/filters/src/index.ts"], "@fincloud/core/focus": ["libs/core/focus/src/index.ts"], "@fincloud/core/form": ["libs/core/form/src/index.ts"], "@fincloud/core/form-field": ["libs/core/form-field/src/index.ts"], "@fincloud/core/formly": ["libs/core/formly/src/index.ts"], "@fincloud/core/fullscreen": ["libs/core/fullscreen/src/index.ts"], "@fincloud/core/host": ["libs/core/host/src/index.ts"], "@fincloud/core/hotkeys": ["libs/core/hotkeys/src/index.ts"], "@fincloud/core/hover": ["libs/core/hover/src/index.ts"], "@fincloud/core/jwt-token": ["libs/core/jwt-token/src/index.ts"], "@fincloud/core/layout": ["libs/core/layout/src/index.ts"], "@fincloud/core/location": ["libs/core/location/src/index.ts"], "@fincloud/core/math": ["libs/core/math/src/index.ts"], "@fincloud/core/modal": ["libs/core/modal/src/index.ts"], "@fincloud/core/neo-gpt": ["libs/core/neo-gpt/src/index.ts"], "@fincloud/core/open-neoloan-in-new-tab": ["libs/core/open-neoloan-in-new-tab/src/index.ts"], "@fincloud/core/overlays": ["libs/core/overlays/src/index.ts"], "@fincloud/core/patches": ["libs/core/patches/src/index.ts"], "@fincloud/core/pipes": ["libs/core/pipes/src/index.ts"], "@fincloud/core/regional-settings": ["libs/core/regional-settings/src/index.ts"], "@fincloud/core/rxjs": ["libs/core/rxjs/src/index.ts"], "@fincloud/core/scroll": ["libs/core/scroll/src/index.ts"], "@fincloud/core/selects": ["libs/core/selects/src/index.ts"], "@fincloud/core/services": ["libs/core/services/src/index.ts"], "@fincloud/core/socket": ["libs/core/socket/src/index.ts"], "@fincloud/core/toast": ["libs/core/toast/src/index.ts"], "@fincloud/core/tour-translate3d-override": ["libs/core/tour-translate3d-override/src/index.ts"], "@fincloud/core/types": ["libs/core/types/src/index.ts"], "@fincloud/core/url": ["libs/core/url/src/index.ts"], "@fincloud/core/user": ["libs/core/user/src/index.ts"], "@fincloud/core/utils": ["libs/core/utils/src/index.ts"], "@fincloud/core/uuid": ["libs/core/uuid/src/index.ts"], "@fincloud/neoshare": ["libs/neoshare/src/index.ts"], "@fincloud/neoshare/account-management": ["libs/neoshare/account-management/src/index.ts"], "@fincloud/neoshare/auth": ["libs/neoshare/auth/src/index.ts"], "@fincloud/neoshare/base-create-update-business-case": ["libs/neoshare/base-create-update-business-case/src/index.ts"], "@fincloud/neoshare/base-create-update-company": ["libs/neoshare/base-create-update-company/src/index.ts"], "@fincloud/neoshare/business-case": ["libs/neoshare/business-case/src/index.ts"], "@fincloud/neoshare/business-case-collaboration": ["libs/neoshare/business-case-collaboration/src/index.ts"], "@fincloud/neoshare/business-case-fields": ["libs/neoshare/business-case-fields/src/index.ts"], "@fincloud/neoshare/business-case-modal-base": ["libs/neoshare/business-case-modal-base/src/index.ts"], "@fincloud/neoshare/chat": ["libs/neoshare/chat/src/index.ts"], "@fincloud/neoshare/company-management": ["libs/neoshare/company-management/src/index.ts"], "@fincloud/neoshare/contract": ["libs/neoshare/contract/src/index.ts"], "@fincloud/neoshare/data-room": ["libs/neoshare/data-room/src/index.ts"], "@fincloud/neoshare/document": ["libs/neoshare/document/src/index.ts"], "@fincloud/neoshare/document-placeholder": ["libs/neoshare/document-placeholder/src/index.ts"], "@fincloud/neoshare/field-information-actions": ["libs/neoshare/field-information-actions/src/index.ts"], "@fincloud/neoshare/folder-structure": ["libs/neoshare/folder-structure/src/index.ts"], "@fincloud/neoshare/guards": ["libs/neoshare/guards/src/index.ts"], "@fincloud/neoshare/kpis": ["libs/neoshare/kpis/src/index.ts"], "@fincloud/neoshare/neogpt-chat": ["libs/neoshare/neogpt-chat/src/index.ts"], "@fincloud/neoshare/notification-system": ["libs/neoshare/notification-system/src/index.ts"], "@fincloud/neoshare/services": ["libs/neoshare/services/src/index.ts"], "@fincloud/neoshare/socket": ["libs/neoshare/socket/src/index.ts"], "@fincloud/neoshare/template-field": ["libs/neoshare/template-field/src/index.ts"], "@fincloud/neoshare/todos-management": ["libs/neoshare/todos-management/src/index.ts"], "@fincloud/neoshare/user-management": ["libs/neoshare/user-management/src/index.ts"], "@fincloud/neoshare/user-roles-checkboxes": ["libs/neoshare/user-roles-checkboxes/src/index.ts"], "@fincloud/state": ["libs/state/src/index.ts"], "@fincloud/state/access": ["libs/state/access/src/index.ts"], "@fincloud/state/account-management": ["libs/state/account-management/src/index.ts"], "@fincloud/state/applications": ["libs/state/applications/src/index.ts"], "@fincloud/state/apps-integration": ["libs/state/apps-integration/src/index.ts"], "@fincloud/state/auth-tokens": ["libs/state/auth-tokens/src/index.ts"], "@fincloud/state/business-case": ["libs/state/business-case/src/index.ts"], "@fincloud/state/business-case-real-estate": ["libs/state/business-case-real-estate/src/index.ts"], "@fincloud/state/chat": ["libs/state/chat/src/index.ts"], "@fincloud/state/company-analysis": ["libs/state/company-analysis/src/index.ts"], "@fincloud/state/company-portal": ["libs/state/company-portal/src/index.ts"], "@fincloud/state/contracts": ["libs/state/contracts/src/index.ts"], "@fincloud/state/customer": ["libs/state/customer/src/index.ts"], "@fincloud/state/dashboard": ["libs/state/dashboard/src/index.ts"], "@fincloud/state/data-room": ["libs/state/data-room/src/index.ts"], "@fincloud/state/document": ["libs/state/document/src/index.ts"], "@fincloud/state/environment": ["libs/state/environment/src/index.ts"], "@fincloud/state/facilities": ["libs/state/facilities/src/index.ts"], "@fincloud/state/faq": ["libs/state/faq/src/index.ts"], "@fincloud/state/folder-structure": ["libs/state/folder-structure/src/index.ts"], "@fincloud/state/inbox-documents": ["libs/state/inbox-documents/src/index.ts"], "@fincloud/state/invitation": ["libs/state/invitation/src/index.ts"], "@fincloud/state/kpi": ["libs/state/kpi/src/index.ts"], "@fincloud/state/login": ["libs/state/login/src/index.ts"], "@fincloud/state/metareducers": ["libs/state/metareducers/src/index.ts"], "@fincloud/state/neogpt-chat": ["libs/state/neogpt-chat/src/index.ts"], "@fincloud/state/next-folder": ["libs/state/next-folder/src/index.ts"], "@fincloud/state/notifications": ["libs/state/notifications/src/index.ts"], "@fincloud/state/router": ["libs/state/router/src/index.ts"], "@fincloud/state/side-navigations": ["libs/state/side-navigations/src/index.ts"], "@fincloud/state/todos-management": ["libs/state/todos-management/src/index.ts"], "@fincloud/state/user": ["libs/state/user/src/index.ts"], "@fincloud/state/user-onboarding": ["libs/state/user-onboarding/src/index.ts"], "@fincloud/state/user-settings": ["libs/state/user-settings/src/index.ts"], "@fincloud/state/users": ["libs/state/users/src/index.ts"], "@fincloud/state/utils": ["libs/state/utils/src/index.ts"], "@fincloud/swagger-generator/application": ["libs/swagger-generator/application/src/index.ts"], "@fincloud/swagger-generator/authorization-server": ["libs/swagger-generator/authorization-server/src/index.ts"], "@fincloud/swagger-generator/billing": ["libs/swagger-generator/billing/src/index.ts"], "@fincloud/swagger-generator/business-case-manager": ["libs/swagger-generator/business-case-manager/src/index.ts"], "@fincloud/swagger-generator/communication": ["libs/swagger-generator/communication/src/index.ts"], "@fincloud/swagger-generator/company": ["libs/swagger-generator/company/src/index.ts"], "@fincloud/swagger-generator/contract-management": ["libs/swagger-generator/contract-management/src/index.ts"], "@fincloud/swagger-generator/dashboard": ["libs/swagger-generator/dashboard/src/index.ts"], "@fincloud/swagger-generator/demo": ["libs/swagger-generator/demo/src/index.ts"], "@fincloud/swagger-generator/doc-info-filler": ["libs/swagger-generator/doc-info-filler/src/index.ts"], "@fincloud/swagger-generator/document": ["libs/swagger-generator/document/src/index.ts"], "@fincloud/swagger-generator/document-forwarding": ["libs/swagger-generator/document-forwarding/src/index.ts"], "@fincloud/swagger-generator/document-generator": ["libs/swagger-generator/document-generator/src/index.ts"], "@fincloud/swagger-generator/document-preview": ["libs/swagger-generator/document-preview/src/index.ts"], "@fincloud/swagger-generator/document-signing": ["libs/swagger-generator/document-signing/src/index.ts"], "@fincloud/swagger-generator/exchange": ["libs/swagger-generator/exchange/src/index.ts"], "@fincloud/swagger-generator/external-integrations": ["libs/swagger-generator/external-integrations/src/index.ts"], "@fincloud/swagger-generator/feedback": ["libs/swagger-generator/feedback/src/index.ts"], "@fincloud/swagger-generator/financing-details": ["libs/swagger-generator/financing-details/src/index.ts"], "@fincloud/swagger-generator/handelsregister": ["libs/swagger-generator/handelsregister/src/index.ts"], "@fincloud/swagger-generator/internal-tools": ["libs/swagger-generator/internal-tools/src/index.ts"], "@fincloud/swagger-generator/neo-gpt": ["libs/swagger-generator/neo-gpt/src/index.ts"], "@fincloud/swagger-generator/notification": ["libs/swagger-generator/notification/src/index.ts"], "@fincloud/swagger-generator/platform-notification": ["libs/swagger-generator/platform-notification/src/index.ts"], "@fincloud/swagger-generator/portal": ["libs/swagger-generator/portal/src/index.ts"], "@fincloud/types": ["libs/types/src/index.ts"], "@fincloud/types/enums": ["libs/types/enums/src/index.ts"], "@fincloud/types/models": ["libs/types/models/src/index.ts"], "@fincloud/utils": ["libs/utils/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}