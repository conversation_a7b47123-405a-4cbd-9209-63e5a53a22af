#!/usr/bin/env node

import { kebabCase } from 'change-case';
import { glob } from 'glob';
import { basename, dirname, join } from 'path';
import { Project } from 'ts-morph';
import { fileURLToPath } from 'url';

// Find all TypeScript files in models directories using glob
async function findModelFiles(projectRoot) {
  try {
    // Use glob to find all .ts files in any models directory
    const pattern = '**/utils/**/*.ts';
    const files = await glob(pattern, {
      cwd: projectRoot,
      absolute: true,
      ignore: [
        '**/*.d.ts',
        '**/node_modules/**',
        '**/swagger-generator/**',
        '**/index.ts',
        '**/*.module.ts',
      ],
    });

    return files;
  } catch (error) {
    console.error(`Error finding model files: ${error.message}`);
    return [];
  }
}

// Extract the first exported symbol from a source file
function getFirstExportedSymbol(sourceFile) {
  try {
    // Get all exported declarations
    const exportedDeclarations = sourceFile.getExportedDeclarations();

    for (const [exportName, declarations] of exportedDeclarations) {
      // Skip default exports (they don't follow the same naming pattern)
      if (exportName === 'default') {
        continue;
      }

      // Just get the first declaration and return its name
      if (declarations.length > 0) {
        const declaration = declarations[0];
        let symbolName = null;

        // Try to get the name from the declaration
        if (typeof declaration.getName === 'function') {
          symbolName = declaration.getName();
        }

        if (symbolName) {
          return {
            name: symbolName,
            exportName: exportName,
          };
        }
      }
    }
  } catch (error) {
    console.error(
      `Error processing exports for ${sourceFile.getFilePath()}: ${error.message}`,
    );
  }

  return null;
}

// Validate naming convention for a single file
function validateFile(filePath, sourceFile) {
  const results = {
    filePath,
    passed: true,
    issues: [],
    exportedSymbol: null,
  };

  // Get filename without extension
  const fileName = basename(filePath, '.ts');

  // Get first exported symbol
  const exportedSymbol = getFirstExportedSymbol(sourceFile);
  results.exportedSymbol = exportedSymbol;

  // If no exports, that's an issue
  if (!exportedSymbol) {
    results.passed = false;
    results.issues.push({
      type: 'no_exports',
      message: `File has no exported symbols`,
    });
    return results;
  }

  // Check if filename matches the expected kebab-case version of the symbol name
  const expectedFileName = kebabCase(exportedSymbol.name);

  if (fileName !== expectedFileName) {
    results.passed = false;
    results.issues.push({
      type: 'naming_mismatch',
      message: `Expected filename '${expectedFileName}.ts' for exported symbol '${exportedSymbol.name}', but found '${fileName}.ts'`,
      symbol: exportedSymbol.name,
      expectedFileName: expectedFileName,
      actualFileName: fileName,
    });
  }

  return results;
}

// Attempt to rename a file using ts-morph
function tryRenameFile(sourceFile, expectedFileName, project) {
  const currentPath = sourceFile.getFilePath();
  const dir = dirname(currentPath);
  const currentFileName = basename(currentPath, '.ts');
  const targetPath = join(dir, `${expectedFileName}.ts`);

  // Check if current and target are the same (shouldn't happen, but safety check)
  if (currentFileName === expectedFileName) {
    return {
      renamed: false,
      reason: 'Already correctly named',
      targetPath,
    };
  }

  // Check if target file already exists in the project
  const existingFile = project.getSourceFile(targetPath);
  if (existingFile) {
    return {
      renamed: false,
      reason: 'Target file already exists',
      targetPath,
    };
  }

  try {
    // Use ts-morph's move functionality which handles file system operations
    sourceFile.move(targetPath);
    return {
      renamed: true,
      reason: 'Successfully renamed',
      targetPath,
    };
  } catch (error) {
    return {
      renamed: false,
      reason: `Rename failed: ${error.message}`,
      targetPath,
    };
  }
}

// Main validation function
async function validateNamingConventions() {
  console.log('🔍 Validating TypeScript model naming conventions...\n');

  // Get current directory
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = dirname(__filename);
  const projectRoot = __dirname;

  // Find all model files
  console.log('📁 Scanning for model files...');
  const modelFiles = await findModelFiles(projectRoot);

  if (modelFiles.length === 0) {
    console.log('❌ No TypeScript model files found in any models/ directory');
    return;
  }

  console.log(`📄 Found ${modelFiles.length} model files\n`);

  // Initialize ts-morph project
  const project = new Project({
    tsConfigFilePath: join(projectRoot, 'tsconfig.base.json'),
  });

  // Add all model files to the project
  // for (const filePath of modelFiles) {
  //   try {
  //     project.addSourceFileAtPath(filePath);
  //   } catch (error) {
  //     console.error(`❌ Failed to add file ${filePath}: ${error.message}`);
  //   }
  // }

  // Validate each file
  const results = [];
  const sourceFiles = project.getSourceFiles(modelFiles);

  for (const sourceFile of sourceFiles) {
    const filePath = sourceFile.getFilePath();
    const result = validateFile(filePath, sourceFile);
    result.sourceFile = sourceFile; // Store source file reference for renaming
    results.push(result);
  }

  // Generate report
  let passedCount = 0;
  let failedCount = 0;

  // Count results
  for (const result of results) {
    if (result.passed) {
      passedCount++;
    } else {
      failedCount++;
    }
  }

  // Process failed files - attempt to rename them
  let renamedCount = 0;
  let skippedCount = 0;

  if (failedCount > 0) {
    console.log('🔧 ATTEMPTING TO FIX NAMING CONVENTION VIOLATIONS');
    console.log('═'.repeat(50));

    for (const result of results) {
      if (!result.passed && result.exportedSymbol) {
        const relativePath = result.filePath.replace(projectRoot, '.');
        const expectedFileName = kebabCase(result.exportedSymbol.name);

        console.log(`❌ ${relativePath}`);

        for (const issue of result.issues) {
          console.log(`   └─ ${issue.message}`);
        }

        if (result.exportedSymbol) {
          console.log(`   📤 Exports: ${result.exportedSymbol.name}`);
        }

        // Attempt to rename the file using ts-morph
        const renameResult = tryRenameFile(
          result.sourceFile,
          expectedFileName,
          project,
        );

        if (renameResult.renamed) {
          renamedCount++;
          const newRelativePath = renameResult.targetPath.replace(
            projectRoot,
            '.',
          );
          console.log(`   ✅ Renamed to: ${newRelativePath}`);
        } else {
          skippedCount++;
          console.log(`   ⚠️  Could not rename: ${renameResult.reason}`);
        }

        console.log();
      }
    }
  } else {
    console.log('🎉 All files follow the naming convention!');
  }

  // Summary
  console.log('📈 SUMMARY');
  console.log('═'.repeat(30));
  console.log(`✅ Passed: ${passedCount}`);
  console.log(`❌ Failed: ${failedCount}`);
  console.log(`📄 Total: ${passedCount + failedCount}`);

  if (failedCount > 0) {
    console.log(`🔧 Renamed: ${renamedCount}`);
    console.log(`⚠️  Skipped: ${skippedCount}`);

    console.log('\n💡 NAMING CONVENTION RULES:');
    console.log(
      '  • Filenames should be in kebab-case (e.g., user-profile.ts)',
    );
    console.log(
      '  • Exported symbols should be in PascalCase (e.g., UserProfile)',
    );
    console.log(
      '  • The filename should match the exported symbol name converted to kebab-case',
    );

    if (renamedCount > 0) {
      console.log(`\n💾 Saving changes to file system...`);
      try {
        await project.save();
        console.log(`🎉 Successfully renamed ${renamedCount} files!`);
        if (skippedCount > 0) {
          console.log(
            `⚠️  ${skippedCount} files could not be renamed (likely due to conflicts).`,
          );
        }
      } catch (error) {
        console.error(`❌ Failed to save changes: ${error.message}`);
        process.exit(1);
      }
    }

    // Exit with error code only if no files were renamed
    if (renamedCount === 0) {
      process.exit(1);
    }
  } else {
    console.log('\n🎉 All files follow the naming convention!');
  }
}

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled error:', error);
  process.exit(1);
});

// Run the validation
validateNamingConventions().catch((error) => {
  console.error('❌ Validation failed:', error);
  process.exit(1);
});
