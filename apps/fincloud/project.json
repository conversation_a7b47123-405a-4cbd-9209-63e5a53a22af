{"name": "fincloud", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/fincloud/src", "i18n": {"sourceLocale": {"code": "de", "baseHref": "/"}, "locales": {"en": "apps/fincloud/src/locale/messages.en.json"}}, "tags": ["app:fincloud"], "targets": {"build": {"executor": "@nx/angular:webpack-browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/fincloud", "index": "apps/fincloud/src/index.html", "main": "apps/fincloud/src/main.ts", "polyfills": ["apps/fincloud/src/polyfills.ts"], "tsConfig": "apps/fincloud/tsconfig.app.json", "inlineStyleLanguage": "scss", "i18nMissingTranslation": "warning", "assets": ["apps/fincloud/src/favicon.ico", "apps/fincloud/src/assets", {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "assets"}, {"glob": "pdf.worker.min.mjs", "input": "node_modules/pdfjs-dist/build/", "output": "assets/vendors"}, {"glob": "**/*", "input": "node_modules/@fincloud/ui/assets", "output": "assets"}, {"glob": "**/*", "input": "node_modules/ngx-scrollbar/assets/", "output": "assets"}], "styles": ["node_modules/@fincloud/ui/styles/base.css", "node_modules/@fincloud/ui/styles/index.scss", "apps/fincloud/src/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["libs/components/", "node_modules/"]}, "scripts": []}, "configurations": {"production": {"customWebpackConfig": {"path": "apps/fincloud/webpack.config.js"}, "index": {"input": "apps/fincloud/src/index.prod.html", "output": "index.html"}, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "9mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "42kb"}], "fileReplacements": [{"replace": "apps/fincloud/src/environments/environment.ts", "with": "apps/fincloud/src/environments/environment.prod.ts"}], "outputHashing": "all", "localize": true, "sourceMap": {"hidden": true}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "en": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "localize": ["en"]}}, "defaultConfiguration": "production"}, "serve": {"executor": "@nx/angular:module-federation-dev-server", "options": {"port": 4200, "publicHost": "http://localhost:4200"}, "configurations": {"production": {"buildTarget": "fincloud:build:production"}, "development": {"proxyConfig": "apps/fincloud/proxy.conf.json", "buildTarget": "fincloud:build:development"}, "en": {"proxyConfig": "apps/fincloud/proxy.conf.json", "buildTarget": "fincloud:build:en"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "fincloud:build", "outputPath": "apps/fincloud/src/locale"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"quiet": true}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/fincloud/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "fincloud:build", "port": 4200, "spa": true}}}}