@if (navigationsAreOpen$ | async; as navigation) {
  @if (businessCaseDataRoomTab$ | async; as businessCaseDataRoomTab) {
    @if (!groupsTemplateFieldsInitial?.length) {
      @if (isCustomerLeadPartner$ | async) {
        <div
          class="tw-flex tw-flex-col tw-bg-color-surface-primary"
          [style.height]="emptyContentHeight$ | async"
        >
          <ng-container
            [ngTemplateOutlet]="warningMessage"
            [ngTemplateOutletContext]="{ companyData: businessCaseDataRoomTab }"
          ></ng-container>
        </div>
      } @else {
        <ng-container
          [ngTemplateOutlet]="warningMessageParticipant"
        ></ng-container>
      }
    } @else {
      <app-sidebar-content-manager
        class="tw-h-full"
        [sidePanelStyles]="{ height: sidePanelHeight$ | async }"
        [pagePanelIsOpen]="navigation.pageNavigationIsOpen"
        (toggle)="toggleNavigation(navigation)"
        cdkDropListGroup
      >
        <ng-template #navPanel>
          <ng-container [ngTemplateOutlet]="navPanelContainer"></ng-container>
        </ng-template>

        <ng-template #mainPanel>
          <ng-container
            [ngTemplateOutlet]="mainPanelContainer"
            [ngTemplateOutletContext]="{
              companyData: businessCaseDataRoomTab,
            }"
          ></ng-container>
        </ng-template>
      </app-sidebar-content-manager>
    }
  }

  <ng-template #navPanelContainer>
    <div class="tw-flex tw-flex-col tw-h-full">
      <fin-scrollbar class="tw-grow">
        <app-business-case-cadr-sidebar></app-business-case-cadr-sidebar>
      </fin-scrollbar>
    </div>
  </ng-template>

  <ng-template #mainPanelContainer let-companyData="companyData">
    <div class="tw-flex tw-flex-col tw-bg-color-surface-primary">
      @if (isCustomerLeadPartner$ | async) {
        <ng-container
          [ngTemplateOutlet]="warningMessage"
          [ngTemplateOutletContext]="{ companyData }"
        ></ng-container>
      }
      @if (company?.companyTemplate) {
        <div class="tw-px-[2.4rem] tw-py-[1.2rem]">
          <fin-search
            dynamicErrorSpace="dynamic"
            [formControl]="searchControl"
            [showLoader]="false"
            [autocomplete]="false"
            [size]="finSize.M"
          ></fin-search>
        </div>
      }
    </div>

    <fin-scrollbar
      appBusinessCaseMainContentScroll
      [smoothResize]="true"
      [ngStyle]="{ height: contentWrapperHeight$ | async }"
    >
      <div
        class="tw-px-[2.4rem] tw-pb-[2.4rem] tw-w-full"
        [ngStyle]="{ 'min-height': contentHeight$ | async }"
      >
        @if (groupsTemplateFields?.length) {
          <div>
            @for (
              group of groupsTemplateFields;
              track trackByKey(groupIndex, group);
              let groupIndex = $index
            ) {
              <div
                class="tw-flex tw-flex-col tw-gap-[2.4rem]"
                (appInsideView)="onGroupVisible(group.key)"
                (elementNotInView)="onGroupNotVisible(group.key)"
                [id]="group.key"
                cdkDropList
                (cdkDropListDropped)="
                  openCreateField($event, group, groupIndex)
                "
                (cdkDropListEntered)="dropListHover(true, groupIndex)"
                (cdkDropListExited)="dropListHover(false, groupIndex)"
              >
                <div class="section-header-wrapper">
                  <div
                    [class.tw-mb-14]="
                      groupIndex !== groupsTemplateFields.length - 1
                    "
                  >
                    <fin-card-label
                      [label]="group.groupIndex + '. ' + group.value"
                    >
                      <ng-container finCardIcon>
                        @defer (on viewport) {
                          @if (isCustomerLeadPartner$ | async) {
                            <app-group-visibility-status-info
                              [group]="group"
                            ></app-group-visibility-status-info>
                          }
                        } @placeholder {
                          <div></div>
                        }
                      </ng-container>
                    </fin-card-label>
                  </div>
                </div>
                <div
                  class="tw-grid tw-grid-cols-[repeat(auto-fill,_minmax(26rem,_1fr))] tw-gap-[2.4rem]"
                >
                  @for (
                    templateField of group.templateFields;
                    track trackByKeyTemplateField($index, templateField)
                  ) {
                    <ui-template-field
                      class="tw-pointer-events-auto"
                      [id]="templateField.field.key"
                      [field]="templateField.field"
                      [information]="templateField.information"
                      [isEditMode]="editMode"
                      [templateFieldData]="templateFieldData$ | async"
                    >
                    </ui-template-field>
                  }
                </div>
                @if (group.documents.length) {
                  @if (group.rootFolder; as folderStructure) {
                    <app-folder-structure
                      [context]="folderStructureContext.BUSINESS_CASE_CADR"
                      [groupKey]="group.key"
                      [rootFolder]="folderStructure"
                      [documentFields]="group.documents"
                      [documentTemplateFieldData]="templateFieldData$ | async"
                      [editMode]="false"
                      [highlight]="highlighted$ | async"
                    >
                    </app-folder-structure>
                  }
                }
              </div>
            }
          </div>
        } @else {
          @if (hasResultAfterFiltering) {
            <div
              i18n="search.autocomplete.noResults"
              class="tw-p-[3.2rem] tw-text-color-text-tertiary tw-font-text-body-1-family tw-font-text-body-1-strong-weight tw-text-text-body-1-size tw-leading-text-body-1-line-height tw-text-center"
            >
              Keine Ergebnisse gefunden. Versuchen Sie andere Schlüsselwörter
              oder überprüfen Sie die Rechtschreibung
            </div>
          }
        }
      </div>
    </fin-scrollbar>
  </ng-template>

  <ng-template #warningMessage let-companyData="companyData">
    <fin-warning-message
      i18n-label="@@dashboard.dataRoom.information2"
      label="Sie können den unternehmensbezogenen Data Room über die
Seite des Unternehmens bearbeiten. Klicken Sie auf den
Button, wenn Sie Änderungen am unternehmensbezogenen Data
Room vornehmen möchten."
      [appearance]="finWarningMessageAppearance.INFORMATIVE"
    >
      <button
        fin-button
        [appearance]="finButtonAppearance.SECONDARY"
        [size]="finSize.L"
        (click)="
          redirectToCompanyInformationEditMode(
            companyData.customerKey,
            companyData.companyId
          )
        "
      >
        <span i18n="@@dashboard.dataRoom.button.label" class="tw-text-nowrap"
          >Zum Unternehmen</span
        >
      </button>
    </fin-warning-message>
  </ng-template>

  <ng-template #warningMessageParticipant>
    <fin-empty-state
      [type]="finEmptyStateType.BASIC"
      [title]="emptyParticipantCadrMessage"
    >
      <ng-template #finIcon>
        <fin-icon
          name="description"
          class="tw-text-color-text-tertiary"
          [size]="finSize.L"
        ></fin-icon>
      </ng-template>
    </fin-empty-state>
  </ng-template>
}
