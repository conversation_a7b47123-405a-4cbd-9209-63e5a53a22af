import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { NsUiGroupVisibilityStatusInfoModule } from '@fincloud/components/group-visibility-status-info';
import { NsUiHorizontalDividerModule } from '@fincloud/components/horizontal-divider';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiLayoutModule } from '@fincloud/components/layout';
import { NsUiSearchFilterModule } from '@fincloud/components/search-filter';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { NsCoreScrollModule } from '@fincloud/core/scroll';
import { NsBusinessCaseRefactoringModule } from '@fincloud/neoshare/business-case';
import { NsTemplateFieldModule } from '@fincloud/neoshare/template-field';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinCardLabelModule } from '@fincloud/ui/card-label';
import { FinEmptyStateModule } from '@fincloud/ui/empty-state';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import { FinSearchModule } from '@fincloud/ui/search';
import {
  FinTreeMenuModule,
  FinTreeNodeDirective,
} from '@fincloud/ui/tree-menu';
import { FinWarningMessageModule } from '@fincloud/ui/warning-message';
import { FolderStructureModule } from '../folder-structure/folder-structure.module';
import { BusinessCaseCadrSidebarComponent } from './components/business-case-cadr-sidebar/business-case-cadr-sidebar.component';
import { BusinessCaseCadrComponent } from './components/business-case-cadr/business-case-cadr.component';

@NgModule({
  declarations: [BusinessCaseCadrComponent, BusinessCaseCadrSidebarComponent],
  imports: [
    CommonModule,
    NsTemplateFieldModule,
    DragDropModule,
    NsUiSearchFilterModule,
    NsUiGroupVisibilityStatusInfoModule,
    NsUiHorizontalDividerModule,
    NsUiIconsModule,
    FinScrollbarModule,
    NsBusinessCaseRefactoringModule,
    FinSearchModule,
    FinWarningMessageModule,
    FinButtonModule,
    NsCorePipesModule,
    FinTreeMenuModule,
    FinTreeNodeDirective,
    FinIconModule,
    FinCardLabelModule,
    FolderStructureModule,
    NsUiLayoutModule,
    FinEmptyStateModule,
    NsCoreScrollModule,
  ],
  exports: [BusinessCaseCadrComponent],
})
export class BusinessCaseCadrModule {}
