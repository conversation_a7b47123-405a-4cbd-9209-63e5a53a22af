import { Injectable } from '@angular/core';
import {
  StateLibApplicationsApiActions,
  StateLibApplicationsPageActions,
  selectAllCustomerApplications,
} from '@fincloud/state/applications';
import {
  StateLibInvitationApiActions,
  StateLibInvitationPageActions,
  selectAllCustomerInvitations,
} from '@fincloud/state/invitation';
import {
  selectIsPlatformManager,
  selectUserCustomerKey,
} from '@fincloud/state/user';
import { StateLibNoopPageActions } from '@fincloud/state/utils';
import {
  Application,
  ApplicationOverviewControllerService,
  Invitation,
  InvitationControllerService,
} from '@fincloud/swagger-generator/application';
import { CollaborationBlacklistSettingsControllerService } from '@fincloud/swagger-generator/authorization-server';
import {
  Customer,
  ExchangeService,
} from '@fincloud/swagger-generator/exchange';
import { Permission } from '@fincloud/types/enums';
import { AppState, Dictionary } from '@fincloud/types/models';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { keyBy } from 'lodash-es';
import { NgxPermissionsService } from 'ngx-permissions';
import { catchError, filter, map, of, shareReplay, switchMap } from 'rxjs';
import { CasesApiActions, CasesPageActions } from '../actions';

@Injectable()
export class CasesEffects {
  loadMyQueryCasesData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CasesPageActions.loadMyQueryCases),
      switchMap((action) =>
        this.exchangeService
          .exchangeControllerLoadMyBusinessCases(action.payload)
          .pipe(
            map((data) =>
              CasesApiActions.loadMyQueryCasesSuccess({ payload: data }),
            ),
            catchError((error) =>
              of(CasesApiActions.loadMyQueryCasesFailure({ error })),
            ),
          ),
      ),
    ),
  );

  loadMyAllCasesData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CasesPageActions.loadMyAllCases),
      switchMap(() =>
        // Small limit as we do not care about the casees but whether we have any or not
        this.exchangeService
          .exchangeControllerLoadMyBusinessCases({
            caseState: 'any',
            limit: 2,
            offset: 0,
          })
          .pipe(
            map((data) =>
              CasesApiActions.loadMyAllCasesSuccess({ payload: data }),
            ),
            catchError((error) =>
              of(CasesApiActions.loadMyAllCasesFailure({ error })),
            ),
          ),
      ),
    ),
  );

  loadMyQueryOrganizationCasesData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CasesPageActions.loadMyQueryOrganizationCases),
      concatLatestFrom(() => [
        this.store.select(selectIsPlatformManager),
        of(this.ngxPermissionService.getPermission(Permission.PERM_0048)),
      ]),
      switchMap(([action, isPlatformManager, hasPermission]) => {
        if (!isPlatformManager || !hasPermission) {
          return of(
            CasesApiActions.loadMyQueryOrganizationCasesSuccess({
              payload: null,
            }),
          );
        }

        return this.exchangeService
          .exchangeControllerLoadMyOrganizationBusinessCases(action.payload)
          .pipe(
            map((data) =>
              CasesApiActions.loadMyQueryOrganizationCasesSuccess({
                payload: data,
              }),
            ),
            catchError((error) =>
              of(
                CasesApiActions.loadMyQueryOrganizationCasesFailure({ error }),
              ),
            ),
          );
      }),
    ),
  );

  loadMyAllOrganizationCasesData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CasesPageActions.loadMyAllOrganizationCases),
      concatLatestFrom(() => [
        this.store.select(selectIsPlatformManager),
        of(this.ngxPermissionService.getPermission(Permission.PERM_0048)),
      ]),
      switchMap(([_, isPlatformManager, hasPermission]) => {
        if (!isPlatformManager || !hasPermission) {
          return of(
            CasesApiActions.loadMyAllOrganizationCasesSuccess({
              payload: null,
            }),
          );
        }

        // Small limit as we do not care about the casees but whether we have any or not
        return this.exchangeService
          .exchangeControllerLoadMyOrganizationBusinessCases({
            caseState: 'any',
            limit: 2,
            offset: 0,
          })
          .pipe(
            map((data) =>
              CasesApiActions.loadMyAllOrganizationCasesSuccess({
                payload: data,
              }),
            ),
            catchError((error) =>
              of(CasesApiActions.loadMyAllOrganizationCasesFailure({ error })),
            ),
          );
      }),
    ),
  );

  loadInvitationsData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        CasesPageActions.loadInvitations,
        StateLibInvitationPageActions.setInvitations,
      ),
      concatLatestFrom(() => this.store.select(selectAllCustomerInvitations)),
      filter(([, invitations]) => invitations?.length > 0),
      switchMap(([, invitations]) => {
        return this.exchangeService
          .exchangeControllerLoadBusinessCasesByIds({
            body: {
              businessCaseIds: invitations.map((a) => a.businessCaseId),
            },
          })
          .pipe(
            map((businessCases) => {
              return invitations
                .map((i) => ({
                  invitation: i,
                  businessCase: businessCases.find(
                    (c) => c.id === i.businessCaseId,
                  ),
                }))
                .filter((invitation) => !!invitation.businessCase);
            }),
            map((data) =>
              CasesApiActions.loadInvitationsSuccess({ payload: data }),
            ),
            catchError((error) =>
              of(CasesApiActions.loadInvitationsFailure({ error })),
            ),
          );
      }),
    ),
  );

  loadNewInvitationsData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibInvitationApiActions.fetchNewInvitationsSuccess),
      switchMap((action) => {
        return this.exchangeService
          .exchangeControllerLoadBusinessCasesByIds({
            body: {
              businessCaseIds: action.payload.map(
                (invitation: Invitation) => invitation.businessCaseId,
              ),
            },
          })
          .pipe(
            map((businessCases) => {
              return action.payload
                .map((invitation: Invitation) => ({
                  invitation: invitation,
                  businessCase: businessCases.find(
                    (businessCase) =>
                      businessCase.id === invitation.businessCaseId,
                  ),
                }))
                .filter((invitation) => !!invitation.businessCase);
            }),
            map((invitations) =>
              CasesApiActions.loadNewInvitationsSuccess({
                payload: invitations,
              }),
            ),
            catchError((error) =>
              of(CasesApiActions.loadInvitationsFailure({ error })),
            ),
          );
      }),
    ),
  );

  loadApplicationsData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        CasesPageActions.loadApplications,
        StateLibApplicationsPageActions.setApplications,
      ),
      concatLatestFrom(() => this.store.select(selectAllCustomerApplications)),
      filter(([, { applications }]) => applications?.length > 0),
      switchMap(([, { applications }]) => {
        return this.exchangeService
          .exchangeControllerLoadBusinessCasesByIds({
            body: {
              businessCaseIds: applications.map((a) => a.businessCaseId),
            },
          })
          .pipe(
            map((businessCases) => {
              return applications
                .map((a) => ({
                  application: a,
                  businessCase: businessCases.find(
                    (c) => c.id === a.businessCaseId,
                  ),
                }))
                .filter((application) => !!application.businessCase);
            }),
            map((data) =>
              CasesApiActions.loadApplicationsSuccess({ payload: data }),
            ),
            catchError((error) =>
              of(CasesApiActions.loadApplicationsFailure({ error })),
            ),
          );
      }),
    ),
  );

  loadNewApplicationsData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibApplicationsApiActions.fetchNewApplicationsSuccess),
      switchMap((action) => {
        return this.exchangeService
          .exchangeControllerLoadBusinessCasesByIds({
            body: {
              businessCaseIds: action.payload.map(
                (application: Application) => application.businessCaseId,
              ),
            },
          })
          .pipe(
            map((businessCases) => {
              return action.payload
                .map((application: Application) => ({
                  application: application,
                  businessCase: businessCases.find(
                    (c) => c.id === application.businessCaseId,
                  ),
                }))
                .filter((application) => !!application.businessCase);
            }),
            map((applications) =>
              CasesApiActions.loadNewApplicationsSuccess({
                payload: applications,
              }),
            ),
            catchError((error) =>
              of(CasesApiActions.loadApplicationsFailure({ error })),
            ),
          );
      }),
    ),
  );

  loadCustomerKeyNamesData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CasesPageActions.loadCustomerKeyNames),
      concatLatestFrom(() => this.store.select(selectUserCustomerKey)),
      switchMap(([action, customerKey]) => {
        return this.collaborationBlacklistSettingsControllerService
          .getEffectiveCustomers({ customerKey })

          .pipe(
            map((res) => keyBy(res, (r) => r.key) as Dictionary<Customer>),
            shareReplay(1),
            map((data) =>
              CasesApiActions.loadCustomerKeyNamesSuccess({ payload: data }),
            ),
            catchError((error) =>
              of(CasesApiActions.loadCustomerKeyNamesFailure({ error })),
            ),
          );
      }),
    ),
  );

  fetchNewApplications$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CasesPageActions.fetchNewInvitationsAndApplications),
      concatLatestFrom(() => [
        this.store.select(selectUserCustomerKey),
        this.store.select(selectAllCustomerApplications),
      ]),
      switchMap(([, customerKey, { applications }]) =>
        this.applicationsOverviewService
          .getApplicationsForCustomer({ customerKey })
          .pipe(
            map((latestApplications) => {
              // Filter out only new applications that are not in the existing list
              const newApplications = latestApplications.filter(
                (latestApplication) =>
                  !applications.some(
                    (existingApplication) =>
                      existingApplication.id === latestApplication.id,
                  ),
              );

              if (newApplications.length > 0) {
                return StateLibApplicationsApiActions.fetchNewApplicationsSuccess(
                  {
                    payload: newApplications,
                  },
                );
              }
              return StateLibNoopPageActions.noop();
            }),
            catchError(() =>
              of(StateLibApplicationsApiActions.fetchNewApplicationsFailure()),
            ),
          ),
      ),
    ),
  );

  fetchNewInvitations$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CasesPageActions.fetchNewInvitationsAndApplications),
      concatLatestFrom(() => this.store.select(selectAllCustomerInvitations)),
      switchMap(([, invitations]) => {
        return this.invitationControllerService
          .getAllInvitationsForCustomer()
          .pipe(
            map((latestInvitations) => {
              // Filter out only new invitations that are not in the existing list
              const newInvitations = latestInvitations.filter(
                (latestInvitation) =>
                  !invitations.some(
                    (existingInvitation) =>
                      existingInvitation.id === latestInvitation.id,
                  ),
              );

              if (newInvitations.length > 0) {
                return StateLibInvitationApiActions.fetchNewInvitationsSuccess({
                  payload: newInvitations,
                });
              }
              return StateLibNoopPageActions.noop();
            }),
            catchError(() =>
              of(StateLibInvitationApiActions.fetchNewInvitationsFailure()),
            ),
          );
      }),
    ),
  );

  constructor(
    private actions$: Actions,
    private store: Store<AppState>,
    private exchangeService: ExchangeService,
    private ngxPermissionService: NgxPermissionsService,
    private collaborationBlacklistSettingsControllerService: CollaborationBlacklistSettingsControllerService,
    private applicationsOverviewService: ApplicationOverviewControllerService,
    private invitationControllerService: InvitationControllerService,
  ) {}
}
