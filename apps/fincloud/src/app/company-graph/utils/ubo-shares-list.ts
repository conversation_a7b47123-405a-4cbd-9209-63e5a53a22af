import { NetworkElement } from '../enums/network-node';
import { CompanyGraphEdge } from '../models/company-graph-edge';
import { CompanyGraphNode } from '../models/company-graph-node';
import { UBOListItem } from '../models/ubo-list-item';

export function getUBOShares(
  nodes: CompanyGraphNode[],
  edges: CompanyGraphEdge[],
): UBOListItem[] {
  const main = nodes.find(
    (node) => node.type === NetworkElement.CURRENT_COMPANY,
  );

  if (!main) {
    return [];
  }

  // Reverse ownership graph: toId -> list of incoming owners
  const adjacencyList: Record<
    string,
    { sourceId: string; targetId: string; shares: number }[]
  > = {};
  nodes.forEach((node) => {
    adjacencyList[node.id] = [];
  });

  edges.forEach((edge) => {
    adjacencyList[edge.toId].push({
      sourceId: edge.fromId,
      targetId: edge.toId,
      shares: edge?.sharesPercent ? edge.sharesPercent / 100 : 0,
    });
  });

  const rawResult: UBOListItem[] = [];

  const dfs = (
    currentNodeId: string,
    sharesSum: number,
    visited: Set<string>,
  ) => {
    const currentNode = nodes.find((node) => node.id === currentNodeId);

    if (!currentNode) {
      return;
    }

    const pathKey = `${currentNodeId}-${sharesSum}`;

    if (visited.has(pathKey)) {
      return;
    }

    visited.add(pathKey);
    const incomingEdges = adjacencyList[currentNodeId] || [];

    if (incomingEdges.length === 0 && currentNode.type === NetworkElement.UBO) {
      rawResult.push({
        id: currentNode.id,
        name: `${currentNode.firstName} ${currentNode.lastName}`,
        shares: sharesSum * 100,
      });
    }

    for (const edge of incomingEdges) {
      dfs(edge.sourceId, sharesSum * edge.shares, new Set(visited));
    }
  };

  dfs(main.id, 1, new Set());

  // Aggregate duplicates by ID
  const aggregatedResult: Record<string, UBOListItem> = {};
  for (const item of rawResult) {
    if (!aggregatedResult[item.id]) {
      aggregatedResult[item.id] = { ...item };
    } else {
      aggregatedResult[item.id].shares += item.shares;
    }
  }

  return Object.values(aggregatedResult);
}
