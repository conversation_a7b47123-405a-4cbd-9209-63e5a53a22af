import { Options } from '@angular-slider/ngx-slider';
import {
  AfterViewInit,
  Component,
  DestroyRef,
  ElementRef,
  OnDestroy,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FullScreenService } from '@fincloud/core/fullscreen';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinWarningMessageAppearance } from '@fincloud/ui/warning-message';
import { Store } from '@ngrx/store';
import {
  Observable,
  debounce,
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  shareReplay,
  skip,
  switchMap,
  take,
  tap,
  timer,
} from 'rxjs';
import { CompanyGraphPageActions } from '../../+state/actions';
import { companyGraphFeature } from '../../+state/reducers/company-graph.reducer';
import { GraphService } from '../../jointjs/services/graph.service';

@Component({
  selector: 'app-company-graph-container',
  templateUrl: './company-graph-container.component.html',
  styleUrl: './company-graph-container.component.scss',
  encapsulation: ViewEncapsulation.None,
})
export class CompanyGraphContainerComponent
  implements AfterViewInit, OnDestroy
{
  @ViewChild('canvas') canvas: ElementRef<HTMLDivElement>;
  @ViewChild('fullscreen') fullscreen: ElementRef<HTMLElement>;
  @ViewChild('minimapElement')
  minimapElement: ElementRef<HTMLDivElement>;

  loadingAppearance = FinWarningMessageAppearance.INFORMATIVE;
  finBtnAppearance = FinButtonAppearance;
  isInitial$ = this.store.select(companyGraphFeature.selectIsInitial);
  graphHasChanges$ = this.store.select(
    companyGraphFeature.selectHasCompanyChanges,
  );
  canRejectChanges$ = this.store.select(
    companyGraphFeature.selectCanRejectCompanyChanges,
  );
  hasNoGraphDataAvailable$ = this.store
    .select(companyGraphFeature.selectHasNoGraphData)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  isGraphEmptyAfterFilter$ = this.store
    .select(companyGraphFeature.selectIsGraphEmptyAfterFilter)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));
  isMinimapShown$ = this.store.select(companyGraphFeature.selectIsMinimapShown);

  isGraphEmpty$ = this.store
    .select(companyGraphFeature.selectIsGraphEmpty)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  sliderValue$: Observable<number> = this.graphService.zoomScale$.pipe(
    distinctUntilChanged(),
    debounceTime(5),
  );
  sliderOptions$: Observable<Options> = this.graphService.scaleData$.pipe(
    map((result) => {
      return {
        ceil: result.maxScale,
        floor: result.minScale,
        step: result.scaleStep,
      };
    }),
  );
  graphNestingLevel$ = this.graphService.graphNestingLevel$;
  loading$ = this.store.select(companyGraphFeature.selectIsLoading).pipe(
    distinctUntilChanged(),
    debounce((isLoading) => (isLoading ? timer(10) : timer(1000))),
    shareReplay({ bufferSize: 1, refCount: true }),
  );
  loadingMessage = $localize`:@@companyGraph.loadingMessage:Die Daten für die Darstellung werden gerade geladen. Sie können die Seite schließen, und neoshare anderweitig nutzen, ohne den Vorgang zu unterbrechen. Bitte beachten Sie, dass der Vorgang längere Zeit in Anspruch nehmen kann.`;

  get isFullScreen(): boolean {
    return this.fullscreenService.isFullScreen;
  }

  constructor(
    private graphService: GraphService,
    private store: Store,
    private fullscreenService: FullScreenService,
    private destroyRef: DestroyRef,
  ) {}

  ngAfterViewInit(): void {
    this.store
      .select(companyGraphFeature.selectCompanyGraph)
      .pipe(
        filter(
          (companyGraph) =>
            !!companyGraph.graphData.length && companyGraph.hasLoadedSettings,
        ),
        take(1),
        tap((graphModel) => {
          this.graphService.initGraph(this.canvas, graphModel);
        }),
        switchMap(() => this.graphService.subscribeViewLayoutChange()),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.store
      .select(companyGraphFeature.selectGraphModelAndSingleExpandedNodeIds)
      .pipe(
        distinctUntilChanged(
          (prev, curr) => prev.graphModel.length === curr.graphModel.length,
        ),
        filter(({ graphModel }) => !!graphModel.length),
        skip(1),
        tap(({ graphModel, singleExpandedNodeIds }) => {
          this.graphService.updateGraphData(graphModel, singleExpandedNodeIds);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.fullscreenService.fullScreenToggle$
      .pipe(
        tap(() =>
          this.graphService.createTooltip(this.fullscreenService.isFullScreen),
        ),
        debounceTime(20),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => {
        this.graphService.fitAndCenterGraphContent(undefined, false);
      });
  }

  ngOnDestroy(): void {
    this.graphService.cleanup();
    this.store.dispatch(
      CompanyGraphPageActions.companyGraphContainerDestroyed(),
    );
  }

  onFullscreenClicked(): void {
    if (!this.fullscreen?.nativeElement) {
      return;
    }
    if (this.fullscreenService.isFullScreen) {
      this.fullscreenService.closeFullScreen();
    } else {
      this.fullscreenService.openInFullScreen(this.fullscreen.nativeElement);
    }
  }

  scale(value: number): void {
    if (this.graphService.paper) {
      this.graphService.paper.scale(value, value, 0);
    }
  }

  viewChanges() {
    this.store.dispatch(CompanyGraphPageActions.expandReviewChanges());
  }

  rejectAllChanges() {
    this.store.dispatch(CompanyGraphPageActions.rejectAllReviewChanges());
  }

  toggleMinimap(): void {
    this.store.dispatch(
      CompanyGraphPageActions.toggleMinimap({
        minimapElement: this.minimapElement,
      }),
    );
  }
}
