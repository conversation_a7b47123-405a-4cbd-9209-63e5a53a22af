<div #fullscreen [class.company-graph-fullscreen-mode]="isFullScreen">
  @if (sliderOptions$ | async; as sliderOptions) {
    @if (isGraphEmpty$ | async | isFalsy) {
      <ui-zoom-slider
        class="tw-absolute tw-bottom-2 tw-right-[24.5rem] tw-z-[1]"
        [options]="sliderOptions"
        [value]="sliderValue$ | async"
        [isMinimapShown]="isMinimapShown$ | async"
        [isFullScreen]="isFullScreen"
        (changed)="scale($event)"
        (toggleMinimap)="toggleMinimap()"
      >
        <div class="minimap" #minimapElement></div>
      </ui-zoom-slider>
    }
  }
  <div class="tw-flex">
    <div
      id="canvas"
      class="canvas tw-min-w-[64.5rem] tw-flex tw-flex-col"
      [class.hide-paper-on-load]="(loading$ | async) || (isGraphEmpty$ | async)"
      #canvas
      appSyncPositionSource="canvas"
      [appSyncSourceProperties]="['height']"
    >
      <app-graph-toolbar
        [hasNoDataAvailable]="hasNoGraphDataAvailable$ | async"
        [isFullScreen]="isFullScreen"
        (fullScreenToggle)="onFullscreenClicked()"
      ></app-graph-toolbar>
      @if (
        (hasNoGraphDataAvailable$ | async) &&
        (isGraphEmptyAfterFilter$ | async | isFalsy)
      ) {
        <ng-template *ngTemplateOutlet="noDataAvailable"></ng-template>
      } @else {
        @if ((graphHasChanges$ | async) && (loading$ | async | isFalsy)) {
          <fin-warning-message
            i18n-label="@@companyGraph.hasChanges.message"
            label="Es gibt neue Änderungen im Firmennetzwerk"
            [showIcon]="true"
            [appearance]="loadingAppearance"
          >
            @if (canRejectChanges$ | async) {
              <button
                (click)="rejectAllChanges()"
                i18n="@@button.label.cancel"
                fin-button
                [appearance]="finBtnAppearance.STEALTH"
              >
                Abbrechen
              </button>
            }
            <button
              (click)="viewChanges()"
              i18n="@@companyGraph.hasChanges.btn.review"
              fin-button
              [appearance]="finBtnAppearance.SECONDARY"
            >
              Überprüfen
            </button>
          </fin-warning-message>
        }
        @if (loading$ | async) {
          @if (isInitial$ | async) {
            <fin-warning-message
              finInformative
              [label]="loadingMessage"
              [showIcon]="true"
              [appearance]="loadingAppearance"
            ></fin-warning-message>
          }
          <div class="tw-absolute tw-top-[55%] tw-left-[30%]">
            <div class="tw-flex tw-flex-col tw-items-center tw-gap-2">
              <fin-loader [hide]="false"></fin-loader>
              <div
                i18n="@@spinner.loadingMessage"
                class="heading7 graph-loading-label"
              >
                Wird geladen
              </div>
            </div>
          </div>
        } @else if (isGraphEmptyAfterFilter$ | async) {
          <ng-container *ngTemplateOutlet="noResultAfterFilter"></ng-container>
        }
      }
    </div>
    <app-graph-side-bar></app-graph-side-bar>
  </div>
</div>
@if (graphNestingLevel$ | async) {}

<ng-template #noResultAfterFilter>
  <div
    class="tw-flex tw-flex-col tw-h-full tw-items-center tw-justify-center heading8"
  >
    <div
      class="tw-text-color-text-tertiary tw-max-w-[80%] tw-text-center"
      i18n="@@companyGraph.graphFilters.noResult"
    >
      Es gibt keine Ergebnisse, die Ihren Suchkriterien entsprechen. Löschen Sie
      die angewandten Filter oder wählen Sie andere Kriterien aus.
    </div>
  </div>
</ng-template>

<ng-template #noDataAvailable>
  <div
    class="tw-m-auto heading7 tw-text-color-text-tertiary"
    i18n="@@companyGraph.noCompany.info.top"
  >
    Keine Informationen zum Unternehmensnetzwerk vorhanden.
  </div>
</ng-template>
