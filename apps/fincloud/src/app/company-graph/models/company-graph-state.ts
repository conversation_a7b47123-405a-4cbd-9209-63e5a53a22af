import {
  CompanyNetworkDto,
  SharesPercentageData,
} from '@fincloud/swagger-generator/company';
import { CollapseOrExpandNodeType } from '../enums/collapse-expand-node';
import { CompanyGraphChangeDto } from './company-graph-change-dto';
import { GraphConfiguration } from './company-graph-configuration';
import { CompanyGraphFilters } from './company-graph-filters';
import { CompanyNetwork } from './company-network-dto';

export interface CompanyGraphState {
  companyNetwork: CompanyNetwork;
  companyNetworkDto: CompanyNetworkDto;
  companyCalculatedUboDto: SharesPercentageData[];
  companyUboDto: CompanyNetworkDto;
  graphConfiguration: GraphConfiguration;
  companyChanges: CompanyGraphChangeDto;
  currentChangeIndex: number;
  error: unknown;
  isCompactView: boolean;
  isInitial: boolean;
  isLoading: boolean;
  filters: CompanyGraphFilters;
  hasNoFilterResults: boolean;
  levelsCount: number;
  selectedNodeNestingAvailability: CollapseOrExpandNodeType;
  availableRoles: string[];
  availableEntityTypes: string[];
  companyId: string;
  hasNoGraphData: boolean;
  isMinimapShown: boolean;
}
