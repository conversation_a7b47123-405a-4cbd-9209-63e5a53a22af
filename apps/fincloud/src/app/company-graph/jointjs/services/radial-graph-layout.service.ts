import { Injectable } from '@angular/core';
import { setTimeoutUnpatched } from '@fincloud/core/utils';
import { CompanyGraphElementDirection } from '@fincloud/types/enums';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { dia } from 'jointjs-plus';
import * as joint from 'jointjs-plus/joint-plus';
import { distinctUntilKeyChanged, filter, tap } from 'rxjs';
import { CompanyGraphPageActions } from '../../+state/actions';
import { companyGraphFeature } from '../../+state/reducers/company-graph.reducer';
import { CollapseOrExpandNodeType } from '../../enums/collapse-expand-node';
import { GroupLabelTypes } from '../../enums/group-label-types';
import { NetworkElement } from '../../enums/network-node';
import { CompanyGraphGroupLabelPositionData } from '../../models/company-graph-group-label-postion-data';
import { CompanyGraphModel } from '../../models/company-graph-model';
import { NodePosition } from '../../models/company-graph-node-position';
import { CompanyGraphTreeNode } from '../../models/company-graph-tree-node';
import { CompanyGraphTreeNodeGroupRoots } from '../../models/company-graph-tree-node-group-roots';
import { CompanyGraphTreeNodeGroups } from '../../models/company-graph-tree-node-groups';
import { GroupLabelType } from '../enums/label-group-type';
import { BaseShape } from '../shapes/base/base.shape';
import { Container } from '../shapes/container/container.shape';
import { GroupLabel } from '../shapes/group-label/group-label.shape';
import { extractStringAsRole } from '../utils/extract-string-role';
import { getLinkType } from '../utils/get-link-type';
import { getShapeType } from '../utils/get-sahpe-opposite-type';
import { GROUP_LABEL_MAPPING } from '../utils/group-label-mapping';

@Injectable()
export class RadialGraphLayoutService {
  private readonly rootElementPosition = { x: 0, y: 0 };
  private groupedGraphData: CompanyGraphTreeNodeGroupRoots[] = [];
  private levelsCount = 0;
  private graphRef: dia.Graph;
  private paperRef: dia.Paper;
  private currentlySelectedLevel = 1;
  private nodePositions: NodePosition[];
  private collapsedGroups: string[] = [];
  private alreadyExpandedCell: dia.Cell | undefined = undefined;
  private shadowGraph: CompanyGraphTreeNodeGroupRoots | undefined = undefined;

  isRootCellCompact = false;

  // TODO: refactor this and move it to an effect
  graphNestingLevel$ = this.store
    .select(companyGraphFeature.selectNestingLevelNodePositionsAndGroups)
    .pipe(
      filter((v) => !!v.nestingLevel),
      distinctUntilKeyChanged('nestingLevel'),
      concatLatestFrom(() => [
        this.store.select(companyGraphFeature.selectSelectedNode),
      ]),
      tap(([{ nestingLevel, nodePositions }]) => {
        if (nestingLevel !== this.currentlySelectedLevel) {
          this.store.dispatch(
            CompanyGraphPageActions.updateSingleExpandedNodeIds({
              nodeIds: [],
            }),
          );
        }
        this.currentlySelectedLevel = nestingLevel;
        this.nodePositions = nodePositions;
      }),
      filter(() => !!this.graphRef),
      tap(([{ collapsedGroups }, selectedNode]) => {
        this.expandCollapsedGroups(collapsedGroups);
        const selectedElementBeforeBuilding = this.graphRef
          .getCells()
          .find((cell) => cell.attributes.id === selectedNode?.id);

        this.graphRef.clear();
        this.buildHierarchyGraph(this.graphRef, this.groupedGraphData);
        this.graphRef.resetCells(this.graphRef.getCells());

        (selectedElementBeforeBuilding as BaseShape)?.unhighlight();

        this.store.dispatch(
          CompanyGraphPageActions.selectNode({
            id: null,
            nodeOriginId: null,
            availabilityType: CollapseOrExpandNodeType.NOT_AVAILABLE,
          }),
        );
      }),
    );

  constructor(private store: Store) {}

  loadRadialGraphLayout(
    graph: joint.dia.Graph,
    paper: joint.dia.Paper,
    graphModel: CompanyGraphModel,
    savedPositions: NodePosition[],
    collapsedGroups: string[],
    singleExpandedNodeIds: string[],
    isInitial?: boolean,
  ): void {
    this.nodePositions = savedPositions;
    this.collapsedGroups = collapsedGroups;
    this.levelsCount = 0;
    this.initGraphData(
      graph,
      paper,
      graphModel,
      singleExpandedNodeIds,
      isInitial,
    );
  }

  setCountersOnCollapsedGroups(
    graph: joint.dia.Graph,
    paper: joint.dia.Paper,
  ): void {
    graph
      .getElements()
      .filter((cell) => cell instanceof Container)
      .forEach((container) => {
        const isCollapsedContainer = this.collapsedGroups.includes(
          container.attributes.groupLabelId.toString(),
        );
        if (isCollapsedContainer) {
          const embeddedCells = container.getEmbeddedCells() as BaseShape[];
          const [firstCell] = embeddedCells;
          const counter = embeddedCells.length;
          firstCell.prop('attrs/counter', { text: counter });
          firstCell.preinitialize(firstCell.attributes);
          const elementView = paper.findViewByModel(firstCell);
          elementView.render();
        }
      });
  }

  /**
   * @param originalId The real (back-end) ID of the node
   * @param graphNodeId The node ID of the graph
   */
  checkForShadowGraph(
    originalId: string,
    graphNodeId = '',
    isInitial = false,
  ): CollapseOrExpandNodeType {
    this.alreadyExpandedCell =
      this.getCopiedGraphNode(graphNodeId) ||
      this.getCopiedGraphNode(originalId);
    if (this.alreadyExpandedCell) {
      return CollapseOrExpandNodeType.COLLAPSE;
    }
    // Find selected node and its sub elements
    this.groupedGraphData.every((element: CompanyGraphTreeNodeGroupRoots) => {
      this.shadowGraph = this.getShadowGraph(originalId, element);

      if (this.shadowGraph) {
        return false; //If find the shadow graph we break the loop
      }
      return true;
    });
    const hasSuccessors = this.graphRef
      .getCell(graphNodeId)
      ?.get('hasSuccessors');
    if (this.shadowGraph && (hasSuccessors || isInitial)) {
      return CollapseOrExpandNodeType.EXPAND;
    }
    return CollapseOrExpandNodeType.NOT_AVAILABLE;
  }

  collapseSelectedNode(id?: string): void {
    if (id) {
      this.alreadyExpandedCell = this.getCopiedGraphNode(id);
    }

    if (this.alreadyExpandedCell) {
      this.alreadyExpandedCell.remove();
      this.alreadyExpandedCell = undefined;

      this.dispatchSingleExpandedNodeIds();
      this.dispatchNodePositions();
      this.setFilterOptions();
    }
  }

  expandSelectedNode(selectedNodeId = '', onInit = false): void {
    if (!this.shadowGraph) {
      return;
    }
    const sourceCell = this.graphRef.getCell(selectedNodeId) as dia.Element;
    if (!sourceCell) {
      return;
    }
    const newRootCell = sourceCell.clone() as BaseShape;
    const originID = sourceCell.id;
    newRootCell.prop('id', `copy-${originID}`);
    if (!newRootCell.get('originID')) {
      newRootCell.prop('originID', originID);
    }
    newRootCell.attr(['counterContent', 'class'], 'tw-hidden');

    setTimeoutUnpatched(() => {
      newRootCell.unhighlight();
    }, 0);
    sourceCell.embed(newRootCell);
    const link = new joint.shapes.standard.Link();
    link.source(newRootCell);
    link.target(sourceCell);
    this.graphRef.addCells([newRootCell, link]);
    const savedPosition = this.nodePositions.find(
      (e) => e.id === newRootCell.id,
    );
    if (savedPosition) {
      newRootCell.set('position', savedPosition.position);
    } else {
      const translateValue = newRootCell.position().x < 0 ? -350 : 350;
      newRootCell.translate(translateValue);
    }

    this.addGroupLabel(
      this.graphRef,
      this.shadowGraph.children,
      newRootCell,
      this.shadowGraph.levelsCount,
      false,
      true,
      newRootCell,
    );

    if (!onInit) {
      this.dispatchSingleExpandedNodeIds();
      this.dispatchNodePositions();

      // This logic is going to be used in Graph 2.0. At this point the BE will return all the data for the company.
      // this.fetchNextLevelCompanies(this.shadowGraph.levelsCount);
    }
    this.shadowGraph = undefined;
  }

  toggleGroup(
    groupId: string,
    graph: dia.Graph,
    paper: dia.Paper,
    element: Container,
  ) {
    element.toggle();
    const stackLeft = element.position().x < 0;
    this.toggleGroupCollapse(
      graph,
      paper,
      element.attributes,
      element.isCollapsed(),
      stackLeft,
    );

    element.fitToChildElements();

    if (element.isCollapsed()) {
      this.store.dispatch(
        CompanyGraphPageActions.collapseGroup({ groupId: groupId }),
      );
      element.set('collapsed', false);
    } else {
      this.store.dispatch(
        CompanyGraphPageActions.expandGroup({ groupId: groupId }),
      );
      element.set('collapsed', true);
    }
  }

  /**
   * Important: Dispatching the node positions should be delayed with timeout if it is used after changeMarkupType()
   */
  dispatchNodePositions(): void {
    const cells = this.graphRef.getElements();
    this.nodePositions = cells.map((cell) => {
      const position = cell.position();
      return {
        id: cell.id,
        position: position,
      } as NodePosition;
    });

    this.store.dispatch(
      CompanyGraphPageActions.updateNodePosition({
        nodePositions: this.nodePositions,
      }),
    );
  }

  applyFilteredGraphJSONData(graphData: dia.Cell[], paper: dia.Paper) {
    paper.freeze();
    this.graphRef.clear();
    this.graphRef.addCells(graphData);
    paper.unfreeze();
    this.setFilterOptions();
    this.store.dispatch(
      CompanyGraphPageActions.updateLoadingStateAndNoFilterResults({
        isLoading: false,
        hasNoFilterResults: graphData.length < 3, // At least 2 nodes and 1 edge
      }),
    );
  }

  // This is going to be refactored once the version 1.5 is released and v2.0 is merged
  updateHierarchyData(
    graphModel: CompanyGraphModel,
    singleExpandedNodeIds: string[],
  ): void {
    if (!this.graphRef) {
      return;
    }
    this.initGraphData(
      this.graphRef,
      this.paperRef,
      graphModel,
      singleExpandedNodeIds,
    );
  }

  private dispatchSingleExpandedNodeIds(): void {
    const singleExpandedNodeIds = this.graphRef
      .getElements()
      .map((e) => e.id.toString())
      .filter((id) => id.startsWith('copy'));
    this.store.dispatch(
      CompanyGraphPageActions.updateSingleExpandedNodeIds({
        nodeIds: singleExpandedNodeIds,
      }),
    );
  }

  /**
   * Returns a node that is on the graph and its ID starts with 'copy'
   */
  private getCopiedGraphNode(nodeId: string): dia.Cell | undefined {
    const id = nodeId?.startsWith('copy') ? nodeId : `copy-${nodeId}`;
    return this.graphRef.getCell(id);
  }

  private initGraphData(
    graph: joint.dia.Graph,
    paper: joint.dia.Paper,
    graphModel: CompanyGraphModel,
    singleExpandedNodeIds: string[],
    isInitial?: boolean,
  ): void {
    this.graphRef = graph;
    this.paperRef = paper;
    this.paperRef.freeze();
    graph.fromJSON({ cells: graphModel });
    setTimeoutUnpatched(() => {
      this.groupedGraphData = this.getGraphHierarchyWithGroups(
        graph,
        isInitial,
      );
      graph.clear();
      this.buildHierarchyGraph(graph, this.groupedGraphData);
      graph.resetCells(graph.getCells());
      singleExpandedNodeIds.forEach((id) => {
        const savedID = id.replace('copy-', '');
        const originalElementId = graph.getCell(savedID)?.get('originID');
        if (
          this.checkForShadowGraph(originalElementId || savedID, '', true) ===
          CollapseOrExpandNodeType.EXPAND
        ) {
          this.expandSelectedNode(savedID, true);
        }
      });
      this.setCountersOnCollapsedGroups(graph, paper);
      this.store.dispatch(
        CompanyGraphPageActions.updateGraphLoadingState({ isLoading: false }),
      );
      this.paperRef.unfreeze();
    }, 0); // Allows the graph to have read the cells and set their dimensions
  }

  /**
   * Returns a tree structure of elements that are descendants of the currently selected one, if any.
   * @param selectedId Selected element original ID
   * @param element Current element
   */
  private getShadowGraph(
    selectedId: string,
    element: CompanyGraphTreeNodeGroupRoots,
  ): CompanyGraphTreeNodeGroupRoots {
    if (
      (element.cell.id === selectedId ||
        element.cell.get('originID') === selectedId) &&
      element.levelsCount >= this.currentlySelectedLevel &&
      Object.keys(element.children)?.length > 0
    ) {
      return element;
    }

    let selectedElement: CompanyGraphTreeNodeGroupRoots;
    const keys = Object.keys(element.children);
    keys?.some((key) => {
      const subChildren = (element.children as any)[key];
      if (subChildren.length) {
        subChildren.some((element: CompanyGraphTreeNodeGroupRoots) => {
          selectedElement = this.getShadowGraph(selectedId, element);
          return selectedElement;
        });
      }
      return selectedElement;
    });
    return selectedElement;
  }

  private toggleGroupCollapse(
    graph: dia.Graph,
    paper: dia.Paper,
    modelAttributes: dia.Element.Attributes,
    isCollapsed: boolean,
    stackLeft: boolean,
  ) {
    const embedIds = modelAttributes.embeds;
    if (embedIds.length < 2) {
      return;
    }
    if (isCollapsed) {
      let prevElementPositionX = 0;
      embedIds.forEach((embedId: dia.Element) => {
        const cell = graph.getCell(embedId) as dia.Element;
        prevElementPositionX = this.setNodePosition(
          cell,
          prevElementPositionX,
          stackLeft,
        );
      });
    } else {
      embedIds.slice(1).forEach((embedId: dia.Element) => {
        const cell = graph.getCell(embedId) as dia.Element;
        cell.position(stackLeft ? -10 : 10, 10, { parentRelative: true });
      });
    }

    this.dispatchNodePositions();
  }

  /**
   * Creates a container for the elements of a specific group in a graph (shares in percentages) and adds the nodes and edges of the group to the graph
   */
  private createGroupContainer(
    graph: joint.dia.Graph,
    groupData: CompanyGraphTreeNode[],
    groupLabelShape: joint.shapes.standard.Circle,
    positionData: CompanyGraphGroupLabelPositionData,
    isShadow: boolean,
    parentNodeId: string,
    shadowContainer: dia.Cell | undefined = undefined,
  ): void {
    let groupContainer: Container;
    if (groupData.length > 1) {
      const groupId = `group-container-${Math.random().toString()}`;
      groupLabelShape.prop('groupId', groupId);
      groupContainer = new Container({
        id: groupId,
        position: this.getNodeContainerPosition(positionData),
      });
      groupContainer.prop('groupLabelId', groupLabelShape.id);
      graph.addCell(groupContainer as unknown as dia.Element);
      groupContainer.prop('z', 1);

      if (this.collapsedGroups?.includes(groupLabelShape.id.toString())) {
        groupContainer.set('collapsed', true);
      }
    }
    if (shadowContainer && groupContainer) {
      shadowContainer.embed(groupContainer);
    }
    let prevElementPositionX = 0;
    let hasSavedPosition = false;
    groupData.forEach((element: CompanyGraphTreeNode, index) => {
      const cell = isShadow ? element.cell.clone() : element.cell;
      cell.prop('z', 999 - index);
      if (isShadow && !cell.get('originID')) {
        cell.prop('originID', element.cell.id);
      }
      if (isShadow) {
        const newId = `shadow-${element.cell.id}+${parentNodeId}`;
        cell.prop('id', newId);
      }
      if (groupContainer) {
        groupContainer.embed(cell);
      }
      const savedPosition = this.nodePositions.find(
        (e) => e.id == cell.id,
      )?.position;
      if (savedPosition) {
        cell.prop('position', savedPosition);
        hasSavedPosition = true;
      } else if (!groupContainer) {
        cell.prop('position', this.getNodeContainerPosition(positionData));
      }
      if (shadowContainer && !groupContainer) {
        shadowContainer.embed(cell);
      }
      if (this.isRootCellCompact !== this.isCellCompact(cell)) {
        const type = getShapeType(cell.attributes.type);
        cell.attributes.type = type;
        (cell as any).changeMarkupType(cell.attributes);
      }
      graph.addCell(cell);

      if (groupContainer && !savedPosition) {
        prevElementPositionX = this.setNodePosition(cell, prevElementPositionX);
      }

      if (groupContainer && Object.keys(element.children).length) {
        groupContainer.prop('hasChildren', true);
      }

      this.createCurvedLink(
        graph,
        cell,
        groupLabelShape,
        positionData.linkPosition,
        positionData.group === GroupLabelType.OUT,
      );

      if (!isShadow) {
        // Reads only the first level when element is expanded manually
        this.addGroupLabel(
          graph,
          element.children,
          cell,
          element.levelsCount,
          false,
          isShadow,
        );
      }
    });

    if (!groupContainer) {
      groupLabelShape.attr(['plusSign', 'visibility'], 'hidden');
      groupLabelShape.attr(['minusSign', 'visibility'], 'hidden');
      groupLabelShape.attr(['collapseIcon', 'visibility'], 'hidden');
      return;
    }
    groupContainer.fitToChildElements();

    if (
      (!hasSavedPosition || (!hasSavedPosition && isShadow)) &&
      positionData.nodesStackDirection === CompanyGraphElementDirection.UP
    ) {
      const { x, y } = groupContainer.position();
      const { height } = groupContainer.size();
      groupContainer.position(x, y - height, { deep: true });
    }

    if (!hasSavedPosition && groupData.length >= 5) {
      this.toggleGroup(
        groupLabelShape.id.toString(),
        this.graphRef,
        this.paperRef,
        groupContainer,
      );
    }
  }

  private createCurvedLink(
    graph: dia.Graph,
    source: dia.Element,
    target: dia.Element,
    position: CompanyGraphElementDirection,
    isOutbound: boolean,
  ) {
    const linkType = getLinkType(source.attributes.type);
    const directionCoefficient =
      position === CompanyGraphElementDirection.LEFT ? -1 : 1;
    const link = linkType({
      id: Math.random(),
      text: source.get('sharesAndPosition'),
      directionCoefficient: directionCoefficient,
    });

    link.router('metro', {
      startDirections: [position],
      maxAllowedDirectionChange: 10,
    });
    link.connector('rounded');

    if (isOutbound) {
      link.changeSourceMarker();
    }

    const offset = 40 * directionCoefficient;
    // Function to update the vertices dynamically based on source and target positions
    const updateVertices = () => {
      const { x, y } =
        position === CompanyGraphElementDirection.LEFT
          ? source.getBBox().leftMiddle()
          : source.getBBox().rightMiddle();

      // Determine control point position based on relative positions
      const controlPoint = {
        x: x + offset,
        y: y,
      };

      link.set('vertices', [controlPoint]);
    };

    // Initial calculation of the vertices
    updateVertices();

    // Update vertices whenever the source or target node moves
    source.on('change:position', updateVertices);
    source.on('change:size', updateVertices);
    target.on('change:position', updateVertices);

    const targetLinkDirection =
      position === CompanyGraphElementDirection.LEFT
        ? CompanyGraphElementDirection.RIGHT
        : CompanyGraphElementDirection.LEFT;

    link.source(source);
    link.target(target, {
      anchor: {
        name: targetLinkDirection,
      },
    });
    graph.addCell(link);
  }

  /**
   * Adds a node (circle) to the graph that shows the group label and the number of nodes in it
   */
  private addGroupLabel(
    graph: joint.dia.Graph,
    groupedChildren: CompanyGraphTreeNodeGroups | CompanyGraphTreeNode[],
    cell: dia.Cell,
    currentLevel: number,
    isRoot: boolean,
    isShadow: boolean,
    shadowContainer: dia.Cell | undefined = undefined,
  ): void {
    if (currentLevel === this.currentlySelectedLevel && !isShadow) {
      // currentLevel is the level of nesting while rendering the graph.
      // currentlySelectedLevel is the level to which the user wants to expand the graph
      return;
    }
    const groupKeys = Object.keys(
      groupedChildren,
    ) as (keyof CompanyGraphTreeNodeGroups)[]; // [ firstGroup, secondGroup,... ]
    groupKeys?.forEach((key: keyof CompanyGraphTreeNodeGroups) => {
      const groupData = (groupedChildren as CompanyGraphTreeNodeGroups)[key]; // [ { cell, groupedChildren }, {}, ... ]
      if (groupData.length) {
        const positionData = this.getGroupLabelPosition(
          cell.position(),
          key,
          isRoot,
          isShadow,
          groupData.length,
        );
        const groupLabelShape = new GroupLabel(positionData);
        const groupId = `${key}-${cell.id}`;
        const savedPosition = this.nodePositions.find(
          (element) => element.id === groupId,
        )?.position;
        groupLabelShape.prop(
          'position',
          savedPosition || positionData.position,
        );
        groupLabelShape.prop('id', groupId);

        (groupLabelShape as any).changeMarkupType({
          ...groupLabelShape.attributes,
          type: this.collapsedGroups?.includes(groupId)
            ? GroupLabelTypes.COLLAPSED
            : GroupLabelTypes.EXPANDED,
        });

        const link = new joint.shapes.standard.Link();
        if (positionData.group === GroupLabelType.OUT) {
          groupLabelShape.prop(
            'attrs/circle/class',
            'tw-fill-color-background-tertiary-strong',
          );
          link.prop('attrs/line/stroke-dasharray', '6');
          link.prop('attrs/line/sourceMarker', {
            markup: joint.util.svg`
            <path d="M 0 0 L 8 -4 L 8 4 z"
                  stroke-width="2"
                  fill="#000000"
            />`,
          });
          link.prop('attrs/line/targetMarker', {
            markup: joint.util.svg`
            <circle r="4" fill="#000000" stroke-width="2" />`,
          });
        }
        link.source(groupLabelShape);
        link.target(cell);
        if (shadowContainer) {
          shadowContainer.embed(groupLabelShape);
        }
        graph.addCells([groupLabelShape, link]);

        this.createGroupContainer(
          graph,
          groupData,
          groupLabelShape,
          positionData,
          isShadow,
          cell.id.toString(),
          shadowContainer,
        );
      }
    });

    if (isShadow) {
      this.setFilterOptions();
    }
  }

  /**
   * The main method that traverses the graph data tree and adds the elements to the screen
   * @param graph Graph reference
   * @param hierarchyData Tree structure with the graph data
   */
  private buildHierarchyGraph(
    graph: joint.dia.Graph,
    hierarchyData: CompanyGraphTreeNode[],
  ) {
    hierarchyData.forEach((rootNode: CompanyGraphTreeNode) => {
      const rootCell = rootNode.cell; // Current company
      if (this.isRootCellCompact !== this.isCellCompact(rootCell)) {
        const type = getShapeType(rootCell.attributes.type);
        rootCell.attributes.type = type;
        (rootCell as any).changeMarkupType(rootCell.attributes);
      }
      rootCell.set('position', this.rootElementPosition);
      graph.addCell(rootCell);

      const groupedChildren = rootNode.children; // First level

      this.addGroupLabel(
        graph,
        groupedChildren as CompanyGraphTreeNodeGroups,
        rootCell,
        rootNode.levelsCount,
        true,
        false,
      );
    });

    this.setFilterOptions();
  }

  private setNodePosition(
    cell: dia.Element,
    currentPosition: number,
    stackLeft = false,
  ): number {
    const { height } = cell.size();
    cell.position(stackLeft ? 10 : 0, currentPosition, {
      parentRelative: true,
    });
    return currentPosition + height + 24;
  }

  private getNodeContainerPosition(
    positionData: CompanyGraphGroupLabelPositionData,
  ) {
    const positionX =
      positionData.nodesDirection === CompanyGraphElementDirection.LEFT
        ? positionData.position.x - 390
        : positionData.position.x + 250;

    return { x: positionX, y: positionData.position.y + 10 };
  }

  private getGroupLabelPosition(
    pivot: { x: number; y: number },
    group: GroupLabelType,
    isRoot: boolean,
    isShadow: boolean,
    groupLength = 0,
  ): CompanyGraphGroupLabelPositionData {
    const rootGroupOffsets: any = {
      firstGroup: {
        x: 105,
        y: 270,
        direction: CompanyGraphElementDirection.LEFT,
        stack: CompanyGraphElementDirection.DOWN,
        linkPosition: CompanyGraphElementDirection.RIGHT,
      },
      secondGroup: {
        x: -310,
        y: 40,
        direction: CompanyGraphElementDirection.LEFT,
        stack: CompanyGraphElementDirection.DOWN,
        linkPosition: CompanyGraphElementDirection.RIGHT,
      },
      thirdGroup: {
        x: -105,
        y: -220,
        direction: CompanyGraphElementDirection.LEFT,
        stack: CompanyGraphElementDirection.UP,
        linkPosition: CompanyGraphElementDirection.RIGHT,
      },
      fourthGroup: {
        x: 330,
        y: -220,
        direction: CompanyGraphElementDirection.RIGHT,
        stack: CompanyGraphElementDirection.UP,
        linkPosition: CompanyGraphElementDirection.LEFT,
      },
      fifthGroup: {
        x: 480,
        y: 40,
        direction: CompanyGraphElementDirection.RIGHT,
        stack: CompanyGraphElementDirection.DOWN,
        linkPosition: CompanyGraphElementDirection.LEFT,
      },
      default: {
        x: 300,
        y: 0,
        direction: CompanyGraphElementDirection.RIGHT,
        stack: CompanyGraphElementDirection.DOWN,
        linkPosition: CompanyGraphElementDirection.LEFT,
      },
    };

    if (isRoot) {
      const { x, y, direction, stack, linkPosition } =
        rootGroupOffsets[group] || rootGroupOffsets.default;
      return {
        position: { x: pivot.x + x, y: pivot.y + y },
        nodesDirection: direction,
        nodesStackDirection: stack,
        groupLabel: GROUP_LABEL_MAPPING[group],
        linkPosition: linkPosition,
        type: GroupLabelTypes.EXPANDED,
        groupLength,
        group,
      };
    }

    const shadowGroupOffsets: any = {
      firstGroup: {
        x: 70,
        rx: 70,
        y: 250,
        stack: CompanyGraphElementDirection.DOWN,
      },
      secondGroup: {
        x: -380,
        rx: 550,
        y: 10,
        stack: CompanyGraphElementDirection.DOWN,
      },
      thirdGroup: {
        x: -340,
        rx: 500,
        y: -230,
        stack: CompanyGraphElementDirection.UP,
      },
      fourthGroup: {
        x: 70,
        rx: 70,
        y: -370,
        stack: CompanyGraphElementDirection.UP,
      },
      fifthGroup: {
        x: 300,
        rx: 500,
        y: -50,
        stack: CompanyGraphElementDirection.DOWN,
      },
      default: {
        x: 300,
        rx: 500,
        y: 0,
        stack: CompanyGraphElementDirection.DOWN,
      },
    };

    if (isShadow) {
      const direction =
        pivot.x < 0
          ? CompanyGraphElementDirection.LEFT
          : CompanyGraphElementDirection.RIGHT;
      const linkPosition =
        direction === CompanyGraphElementDirection.LEFT
          ? CompanyGraphElementDirection.RIGHT
          : CompanyGraphElementDirection.LEFT;
      const { x, rx, y, stack } =
        shadowGroupOffsets[group] || shadowGroupOffsets.default;
      return {
        position: { x: pivot.x + (pivot.x < 0 ? x : rx), y: pivot.y + y },
        nodesDirection: direction,
        nodesStackDirection: stack,
        groupLabel: GROUP_LABEL_MAPPING[group],
        linkPosition: linkPosition,
        type: GroupLabelTypes.EXPANDED,
        groupLength,
        group,
      };
    }

    const childGroupOffsets: any = {
      firstGroup: { x: -100, rx: 300, y: 100 },
      secondGroup: { x: -350, rx: 550, y: 60 },
      thirdGroup: { x: -600, rx: 400, y: 20 },
      fourthGroup: { x: -850, rx: 650, y: -50 },
      fifthGroup: { x: -300, rx: 500, y: -60 },
      default: { x: -300, rx: 500, y: 0 },
    };

    const { x, rx, y } = childGroupOffsets[group] || childGroupOffsets.default;
    const direction =
      pivot.x < 0
        ? CompanyGraphElementDirection.LEFT
        : CompanyGraphElementDirection.RIGHT;
    const stackDirection =
      pivot.y < 0
        ? CompanyGraphElementDirection.UP
        : CompanyGraphElementDirection.DOWN;
    const linkPosition =
      direction === CompanyGraphElementDirection.LEFT
        ? CompanyGraphElementDirection.RIGHT
        : CompanyGraphElementDirection.LEFT;

    return {
      position: { x: pivot.x + (pivot.x < 0 ? x : rx), y: pivot.y + y },
      nodesDirection: direction,
      nodesStackDirection: stackDirection,
      groupLabel: GROUP_LABEL_MAPPING[group],
      linkPosition: linkPosition,
      type: GroupLabelTypes.EXPANDED,
      groupLength,
      group,
    };
  }

  private getGraphHierarchyWithGroups(
    graph: joint.dia.Graph,
    isInitial?: boolean,
  ) {
    const currentCompany = graph
      .getElements()
      .find(
        (element: dia.Element) =>
          element.attributes.type === NetworkElement.CURRENT_COMPANY_COMPACT ||
          element.attributes.type === NetworkElement.CURRENT_COMPANY,
      );
    const roots: CompanyGraphTreeNodeGroupRoots[] = [];
    const visitedNodes = new Set<string | number>();
    const nodesAtLevel = new Map<number, number>();

    if (currentCompany) {
      const groupedChildren = this.groupChildrenByPercentage(
        graph,
        currentCompany,
        visitedNodes,
        currentCompany.id.toString(),
        nodesAtLevel,
      );
      groupedChildren.fifthGroup = this.outConnections(graph, currentCompany);
      roots.push({
        cell: currentCompany,
        children: groupedChildren,
        levelsCount: 0,
      });
      if (!isInitial) {
        this.store.dispatch(
          CompanyGraphPageActions.updateGraphDataAvailability({
            hasNoGraphData: false,
          }),
        );
      }
    }

    // Checks if there is a nesting level with more than 100 nodes and does not show the toggle to expand this level.
    const matchedLevel = [...nodesAtLevel].find(
      ([, value]) => value > 100,
    )?.[0];
    if (matchedLevel === 1) {
      this.levelsCount = 1;
    } else if (matchedLevel > 1) {
      this.levelsCount = matchedLevel - 1;
    }

    this.store.dispatch(
      CompanyGraphPageActions.setGraphLevel({
        levelsCount: this.levelsCount,
      }),
    );
    return roots;
  }

  /**
   * Creates a tree structure that groups the nodes and represents the design requirements.
   * There are cases when one node should be shown more than once on the graph.
   * To make this possible, we have to change the original node ID with a new unique one,
   * (newNodeID = parentID + nodeID)
   * and it has to be the same every time this node is rendered.
   */
  private groupChildrenByPercentage(
    graph: joint.dia.Graph,
    cell: dia.Element,
    visitedNodes: Set<string | number>,
    parentId: string,
    nodesAtLevel: Map<number, number>,
    currentLevel = 0,
  ) {
    // Get the direct children of the current cell. We want to filter direct connections that are the main company or the node itself.
    const firstLevelSuccessors = graph
      .getNeighbors(cell, { outbound: true })
      .filter(
        (successor) =>
          !(
            successor.id === cell.id ||
            [
              NetworkElement.CURRENT_COMPANY,
              NetworkElement.CURRENT_COMPANY_COMPACT,
            ].includes(successor.get('type'))
          ),
      );
    const connectedLinks = graph.getConnectedLinks(cell, { outbound: true });

    if (visitedNodes.has(cell.id)) {
      // Checks if the node is already visited to avoid cycles in the graph
      cell.prop('originID', cell.get('originID') || cell.id);
      cell.prop('id', `${parentId}=${cell.id}`);
      return {};
    }
    visitedNodes.add(cell.id);
    const groups: CompanyGraphTreeNodeGroups = {
      firstGroup: [], // firstGroup - <5%
      secondGroup: [], // secondGroup - 5-19%
      thirdGroup: [], // thirdGroup - >19%
      fourthGroup: [], // fourthGroup - Other,
      fifthGroup: [], // fifthGroup - Out
    };

    // Shows successor icon if the node has successors
    if (this.levelsCount > 0 && firstLevelSuccessors.length) {
      cell.attr(['successorIconBg', 'visibility'], 'visible');
      cell.attr(['successorIcon', 'visibility'], 'visible');
      cell.prop('hasSuccessors', true);
    }

    if (firstLevelSuccessors.length) {
      currentLevel++;
      this.levelsCount = Math.max(currentLevel, this.levelsCount);
    }

    const nodesLength =
      currentLevel > 1
        ? firstLevelSuccessors.length
        : firstLevelSuccessors.filter(
            (element) => element.attributes.type === NetworkElement.COMPANY,
          ).length;
    const companiesCount = nodesLength + (nodesAtLevel.get(currentLevel) || 0);
    nodesAtLevel.set(currentLevel, companiesCount);

    firstLevelSuccessors.forEach((directSuccessor) => {
      const successor = directSuccessor.clone();
      successor.prop('id', directSuccessor.id);
      const connectedLink = connectedLinks.find(
        (link: dia.Link) =>
          link.attributes.target.id === successor.attributes.id,
      );
      this.addSharesAndPositionToNode(connectedLink, successor);
      const percentage = connectedLink?.get('sharesPercent');
      const groupedChildren = this.groupChildrenByPercentage(
        graph,
        successor,
        visitedNodes,
        cell.id.toString(),
        nodesAtLevel,
        currentLevel,
      );

      if (!percentage) {
        groups.fourthGroup.push({
          cell: successor,
          children: groupedChildren,
          levelsCount: currentLevel,
        });
      } else if (percentage >= 0 && percentage < 5) {
        groups.firstGroup.push({
          cell: successor,
          children: groupedChildren,
          levelsCount: currentLevel,
        });
      } else if (percentage >= 5 && percentage <= 19) {
        groups.secondGroup.push({
          cell: successor,
          children: groupedChildren,
          levelsCount: currentLevel,
        });
      } else if (percentage > 19) {
        groups.thirdGroup.push({
          cell: successor,
          children: groupedChildren,
          levelsCount: currentLevel,
        });
      }
    });

    this.deleteEmptyGroups(groups);
    return groups;
  }

  private deleteEmptyGroups(groups: CompanyGraphTreeNodeGroups) {
    Object.keys(groups).forEach((key) => {
      if (!(groups as any)[key].length) {
        delete (groups as any)[key];
      }
    });
  }

  // Groups companies that current company has shares in.
  private outConnections(
    graph: dia.Graph,
    cell: dia.Element,
  ): CompanyGraphTreeNode[] {
    const directSuccessors = graph.getNeighbors(cell, { inbound: true });
    const connectedLinks = graph.getConnectedLinks(cell, { inbound: true });
    const outConnectionsGroup: CompanyGraphTreeNode[] = [];

    directSuccessors.forEach((directSuccessor) => {
      if (directSuccessor.id === cell.id) {
        return;
      }
      const successor = directSuccessor.clone();
      successor.prop('id', directSuccessor.id);
      const connectedLink = connectedLinks.find(
        (link: dia.Link) =>
          link.attributes.source.id === successor.attributes.id,
      );
      this.addSharesAndPositionToNode(connectedLink, successor);
      successor.prop('id', `out-${directSuccessor.id}`);
      successor.prop('originID', directSuccessor.id);

      outConnectionsGroup.push({
        cell: successor,
        children: [],
        levelsCount: 1,
      });
    });

    return outConnectionsGroup;
  }

  private addSharesAndPositionToNode(link: dia.Link, node: dia.Element): void {
    const sharesPercent = link.attributes.sharesPercent
      ? `${link.attributes.sharesPercent}%`
      : '';
    const position = link.attributes.description || '';
    const separator = sharesPercent && position ? ' - ' : '';
    node.set(
      'sharesAndPosition',
      `${sharesPercent}${separator}${position}` || ' - ',
    );
    node.set('sharesPercent', link.attributes.sharesPercent || 0);
    node.set('description', link.attributes.description);
  }

  private isCellCompact(cell: dia.Element): boolean {
    return cell.get('type').includes('COMPACT');
  }

  private setFilterOptions() {
    const cells = this.graphRef.getCells();
    const uniqueRoles = new Set<string>();
    const uniqueEntities = new Set<string>();
    const defaultEntities = [
      NetworkElement.PERSON,
      NetworkElement.PERSON_COMPACT,
      NetworkElement.UBO,
      NetworkElement.UBO_COMPACT,
      NetworkElement.COMPANY,
      NetworkElement.COMPANY_COMPACT,
    ];

    const rolesTypes = [
      NetworkElement.PERSON,
      NetworkElement.PERSON_COMPACT,
      NetworkElement.UBO,
      NetworkElement.UBO_COMPACT,
      NetworkElement.INACTIVE_PERSON,
      NetworkElement.INACTIVE_PERSON_COMPACT,
    ];

    cells.forEach((cell: dia.Cell) => {
      const type = cell.get('type');
      if (defaultEntities.includes(type)) {
        uniqueEntities.add(type);
      }

      if (rolesTypes.includes(type)) {
        const text = cell.get('sharesAndPosition');
        const role = extractStringAsRole(text);
        if (role) {
          uniqueRoles.add(role);
        }
      }
    });

    this.store.dispatch(
      CompanyGraphPageActions.filterSelectOptions({
        availableRoles: [...uniqueRoles],
        availableEntityTypes: [...uniqueEntities],
      }),
    );
  }

  private fetchNextLevelCompanies(currentlyExpandedLevel: number): void {
    const lastVisibleLevel = currentlyExpandedLevel + 1;
    if (lastVisibleLevel >= this.levelsCount) {
      this.store.dispatch(
        CompanyGraphPageActions.fetchNextLevel({
          levelNumber: lastVisibleLevel,
        }),
      );
    }
  }

  private expandCollapsedGroups(collapsedGroupIds: string[]): void {
    collapsedGroupIds.forEach((groupId) => {
      const groupContainerId = this.graphRef.getCell(groupId)?.get('groupId');
      const container = this.graphRef.getCell(groupContainerId);
      if (!container?.get('hasChildren')) {
        return;
      }
      this.toggleGroup(
        groupId,
        this.graphRef,
        this.paperRef,
        container as Container,
      );
    });
  }
}
