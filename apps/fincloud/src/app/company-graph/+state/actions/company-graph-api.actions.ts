import {
  CompanyNetworkDto,
  SharesPercentageData,
} from '@fincloud/swagger-generator/company';
import { createAction, props } from '@ngrx/store';
import { CompanyGraphChangeDto } from '../../models/company-graph-change-dto';
import { GraphConfiguration } from '../../models/company-graph-configuration';
import { CompanyNetwork } from '../../models/company-network-dto';

export const getCompanyNetworkFailure = createAction(
  '[Network API] Get company graph data Failure',
  props<{ error: unknown }>(),
);

export const getCompanyNetworkDtoSuccess = createAction(
  '[Network API] Get company network dto Success',
  props<{ companyNetworkDto: CompanyNetworkDto }>(),
);

export const getCompanyCalculatedUboDtoSuccess = createAction(
  '[Network API] Get company calculated ubo dto Success',
  props<{ companyCalculatedUboDto: SharesPercentageData[] }>(),
);

export const getCompanyCalculatedUboDtoFailure = createAction(
  '[Network API] Get company calculated ubo dto Failure',
  props<{ error: unknown }>(),
);

export const getCompanyUboDtoSuccess = createAction(
  '[Network API] Get company ubo dto Success',
  props<{ companyUboDto: CompanyNetworkDto }>(),
);

export const getCompanyUboDtoFailure = createAction(
  '[Network API] Get company ubo dto Failure',
);

export const saveUserGraphSettingsSuccess = createAction(
  '[Network API] Save user graph settings Success',
);

export const saveUserGraphSettingsFailure = createAction(
  '[Network API] Save user graph settings Failure',
);

export const getUserGraphSettingsSuccess = createAction(
  '[Network API] Get user graph settings Success',
  props<{ graphConfiguration: GraphConfiguration }>(),
);

export const getUserGraphSettingsFailure = createAction(
  '[Network API] Get user graph settings Failure',
  props<{ error: unknown }>(),
);

export const connectGraphDownloadedWebSocketSuccess = createAction(
  '[Network API] Connect graph downloaded web socket Success',
);

export const connectGraphDownloadedWebSocketFailure = createAction(
  '[Network API] Connect graph downloaded web socket Failure',
  props<{ error: unknown }>(),
);

export const getCompanyChangesSuccess = createAction(
  '[Network API] Get company changes Success',
  props<{ companyChanges: CompanyGraphChangeDto }>(),
);

export const getCompanyChangesFailure = createAction(
  '[Network API] Get company changes Failure',
  props<{ error: unknown }>(),
);

export const acceptReviewChangesSuccess = createAction(
  '[Network API] Accept review changes Success',
  props<{ id: string }>(),
);

export const acceptReviewChangesFailure = createAction(
  '[Network API] Accept review changes Failure',
  props<{ error: unknown }>(),
);

export const acceptAllReviewChangesSuccess = createAction(
  '[Network API] Accept all review changes Success',
);

export const acceptAllReviewChangesFailure = createAction(
  '[Network API] Accept all review changes Failure',
  props<{ error: unknown }>(),
);

export const fetchNextLevelSuccess = createAction(
  '[Network API] Fetch next level Success',
  props<{ data: CompanyNetwork }>(),
);

export const fetchNextLevelFailure = createAction(
  '[Network API] Fetch next level Failure',
  props<{ error: unknown }>(),
);
