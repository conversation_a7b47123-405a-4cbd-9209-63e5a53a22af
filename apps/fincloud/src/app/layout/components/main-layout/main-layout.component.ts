import { transition, trigger, useAnimation } from '@angular/animations';
import { TemplatePortal } from '@angular/cdk/portal';
import {
  AfterViewInit,
  Component,
  DestroyRef,
  ElementRef,
  HostListener,
  OnDestroy,
  OnInit,
  OutputRefSubscription,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import { fadeInX, fadeOutX } from '@fincloud/components/animations';
import {
  SESSION_STORAGE_UNIQUE_INSTANCE_ID,
  TokenManagementService,
} from '@fincloud/core/auth';
import {
  LayoutCommunicationService,
  SidebarLayoutSection,
} from '@fincloud/core/layout';
import { ScrollCommunicationService } from '@fincloud/core/scroll';
import { selectAllowedPages } from '@fincloud/state/access';
import { StateLibAuthTokensPageActions } from '@fincloud/state/auth-tokens';
import { selectRouteCustomerKey } from '@fincloud/state/router';
import {
  StateLibSideNavigationsPageActions,
  sideNavigationsFeature,
} from '@fincloud/state/side-navigations';
import {
  StateLibTodosManagementPageActions,
  todosManagementBadgeFeature,
} from '@fincloud/state/todos-management';
import { selectUser, selectUserCustomerKey } from '@fincloud/state/user';
import { Permission, UniqueInstanceStatusType } from '@fincloud/types/enums';
import { AppState } from '@fincloud/types/models';
import { FinButtonAppearance } from '@fincloud/ui/button';
import {
  FinSidePanelComponent,
  FinSidePanelMod,
  FinSidePanelPosition,
} from '@fincloud/ui/side-panel';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { NgScrollbar } from 'ngx-scrollbar';
import { SessionStorageService } from 'ngx-webstorage';
import { Observable, filter, fromEvent, map, scan, startWith, tap } from 'rxjs';

@Component({
  selector: 'app-main-layout',
  templateUrl: './main-layout.component.html',
  styleUrls: ['./main-layout.component.scss'],
  animations: [
    trigger('fade', [
      transition(':enter', useAnimation(fadeInX(200, 0))),
      transition(':leave', useAnimation(fadeOutX(200, 0))),
    ]),
  ],
})
export class MainLayoutComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('mainScrollContainer') mainScrollContainer: NgScrollbar;
  @ViewChild('main', { static: true, read: ElementRef }) main: ElementRef;
  @ViewChild(FinSidePanelComponent) sidePanel: FinSidePanelComponent;

  isTodosFeatureAvailable$ = this.store.select(
    todosManagementBadgeFeature.selectIsTodosFeatureAvailable,
  );

  sidebarCollapsed = false;
  rememberSidebarCollapse = false;

  finSidePanelMod = FinSidePanelMod;
  finSidePanelPosition = FinSidePanelPosition;
  sidebarLayoutSection = SidebarLayoutSection;
  templatePortals$: Observable<{
    [key: string]: TemplatePortal<unknown> | null;
  }> = this.layoutCommunicationService.sidebarLayoutConfiguration$.pipe(
    scan((portals, config) => {
      portals = {
        ...portals,
        [config.side]: config.template
          ? new TemplatePortal(config.template, this.viewContainerRef)
          : null,
      };
      return portals;
    }, {}),
    startWith({}),
  );

  isUserMenuOpened = false;
  uniqueInstanceId: string;
  customerKey: string;

  finSize = FinSize;
  finButtonAppearance = FinButtonAppearance;

  user$ = this.store.select(selectUser);
  userName$ = this.user$.pipe(
    filter(Boolean),
    map((user) => [user.firstName, user.lastName].join(' ')),
  );

  customerKey$ = this.store.select(selectUserCustomerKey);

  selectNavigationAndChatAreOpen$ = this.store.select(
    sideNavigationsFeature.selectNavigationAndChatAreOpen,
  );

  createBusinessCaseAllowed = this.store
    .select(selectAllowedPages)
    .pipe(map((allowedPages) => allowedPages.CreateBusinessCase.read));

  uniqueInstanceId$ = this.store.select(selectRouteCustomerKey).pipe(
    map((customerKey) => {
      const token = this.tokenManagementService.getToken(customerKey);
      this.uniqueInstanceId = this.sessionStorageService.retrieve(
        SESSION_STORAGE_UNIQUE_INSTANCE_ID,
      );
      this.customerKey = customerKey;

      return [this.uniqueInstanceId, customerKey, token.openedAngularInstances];
    }),
    tap(([uniqueInstance, customerKey, openedAngularInstances]) => {
      if (
        uniqueInstance &&
        openedAngularInstances &&
        (!openedAngularInstances[uniqueInstance] ||
          openedAngularInstances[uniqueInstance] ===
            UniqueInstanceStatusType.INACTIVE)
      ) {
        this.store.dispatch(
          StateLibAuthTokensPageActions.addUniqueInstanceStatus({
            customerKey,
            status: UniqueInstanceStatusType.ACTIVE,
          }),
        );
      }
    }),
  );

  private scrollbarAfterUpdateSubscription: OutputRefSubscription;

  readonly sidebarToggleButtonAppearance = FinButtonAppearance.INFORMATIVE;

  get permissionHtml(): typeof Permission {
    return Permission;
  }

  constructor(
    private router: Router,
    private store: Store<AppState>,
    private tokenManagementService: TokenManagementService,
    private sessionStorageService: SessionStorageService,
    private destroyRef: DestroyRef,
    private layoutCommunicationService: LayoutCommunicationService,
    private viewContainerRef: ViewContainerRef,
    private scrollCommunicationService: ScrollCommunicationService,
  ) {}

  @HostListener('window:beforeunload', ['$event'])
  beforeUnloadHandler() {
    this.tokenManagementService.updateToken(this.customerKey, {
      openedAngularInstances: {
        [this.uniqueInstanceId]: UniqueInstanceStatusType.INACTIVE,
      },
    });

    // Delay closing the window
    const timeForDelay = 250;
    const delayTimer = new Date().getTime() + timeForDelay;
    // eslint-disable-next-line no-empty
    while (new Date().getTime() < delayTimer) {}
  }

  ngOnInit() {
    this.store.dispatch(
      StateLibTodosManagementPageActions.todosSummarySocketConnect(),
    );
    if (this.rememberSidebarCollapse) {
      this.sidebarCollapsed = !!JSON.parse(
        localStorage.getItem('sidebarCollapsed'),
      );
    }

    this.layoutCommunicationService.toggleRightSideOverlayPanel$
      .pipe(
        tap(() => this.sidePanel.toggleSidePanel()),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  ngAfterViewInit() {
    this.layoutCommunicationService.scrollToTopEvent$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: () => this.scrollToTop(),
      });

    this.scrollbarAfterUpdateSubscription =
      this.mainScrollContainer.afterUpdate.subscribe(() =>
        this.layoutCommunicationService.layoutChanged(),
      );

    this.scrollCommunicationService.mainScrollbar = this.mainScrollContainer;

    if (this.mainScrollContainer?.nativeElement) {
      fromEvent(this.mainScrollContainer.nativeElement, 'scroll')
        .pipe(
          tap((e: Event) => {
            this.layoutCommunicationService.setLayoutScrollYPosition(
              (e.target as EventTarget & { scrollTop: number }).scrollTop,
            );
          }),
          takeUntilDestroyed(this.destroyRef),
        )
        .subscribe();
    }
  }

  ngOnDestroy(): void {
    if (this.scrollbarAfterUpdateSubscription) {
      this.scrollbarAfterUpdateSubscription.unsubscribe();
    }
  }

  toggleNavigations(navigation: {
    isChatVisible: boolean;
    pageNavigationIsOpen: boolean;
    platformNavigationIsOpen: boolean;
  }): void {
    this.layoutCommunicationService.layoutChanged();

    if (navigation.isChatVisible && navigation.pageNavigationIsOpen) {
      this.store.dispatch(
        StateLibSideNavigationsPageActions.toggleOpenForPageNavigation(),
      );
    }

    this.store.dispatch(
      StateLibSideNavigationsPageActions.toggleOpenForPlatformNavigation(),
    );
  }

  onCreateBusinessCase(customerKey: string, path: string) {
    void this.router.navigate([customerKey, path]);
  }

  toggleUserMenu() {
    this.isUserMenuOpened = !this.isUserMenuOpened;
  }

  private scrollToTop() {
    void this.mainScrollContainer?.scrollToElement(this.main, { top: 0 });
  }

  onLayoutVisibilityChange(isOpen: boolean) {
    if (!isOpen) {
      this.layoutCommunicationService.layoutClosed();
    }
  }
}
