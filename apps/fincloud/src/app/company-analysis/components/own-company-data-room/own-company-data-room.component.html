@if (company) {
  <div
    class="tw-flex tw-flex-col my-cadr-container"
    [class.edit-mode]="editMode"
  >
    <div class="header">
      <div
        class="label"
        i18n="
          @@dashboard.collaboration.myPartners.roles.invitationManagement.text.permission.administer"
      >
        Verwalten
      </div>
      <div class="actions">
        <ng-template [ngxPermissionsOnly]="permissionHtml.PERM_0043">
          <ui-icon name="share" (click)="openShareWithModal()"></ui-icon>
        </ng-template>
        <ui-button
          corners="pointy"
          icon="settings"
          (clicked)="openLinkedCases()"
          label="Verlinkte Fälle"
          i18n-label="@@ownCompany.dataRoom.button.openLInkedCases"
          type="fill"
          color="gray"
        ></ui-button>
      </div>
    </div>
    <ui-horizontal-divider color="gray"></ui-horizontal-divider>
    <div class="data-room">
      @if (
        !company.companyTemplate?.template?.groupsOrdered?.length &&
        !editMode &&
        !searchControl.value
      ) {
        <div class="empty-data-room">
          <ui-icon name="document" color="subtle"></ui-icon>
          <div class="text" i18n="@@ownCompany.dataRoom.empty">
            Es wurden noch keine Felder zum unternehmensbezogenen Data Room
            hinzugefügt. Um welche anzulegen, aktivieren Sie bitte den
            Bearbeitungsmodus und fügen Sie Felder hinzu oder erstellen sie eine
            Vorlage für den unternehmensbezogenen Data Room unter “Vorlagen”.
          </div>
        </div>
      }
      @if (!groupsTemplateFieldsInitial?.length && editMode) {
        <div class="empty-data-room-edit-mode" (click)="openCreateGroup()">
          <a class="add-group">
            <ui-icon name="add" color="primary"></ui-icon>
          </a>
          <div class="label" i18n="@@ownCompany.dataRoom.addGroup">
            Gruppe hinzufügen
          </div>
        </div>
      }
    </div>
    <ng-container>
      @if (company?.companyTemplate?.template?.groupsOrdered?.length) {
        <fin-search
          dynamicErrorSpace="dynamic"
          [formControl]="searchControl"
          [showLoader]="false"
          [options]="[]"
          [autocomplete]="false"
          [size]="finSize.M"
          [numberOfResults]="searchResultsCount"
          class="tw-grow tw-mx-[1.2rem]"
        ></fin-search>
      }
      @if (groupsTemplateFields?.length) {
        <div class="groups-fields-container">
          @for (
            group of groupsTemplateFields;
            track trackByKey(groupIndex, group);
            let groupIndex = $index
          ) {
            <div
              class="group-container"
              [id]="group.key"
              cdkDropList
              (cdkDropListDropped)="openCreateField($event, group, groupIndex)"
              (cdkDropListEntered)="dropListHover(true, groupIndex)"
              (cdkDropListExited)="dropListHover(false, groupIndex)"
            >
              <ng-template
                [ngTemplateOutlet]="groupHoverOverlay"
                [ngTemplateOutletContext]="{ group, groupIndex }"
              >
              </ng-template>
              <div class="section-header-wrapper">
                <app-group-visibility-status-info
                  [group]="group"
                ></app-group-visibility-status-info>
                <div
                  class="section-title-overview"
                  (appInsideView)="
                    elementIntoView(group.key, groupsTemplateFields)
                  "
                  (elementNotInView)="
                    elementOutsideOfView(
                      group.key,
                      groupIndex,
                      groupsTemplateFields
                    )
                  "
                >
                  <span>{{ group.groupIndex }}. {{ group.value }}</span>
                  <ui-horizontal-divider
                    color="gray"
                    class="section-title-divider"
                  ></ui-horizontal-divider>
                </div>
              </div>
              @if (editMode) {
                <ng-template
                  [ngTemplateOutlet]="emptyGroupOverlay"
                  [ngTemplateOutletContext]="{ group, groupIndex }"
                >
                </ng-template>
              }
              <div class="fields-group">
                @for (
                  templateField of group.templateFields;
                  track trackByKeyTemplateField($index, templateField)
                ) {
                  <ng-template
                    [ngTemplateOutlet]="templateFieldTemplate"
                    [ngTemplateOutletContext]="{ field: templateField }"
                  >
                  </ng-template>
                }
              </div>
              @if (group.documents.length) {
                <div class="section-title-overview">
                  <span
                    >{{ group.documents.length }}
                    {{
                      internationalizationForDocument(group.documents.length)
                    }}</span
                  >
                </div>
                <ui-horizontal-divider
                  class="documents"
                ></ui-horizontal-divider>
                <div
                  class="tw-grid tw-grid-cols-[repeat(auto-fill,_minmax(27rem,_1fr))] tw-gap-[2.4rem]"
                >
                  @for (
                    documentField of group.documents;
                    track trackByKeyTemplateField($index, documentField)
                  ) {
                    <ng-template
                      [ngTemplateOutlet]="templateFieldTemplate"
                      [ngTemplateOutletContext]="{ field: documentField }"
                    >
                    </ng-template>
                  }
                  @if (editMode) {
                    <ui-new-document-drop
                      (fileDropped)="
                        openCreateField(
                          $event.fileDropped,
                          group,
                          groupIndex,
                          $event.dragEvent
                        )
                      "
                    ></ui-new-document-drop>
                  }
                </div>
              }
            </div>
          }
        </div>
      } @else {
        @if (searchControl.value) {
          <div
            class="tw-text-color-text-tertiary tw-text-body-1-strong tw-text-center tw-pt-10"
            i18n="@@businessCase.dataRoom.noResult"
          >
            Keine Ergebnisse gefunden. Versuchen Sie andere Schlüsselwörter oder
            überprüfen Sie die Rechtschreibung
          </div>
        }
      }

      <ng-template #templateFieldTemplate let-field="field">
        <ui-template-field
          [field]="field.field"
          [information]="field.information"
          [isEditMode]="editMode"
          [templateFieldData]="templateFieldData$ | async"
          (fieldDeleted)="deleteField(field)"
          (openModalRequested)="
            openModal($event.modalTab, field.information, field.field)
          "
          (valueChanged)="onFieldValueChanged($event)"
          (fileUpload)="onFileUpload($event)"
        >
        </ui-template-field>
      </ng-template>
      <ng-template
        #groupHoverOverlay
        let-group="group"
        let-groupIndex="groupIndex"
      >
        <div class="overlay" [ngClass]="{ hovered: groupHovered[groupIndex] }">
          <div class="background tw-z-[1]"></div>
          <div class="dragzone-info tw-z-[2]">
            <div class="group-title">
              {{ group.groupIndex }}. {{ group.value }}
            </div>
            <div class="sub-text">
              <span i18n="@@ownCompany.dataRoom.prefix"> Information zu</span>
              {{ group.value }}
              <span i18n="@@ownCompany.dataRoom.suffix">hinzufügen</span>
            </div>
            <ui-button
              size="large"
              corners="pointy"
              type="outline"
              color="white"
              borderType="dashed"
              label="Drag-and-Drop"
            ></ui-button>
          </div>
        </div>
      </ng-template>
      <ng-template
        #emptyGroupOverlay
        let-group="group"
        let-groupIndex="groupIndex"
      >
        @if (!group.fields?.length && !group.documents?.length) {
          <div class="empty-group-placeholder">
            @if (!groupHovered[groupIndex]) {
              <div class="inner">
                <ui-icon name="svgEmptyGroup"></ui-icon>
                <div
                  class="text-main"
                  i18n="@@ownCompany.dataRoom.emptyGroup.text"
                >
                  Fügen Sie Felder durch Drag & Drop hinzu
                </div>
                <div
                  class="text-sub"
                  i18n="@@ownCompany.dataRoom.emptyGroup.textSub"
                >
                  Ziehen Sie dafür die Felder aus der rechten Spalte in diesen
                  Bereich.
                </div>
              </div>
            }
          </div>
        }
      </ng-template>
    </ng-container>
  </div>
}
