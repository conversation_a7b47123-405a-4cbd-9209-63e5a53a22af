@use 'styles/src/lib/common';

:host {
  .edit-field-wrapper {
    overflow-y: auto;
    height: 100%;
    max-height: 80vh;
  }

  .edit-field-wrapper-document {
    overflow-y: hidden;
  }

  ::ng-deep {
    .cl-switch {
      margin: 1.5rem 1rem 0 0;
    }
    .btn.stealth {
      padding-left: 0;
    }
  }
}


.wrapper {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  width: 40%;
}

.edit-field-wrapper {
  padding-bottom: 2rem;

  .existing-chat-warning {
    @include common.heading4();
    padding: 0.8rem;
    border-radius: common.$border-radius;
    background-color: rgb(
      var(--colors-color-status-warning) / (1 - 0.8)
    ); //#FFE5C8;
    color: theme('colors.color-status-failure');
    margin-top: 1.2rem;
    text-align: center;
  }
}

.edit-field-switch {
  padding-top: 1rem;
}

label {
  @include common.heading5();
  padding-bottom: 0.6rem;
}

.modal-footer {
  padding: 0 3.5rem;
}
