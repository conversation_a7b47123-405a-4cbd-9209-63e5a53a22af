<div
  class="edit-field-wrapper"
  [ngClass]="{
    'edit-field-wrapper-document': fieldType === fieldTypes.DOCUMENT,
  }"
>
  <div class="tw-flex tw-justify-between tw-pb-4">
    <fin-scrollbar
      class="tw-w-full"
      [ngClass]="{
        'tw-max-h-[32.2rem]': fieldType !== fieldTypes.DOCUMENT,
        'tw-max-h-[38.8rem]': fieldType === fieldTypes.DOCUMENT,
        'tw-max-h-[51.8rem]': fieldType === fieldTypes.SELECT,
      }"
    >
      <app-document-field
        [class.tw-flex-grow]="fieldType === fieldTypes.DOCUMENT"
        [fieldType]="fieldType"
        [information]="companyInformation"
        [documentCategoryOptions]="documentCategoryOptions$ | async"
        [documentId]="documentId"
        [formGroup]="editFieldForm"
        [fileDeleting]="fileDeleting"
        [fileUploading]="fileUploading"
      ></app-document-field>

      @if (fieldType === 'SELECT') {
        <ui-dropdown-options-configuration
          (addedOption)="scrollToBottom()"
          [(ngModel)]="fieldMetadata"
        ></ui-dropdown-options-configuration>
      }
    </fin-scrollbar>

    @if (fieldType === fieldTypes.DOCUMENT) {
      <div class="tw-pr-[3.2rem]" [formGroup]="editFieldForm">
        <app-document-field-upload
          [documentId]="companyInformation?.value?.toString()"
          [dataRoomOwnerId]="company.id"
          [fileName]="
            companyInformation?.value &&
            companyInformation?.field?.label?.toLocaleLowerCase()
          "
          ownerType="COMPANY"
          [fileUploadEvent]="fileUploadEvent"
          formControlName="documentId"
          (fileUploading)="onFileUploading($event)"
          (fileDeleting)="onFileDeleting($event)"
        ></app-document-field-upload>
      </div>
    }
  </div>

  <hr
    finHorizontalSeparator
    [type]="finSeparator.SUBTLE"
    class="tw-mx-14 tw-mb-6"
  />
  <div class="modal-footer">
    <ui-buttons-modal-footer
      label="Feld"
      i18n-label="@@facilityField.edit.field"
      [deleteDisabled]="companyInformation === null && !fieldDto"
      [saveDisabled]="
        editFieldForm.invalid ||
        (fieldDto &&
          !isLabelEdited &&
          !isMetadataEdited &&
          !isDocumentEdited &&
          !isCategoryEdited &&
          !isDescriptionEdited) ||
        fileUploading ||
        isSavingInProcess
      "
      [showDelete]="false"
      (closed)="close()"
      (saved)="save()"
    ></ui-buttons-modal-footer>
  </div>
</div>
