/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { HttpErrorResponse } from '@angular/common/http';
import {
  ChangeDetectorRef,
  Component,
  DestroyRef,
  Input,
  OnInit,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { GroupTemplateFields } from '@fincloud/core/business-case';
import {
  AbstractValueAccessor,
  makeControlValidatorProvider,
  makeControlValueAccessorProvider,
} from '@fincloud/core/form';
import { FieldTypeEnum } from '@fincloud/core/formly';
import { IdentityService } from '@fincloud/core/services';
import { Toast } from '@fincloud/core/toast';
import { CompanyDataRoomHelperService } from '@fincloud/neoshare/data-room';
import { selectDocumentFieldCategoryOptions } from '@fincloud/state/company-analysis';
import {
  CadrGroup,
  Company,
  CompanyControllerService,
  FieldDto,
  Information,
  InformationControllerService,
} from '@fincloud/swagger-generator/company';
import { FinSeparator } from '@fincloud/ui/separators';
import { FinToastService } from '@fincloud/ui/toast';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { cloneDeep, isEmpty, isEqual, isObject } from 'lodash-es';
import { NgScrollbar } from 'ngx-scrollbar';
import { Observable, concat, of } from 'rxjs';
import { catchError, finalize, switchMap, takeLast, tap } from 'rxjs/operators';

@Component({
  selector: 'app-edit-company-field',
  templateUrl: './edit-company-field.component.html',
  styleUrls: ['./edit-company-field.component.scss'],
  providers: [
    makeControlValueAccessorProvider(EditCompanyFieldComponent),
    makeControlValidatorProvider(EditCompanyFieldComponent),
  ],
})
export class EditCompanyFieldComponent
  extends AbstractValueAccessor<string>
  implements OnInit
{
  @Input() companyInformation?: Information;
  @Input() fieldDto: FieldDto;
  @Input() newField?: FieldDto;
  @Input() orderedGroups: CadrGroup[];
  @Input() fieldGroup: GroupTemplateFields;
  @Input() company: Company;
  @Input() fileUploadEvent: DragEvent;

  readonly fieldTypes = FieldTypeEnum;
  readonly finSeparator = FinSeparator;

  @ViewChild(NgScrollbar)
  scrollbarRef: NgScrollbar;

  fieldLabel: string;
  documentId: string;
  description: string;
  categoryId: string;

  editFieldForm: UntypedFormGroup;
  fieldMetadata: unknown;
  fieldType: string;
  isGlobalRequiredField: boolean;
  fileUploading = false;
  fileDeleting = false;
  isSavingInProcess = false;
  fileName: string;

  documentCategoryOptions$ = this.store.select(
    selectDocumentFieldCategoryOptions,
  );

  attachTemplateReq = this.companyDataRoomHelperService
    .checkIfCadrTemplateAttached()
    .pipe(
      switchMap((isAttached) => {
        if (!isAttached) {
          return this.companyDataRoomHelperService.attachCompanyTemplate(
            this.company.id,
            this.company.companyTemplate?.template?.fields || [],
          );
        }

        return of(null);
      }),
    );

  constructor(
    private destroyRef: DestroyRef,
    changeDetectorRef: ChangeDetectorRef,
    private activeModal: NgbActiveModal,
    private formBuilder: UntypedFormBuilder,
    private companyControllerService: CompanyControllerService,
    private store: Store,
    private finToastService: FinToastService,
    private informationControllerService: InformationControllerService,
    private companyDataRoomHelperService: CompanyDataRoomHelperService,
  ) {
    super(changeDetectorRef);
  }

  ngOnInit(): void {
    this.fieldType = this.fieldDto?.fieldType || this.newField.fieldType;

    this.fieldLabel = this.fieldDto?.label || '';

    this.editFieldForm = this.formBuilder.group({
      label: [this.fieldLabel, Validators.required],
      description: this.fieldDto?.description || '',
      categoryId: this.fieldDto?.categoryId || null,
    });

    if (this.fieldType === 'SELECT') {
      this.fieldMetadata = this.fieldDto?.fieldMetaData as [];
    }
    if (this.fieldType === 'DOCUMENT') {
      this.editFieldForm.addControl(
        'documentId',
        new UntypedFormControl(this.companyInformation?.value),
      );
    }

    this.editFieldForm.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(
        ({
          label,
          description,
          documentId,
          categoryId,
        }: {
          label: string;
          description: string;
          documentId?: string;
          categoryId?: string;
        }) => {
          this.fieldLabel = label;
          this.documentId = documentId;
          this.description = description;
          this.categoryId = categoryId;
        },
      );

    this.checkInformationEmtptyValue();
  }

  private checkInformationEmtptyValue() {
    if (
      this.companyInformation &&
      (isObject(this.companyInformation?.value) ||
        Array.isArray(this.companyInformation?.value)) &&
      isEmpty(this.companyInformation?.value)
    ) {
      this.companyInformation = cloneDeep(this.companyInformation);
      this.companyInformation.value = null;
    }
  }

  scrollToBottom() {
    setTimeout(() => {
      void this.scrollbarRef?.scrollTo({ bottom: 0, duration: 500 });
    });
  }

  close() {
    this.activeModal.close({
      success: false,
    });
  }

  save() {
    this.isSavingInProcess = true;
    this.newField ? this.createField() : this.updateField();
  }

  onFileUploading(fileUploading: { isUploading: boolean; fileName?: string }) {
    this.fileUploading = fileUploading.isUploading;
    this.fileName = fileUploading.fileName;
  }

  onFileDeleting(fileDeleting: boolean) {
    this.fileDeleting = fileDeleting;
  }

  private createField() {
    const fieldKey = IdentityService.generateId();
    const fieldDto = {
      ...this.newField,
      isPublic: false,
      label: this.fieldLabel,
      key: fieldKey,
      fieldMetaData: this.fieldMetadata ?? '',
      description: this.description ?? '',
      value: {},
      categoryId: this.editFieldForm?.getRawValue()?.categoryId || undefined,
    };

    if (this.newField.fieldType === 'DOCUMENT' && this.documentId) {
      fieldDto.value = this.documentId;
    }

    this.attachTemplateReq
      .pipe(
        switchMap(() => {
          // update groups structure
          const groupsOrderedModify = cloneDeep(this.orderedGroups);
          groupsOrderedModify
            .find((g: CadrGroup) => g.key === this.fieldGroup.key)
            .fields.push(fieldKey);
          this.orderedGroups = groupsOrderedModify;

          return this.companyControllerService
            .addFieldAndEditCompanyGroups({
              companyId: this.company.id,
              body: {
                groupsOrdered: groupsOrderedModify,
                fieldDto,
              },
            })
            .pipe(
              tap(() => this.successCloseModal(this.fileName)),
              finalize(() => (this.isSavingInProcess = false)),
              catchError((response: HttpErrorResponse) => {
                this.finToastService.show(Toast.error(response.error?.message));
                return of(null);
              }),
            );
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  private updateField() {
    const requests: Observable<unknown>[] = [];

    if (this.isDocumentEdited) {
      const editValueRequest =
        this.companyDataRoomHelperService.handleEditValueRequest(
          this.companyInformation,
          this.documentId,
          this.company.id,
          false,
          this.fieldDto?.key,
        );
      requests.push(editValueRequest);
    }

    if (this.isSelectedOptionRemoved) {
      const editSelectValueRequest =
        this.companyDataRoomHelperService.handleEditValueRequest(
          this.companyInformation,
          null,
          this.company.id,
          false,
          this.fieldDto?.key,
        );

      requests.push(editSelectValueRequest);
    }

    if (
      this.isLabelEdited ||
      this.isMetadataEdited ||
      this.isDescriptionEdited ||
      this.isCategoryEdited
    ) {
      const editFieldBaseData =
        this.informationControllerService.editCompanyField({
          fieldKey: this.fieldDto.key,
          companyId: this.company.id,
          body: {
            label: this.fieldLabel,
            fieldMetaData: this.fieldMetadata ?? '',
            description: this.description ?? this.fieldDto?.description,
            categoryId: this.editFieldForm.getRawValue()?.categoryId || null,
          },
        });

      requests.push(editFieldBaseData);
    }

    if (requests.length) {
      if (!this.isDocumentEdited && !this.isSelectedOptionRemoved) {
        // handleEditValueRequest attaches the template
        requests.unshift(this.attachTemplateReq);
      }

      concat(...requests)
        .pipe(
          takeLast(1),
          tap(() => this.successCloseModal()),
          finalize(() => (this.isSavingInProcess = false)),
          catchError((response: HttpErrorResponse) => {
            this.finToastService.show(Toast.error(response.error?.message));
            return of(null);
          }),
        )
        .subscribe();
    }
  }

  get isSelectedOptionRemoved() {
    return (
      this.isMetadataEdited &&
      !(this.fieldMetadata as string[]).includes(
        this.companyInformation?.value as string,
      )
    );
  }

  get isDocumentEdited() {
    return (
      this.fieldType === 'DOCUMENT' &&
      (this.documentId || this.fileDeleting) &&
      this.documentId !== this.companyInformation?.value
    );
  }

  get isLabelEdited() {
    return this.fieldLabel !== this.fieldDto?.label;
  }

  get isDescriptionEdited() {
    // we dont want type check
    return (
      (this.description || this.fieldDto?.description) &&
      this.description != this.fieldDto?.description
    );
  }

  get isCategoryEdited() {
    return (
      (this.categoryId || this.fieldDto?.categoryId) &&
      this.categoryId != this.fieldDto?.categoryId
    );
  }

  get isMetadataEdited() {
    return (
      this.fieldMetadata &&
      !isEqual(this.fieldMetadata, this.fieldDto.fieldMetaData)
    );
  }

  private successCloseModal(message?: string) {
    if (message) {
      const displayMessage = $localize`:@@fileUpload.success:has been uploaded successfully.`;
      this.finToastService.show(Toast.success(`${message} ${displayMessage}`));
    } else {
      this.finToastService.show(Toast.success());
    }
    this.activeModal.close('success');
  }
}
