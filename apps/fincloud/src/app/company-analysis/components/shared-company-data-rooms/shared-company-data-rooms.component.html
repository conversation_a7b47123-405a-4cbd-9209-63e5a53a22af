@if (loaded) {
  <div class="select-shared-data-room">
    <div class="select-customer">
      <ui-select
        placeholder="Einen mit mir geteilten Data Room auswählen"
        i18n-placeholder="@@sharedCompany.data.rooms.placeholder"
        [options]="sharedCADRs"
        [typeAhead]="searchInput$"
        size="large"
        [customOptionItemTemplate]="sharedCADRtemplate"
        [(ngModel)]="selectedCADR"
        [customSelectedItemTemplate]="selectedCADRtemplate"
        (selectionChange)="onSelectionChanged($event)"
        [isDisabled]="!sharedCADRsInitial?.length"
      ></ui-select>
    </div>
    @if (!sharedCADRs?.length) {
      <div class="empty-data-room">
        <ui-icon name="document" color="subtle"></ui-icon>
        <div class="text" i18n="@@sharedCompany.data.rooms.text">
          Es wurde noch kein Data Room mit Ihnen geteilt. Sobald eine andere
          Organisation ihren Data Room zu diesem Unternehmen mit Ihnen teilt,
          wird dieser hier angezeigt.
        </div>
      </div>
    }
  </div>
}

@if (!!selectedCADR && groupsTemplateFields?.length) {
  <div class="data-room-container">
    @if (selectedCADR?.companyTemplate?.template?.groupsOrdered?.length) {
      <fin-search
        dynamicErrorSpace="dynamic"
        [formControl]="searchControl"
        [showLoader]="false"
        [options]="[]"
        [autocomplete]="false"
        [size]="finSize.M"
        [numberOfResults]="searchResultsCount"
        class="tw-grow"
      ></fin-search>
    }
    @if (groupsTemplateFields?.length) {
      <div class="groups-fields-container">
        @for (
          group of groupsTemplateFields;
          track trackByKey(groupIndex, group);
          let groupIndex = $index
        ) {
          <div
            class="group-container"
            [id]="group.key"
            cdkDropList
            (cdkDropListDropped)="openCreateField($event, group, groupIndex)"
            (cdkDropListEntered)="dropListHover(true, groupIndex)"
            (cdkDropListExited)="dropListHover(false, groupIndex)"
          >
            <div class="section-header-wrapper">
              <div
                class="section-title-overview"
                (appInsideView)="
                  elementIntoView(group.key, groupsTemplateFields)
                "
                (elementNotInView)="
                  elementOutsideOfView(
                    group.key,
                    groupIndex,
                    groupsTemplateFields
                  )
                "
              >
                <span>{{ group.groupIndex }}. {{ group.value }}</span>
                @if (isSharedDataRoom && group.fields?.length !== 0) {
                  <ui-icon
                    class="copy"
                    name="content_copy"
                    size="medium"
                    (click)="copyGroup(group)"
                  ></ui-icon>
                }
                <ui-horizontal-divider
                  color="gray"
                  class="section-title-divider"
                ></ui-horizontal-divider>
              </div>
            </div>
            <div class="fields-group">
              @for (
                templateField of group.templateFields;
                track trackByKeyTemplateField($index, templateField)
              ) {
                <ng-template
                  [ngTemplateOutlet]="templateFieldTemplate"
                  [ngTemplateOutletContext]="{ field: templateField }"
                >
                </ng-template>
              }
            </div>
            @if (group.documents.length) {
              <div class="section-title-overview">
                <span
                  >{{ group.documents.length }}
                  {{
                    group.documents.length > 1 ? 'Dokumente' : 'Dokument'
                  }}</span
                >
              </div>
              <ui-horizontal-divider class="documents"></ui-horizontal-divider>
              <div
                class="tw-grid tw-grid-cols-[repeat(auto-fill,_minmax(27rem,_1fr))] tw-gap-[2.4rem]"
              >
                @for (
                  documentField of group.documents;
                  track trackByKeyTemplateField($index, documentField)
                ) {
                  <ng-template
                    [ngTemplateOutlet]="templateFieldTemplate"
                    [ngTemplateOutletContext]="{ field: documentField }"
                  >
                  </ng-template>
                }
              </div>
            }
          </div>
        }
      </div>
    } @else {
      @if (searchControl.value) {
        <div
          class="tw-text-color-text-tertiary tw-text-body-1-strong tw-text-center tw-pt-10"
          i18n="@@businessCase.dataRoom.noResult"
        >
          Keine Ergebnisse gefunden. Versuchen Sie andere Schlüsselwörter oder
          überprüfen Sie die Rechtschreibung
        </div>
      }
    }
    <ng-template #templateFieldTemplate let-field="field">
      <ui-template-field
        [field]="field.field"
        [information]="field.information"
        [isEditMode]="editMode"
        [templateFieldData]="templateFieldData"
        (fieldCopy)="onFieldCopy(field.information)"
      >
      </ui-template-field>
    </ng-template>
  </div>
}

@if (selectedCADR && !groupsTemplateFields?.length) {
  <div class="empty-cadr-linked empty-fields-message">
    <ui-icon name="svgDisabledVisible" color="subtle"></ui-icon>
    <div class="text" i18n="@@dashboard.businessCase.dataRoom">
      Der unternehmensbezogene Data Room wurde zum Finanzierungsfall
      hinzugefügt, allerdings sind noch keine Felder im unternehmensbezogenen
      Data Room vorhanden. Sobald Felder zu diesem hinzugefügt werden, werden
      diese hier angezeigt.
    </div>
  </div>
}

<ng-template #sharedCADRtemplate let-item="item">
  <div class="custom-shared-cadr-option">
    <div>{{ item?.value.customerName }}</div>
    <div
      class="text-icon-wrapper"
      [class.accent]="item?.value.shareType !== 'IMPLICIT'"
    >
      <ui-icon
        [name]="
          item?.value.shareType === 'IMPLICIT'
            ? 'svgViaCaseShare'
            : 'svgDirectShare'
        "
      ></ui-icon>
      @if (item?.value.shareType === 'IMPLICIT') {
        <span class="text-case" i18n="@@sharedCompany.dataRooms.viaCase">
          via Fall</span
        >
      }
      @if (item?.value.shareType !== 'IMPLICIT') {
        <span class="text-direct" i18n="@@sharedCompany.dataRooms.direct">
          Direkt</span
        >
      }
    </div>
  </div>
</ng-template>

<ng-template #selectedCADRtemplate let-item="item">
  <div class="custom-shared-cadr-option">
    <div>{{ item?.customerName }}</div>
    <div
      class="text-icon-wrapper"
      [class.accent]="item?.shareType !== 'IMPLICIT'"
    >
      <ui-icon
        [name]="
          item?.shareType === 'IMPLICIT' ? 'svgViaCaseShare' : 'svgDirectShare'
        "
      ></ui-icon>
      @if (item?.shareType === 'IMPLICIT') {
        <span class="text-case" i18n="@@sharedCompany.dataRooms.viaCase">
          via Fall</span
        >
      }
      @if (item?.shareType !== 'IMPLICIT') {
        <span class="text-direct" i18n="@@sharedCompany.dataRooms.direct">
          Direkt</span
        >
      }
    </div>
  </div>
</ng-template>
