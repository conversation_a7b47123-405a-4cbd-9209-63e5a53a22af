import { Injectable } from '@angular/core';
import { FileService } from '@fincloud/core/files';
import { ModalService } from '@fincloud/core/modal';
import { SocketService, SocketType } from '@fincloud/core/socket';
import { Toast } from '@fincloud/core/toast';
import { CompanyDataRoomCustomHandlerService } from '@fincloud/neoshare/data-room';
import {
  StateLibCompanyPageActions,
  selectCompanyId,
} from '@fincloud/state/company-analysis';
import {
  selectCustomer,
  selectCustomerKey,
  selectIsVolksbankGroupWithBmsSales,
} from '@fincloud/state/customer';
import { StateLibDataRoomApiActions } from '@fincloud/state/data-room';
import { selectUserId } from '@fincloud/state/user';
import {
  CadrGroup as CadrGroupCompany,
  CompanyControllerService,
  InformationControllerService,
} from '@fincloud/swagger-generator/company';
import { CadrGroup, FieldDto } from '@fincloud/swagger-generator/demo';
import {
  DocumentCategoryControllerService,
  DocumentCompanyControllerService,
  DocumentControllerService,
} from '@fincloud/swagger-generator/document';
import { DownloadResponseNotification } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import {
  PLATFORM_NOTIFICATION_DOCUMENT_LINK_GENERATION_SUBSCRIPTION,
  VOLKSBANK_GROUP_CATEGORIES,
} from '@fincloud/utils';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import { cloneDeep } from 'lodash-es';
import { catchError, filter, map, of, switchMap, take, tap } from 'rxjs';
import {
  CompanyApiActions,
  CompanyDataRoomBusinessCaseApiActions,
  CompanyPageActions,
} from '../actions';
import {
  selectCompany,
  selectCompanyFieldDtoInProcessDelete,
  selectCompanyFieldInformationInProcessDelete,
  selectCompanyOrderedGroups,
} from '../selectors';

@Injectable()
export class CompanyDataRoomEffects {
  deleteCompanyDataRoomField$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(CompanyPageActions.deleteFieldBusinessCaseDataRoomCompany),
      concatLatestFrom(() => [this.store.select(selectCompanyId)]),

      switchMap(([{ payload }, companyId]) =>
        this.informationControllerService
          .deleteByKey({
            companyId: companyId as string,
            key: payload.fieldDto.key,
          })
          .pipe(
            map(() =>
              CompanyDataRoomBusinessCaseApiActions.deleteFieldDataRoomCompanySuccess(),
            ),
            catchError((error: Error) => {
              return of(
                CompanyDataRoomBusinessCaseApiActions.deleteFieldDataRoomCompanyFailure(
                  {
                    errorMessage: error?.message || '',
                  },
                ),
              );
            }),
          ),
      ),
    );
  });

  loadCategories$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibCompanyPageActions.loadCompany),
      concatLatestFrom(() => [
        this.store.select(selectCustomer),
        this.store.select(selectIsVolksbankGroupWithBmsSales),
      ]),
      filter(
        ([, customer, isBmsBankCustomer]) => isBmsBankCustomer && !!customer,
      ),
      switchMap(([, customer]) =>
        this.documentCategoryService
          .getDocumentCategoriesByBankingGroup({
            bankingGroup: customer.bankingGroup,
          })
          .pipe(
            map((data) => {
              const translatedCategories = data.map((item) => {
                return {
                  ...item,
                  name: `[${item.code}] ${VOLKSBANK_GROUP_CATEGORIES[item.key]}`,
                };
              });
              return CompanyDataRoomBusinessCaseApiActions.loadDocumentFieldCategoriesSuccess(
                { payload: translatedCategories },
              );
            }),
            catchError(() =>
              of(
                CompanyDataRoomBusinessCaseApiActions.loadDocumentFieldCategoriesFailure(),
              ),
            ),
          ),
      ),
    ),
  );

  logicallyDeleteDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        CompanyDataRoomBusinessCaseApiActions.deleteFieldDataRoomCompanySuccess,
      ),
      concatLatestFrom(() => [
        this.store.select(selectCompanyFieldInformationInProcessDelete),
        this.store.select(selectCustomerKey),
      ]),
      filter(
        ([, information]) =>
          !!(information.field.fieldType === 'DOCUMENT' && information.value),
      ),
      switchMap(([, information, customerKey]) =>
        this.documentControllerService
          .logicallyDeleteDocument({
            documentId: information.value.toString(),
            customerKey,
          })
          .pipe(
            map(() => StateLibDataRoomApiActions.deleteDocumentFieldSuccess()),
            catchError((error) =>
              of(
                StateLibDataRoomApiActions.deleteDocumentFieldFailure({
                  error,
                }),
              ),
            ),
          ),
      ),
    ),
  );

  private getModifiedOrderedGroups(groups: CadrGroup[], field: FieldDto) {
    const groupsOrderedModify = cloneDeep(groups);
    const groupToEdit = groupsOrderedModify.find((g) =>
      g.fields.some((f) => f === field.key),
    );
    groupToEdit.fields = groupToEdit.fields.filter((f) => f !== field.key);

    return groupsOrderedModify as CadrGroupCompany[];
  }

  updateOrderedGroupsOnCompanyFieldDelete$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        CompanyDataRoomBusinessCaseApiActions.deleteFieldDataRoomCompanySuccess,
      ),
      concatLatestFrom(() => [
        this.store.select(selectCompanyId),
        this.store.select(selectCompanyOrderedGroups),
        this.store.select(selectCompanyFieldDtoInProcessDelete),
      ]),
      switchMap(([, companyId, orderedGroups, fieldDto]) => {
        return this.companyControllerService
          .addFieldAndEditCompanyGroups({
            companyId,
            body: {
              groupsOrdered: this.getModifiedOrderedGroups(
                orderedGroups,
                fieldDto,
              ),
            },
          })
          .pipe(
            map((payload) => {
              return CompanyDataRoomBusinessCaseApiActions.updateDataRoomCompanyFieldGroupsSuccess(
                {
                  groupsOrdered: payload.companyTemplate.template.groupsOrdered,
                },
              );
            }),
            catchError((error) => {
              return of(
                CompanyDataRoomBusinessCaseApiActions.updateDataRoomCompanyFieldGroupsFailure(
                  {
                    errorMessage: error?.message || '',
                  },
                ),
              );
            }),
          );
      }),
    ),
  );

  deleteCaseDataRoomFieldSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          CompanyDataRoomBusinessCaseApiActions.updateDataRoomCompanyFieldGroupsSuccess,
        ),
        tap(() => {
          this.modalService.closeActiveModals(true);
          this.finToastService.show(Toast.success());
        }),
      ),
    { dispatch: false },
  );

  deleteCaseDataRoomFieldFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          CompanyDataRoomBusinessCaseApiActions.deleteFieldDataRoomCompanyFailure,
        ),
        tap((action) => {
          this.finToastService.show(Toast.error(action?.errorMessage || ''));
        }),
      ),
    { dispatch: false },
  );

  companyDataRoomFileUpload$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CompanyPageActions.companyDataRoomUploadFile),
      switchMap(({ valueChange }) =>
        this.cadrCustomHandlerService.handleFile(valueChange, 'COMPANY').pipe(
          map(() =>
            CompanyApiActions.companyDataRoomUploadFileSuccess({
              fieldKey: valueChange.fieldKey,
            }),
          ),
          catchError((error) =>
            of(
              CompanyApiActions.companyDataRoomUploadFileFailure({
                error,
                fieldKey: valueChange.fieldKey,
              }),
            ),
          ),
        ),
      ),
    ),
  );

  initializeZipFilesDownload$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CompanyPageActions.initiateZipFilesDownloadCompanyPage),
      concatLatestFrom(() => [
        this.store.select(selectCompany),
        this.store.select(selectUserId),
      ]),
      tap(([, company, userId]) => {
        this.socketService.joinRoomAndReceiveMessagesByDestination(
          `download-link-user-${userId}-company-${company.id}`,
          PLATFORM_NOTIFICATION_DOCUMENT_LINK_GENERATION_SUBSCRIPTION,
          SocketType.PLATFORM_NOTIFICATION,
        );
      }),
      switchMap(([, company]) => {
        return this.documentCompanyService
          .getAllCompanyDocumentsLink({ companyId: company.id })
          .pipe(
            map(() => {
              return CompanyApiActions.downloadFilesAsZipFromDataRoomSuccess();
            }),
            catchError(() => {
              return of(
                CompanyApiActions.downloadFilesAsZipFromDataRoomFailure(),
              );
            }),
          );
      }),
    ),
  );

  downloadFilesAsZipFromDataRoomSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CompanyApiActions.downloadFilesAsZipFromDataRoomSuccess),

      switchMap(() =>
        this.socketService
          .getMessagesByDestination$<DownloadResponseNotification>(
            PLATFORM_NOTIFICATION_DOCUMENT_LINK_GENERATION_SUBSCRIPTION,
            SocketType.PLATFORM_NOTIFICATION,
          )
          .pipe(
            take(1),
            map((msg) => {
              if (msg.hasFailed) {
                return CompanyApiActions.downloadFilesAsZipFromDataRoomFailure();
              }

              return CompanyApiActions.receivedSocketMessageForDownloadSuccess({
                urlToDownload: msg.link,
              });
            }),
          ),
      ),
    ),
  );

  handleUrlFileDownload$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(CompanyApiActions.receivedSocketMessageForDownloadSuccess),
        concatLatestFrom(() => this.store.select(selectCompany)),
        tap(([{ urlToDownload }, company]) => {
          const zipName = $localize`:@@downloadFilesAsZip.fileName.company:Archiv-company-${company.companyInfo.legalName}.zip`;
          this.fileService.downloadUrl(urlToDownload, zipName);
        }),
      ),
    { dispatch: false },
  );

  downloadFilesAsZipFromDataRoomFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(CompanyApiActions.downloadFilesAsZipFromDataRoomFailure),
        tap(() => {
          this.finToastService.show(Toast.error());
        }),
      ),
    { dispatch: false },
  );

  leaveRoomAfterZipDownloadMessage$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          CompanyApiActions.downloadFilesAsZipFromDataRoomFailure,
          CompanyApiActions.receivedSocketMessageForDownloadSuccess,
        ),
        concatLatestFrom(() => [
          this.store.select(selectCompany),
          this.store.select(selectUserId),
        ]),
        tap(([, company, userId]) => {
          this.socketService.leaveRoom(
            `download-link-user-${userId}-company-${company.id}`,
            SocketType.PLATFORM_NOTIFICATION,
          );
        }),
      ),
    { dispatch: false },
  );

  constructor(
    private actions$: Actions,
    private finToastService: FinToastService,
    private informationControllerService: InformationControllerService,
    private modalService: ModalService,
    private documentCategoryService: DocumentCategoryControllerService,
    private documentControllerService: DocumentControllerService,
    private companyControllerService: CompanyControllerService,
    private cadrCustomHandlerService: CompanyDataRoomCustomHandlerService,
    private store: Store,
    private socketService: SocketService,
    private documentCompanyService: DocumentCompanyControllerService,
    private fileService: FileService,
  ) {}
}
