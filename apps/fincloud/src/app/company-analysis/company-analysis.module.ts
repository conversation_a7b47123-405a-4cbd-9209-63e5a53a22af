import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule, DatePipe, PercentPipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AvatarComponent } from '@fincloud/components/avatar';
import { LocationPreviewMapComponent } from '@fincloud/components/azure-map';
import { NsUiBooleansModule } from '@fincloud/components/booleans';
import { NsUiButtonsModule } from '@fincloud/components/buttons';
import { NsUiDataGridModule } from '@fincloud/components/data-grid';
import { NsUiDataRoomModule } from '@fincloud/components/data-room';
import { NsUiDropdownsModule } from '@fincloud/components/dropdowns';
import { NsUiFormsModule } from '@fincloud/components/forms';
import { NsUiGroupVisibilityStatusInfoModule } from '@fincloud/components/group-visibility-status-info';
import { NsUiHorizontalDividerModule } from '@fincloud/components/horizontal-divider';
import { NsUiIconsModule } from '@fincloud/components/icons';
import { NsUiListsModule } from '@fincloud/components/lists';
import { NsUiLoadersModule } from '@fincloud/components/loaders';
import { NsUiMessagePanelModule } from '@fincloud/components/message-panel';
import { NsUiModalsModule } from '@fincloud/components/modals';
import { NsUiNavigationModule } from '@fincloud/components/navigation';
import { NsUiNumberModule } from '@fincloud/components/number';
import { NsUiSearchFilterModule } from '@fincloud/components/search-filter';
import { NsUiSelectsModule } from '@fincloud/components/selects';
import { NsUiTextModule } from '@fincloud/components/text';
import {
  NsUiNgBootstrapModule,
  NsUiNgxModule,
} from '@fincloud/components/third-party-modules';
import { NsUiTooltipModule } from '@fincloud/components/tooltip';
import { NsUiTreesModule } from '@fincloud/components/trees';
import { NsUiTruncatedTextModule } from '@fincloud/components/truncated-text';
import { NsCoreDateModule } from '@fincloud/core/date';
import { NsCoreDirectivesModule } from '@fincloud/core/directives';
import { NsCoreLayoutModule } from '@fincloud/core/layout';
import {
  NsCorePipesModule,
  RemoveTrailingZerosPipe,
} from '@fincloud/core/pipes';
import { NsCoreScrollModule } from '@fincloud/core/scroll';
import { NsBusinessCaseRefactoringModule } from '@fincloud/neoshare/business-case';
import { NsBusinessCaseModalBaseModule } from '@fincloud/neoshare/business-case-modal-base';
import { NsDataRoomModule } from '@fincloud/neoshare/data-room';
import { NsDocumentModule } from '@fincloud/neoshare/document';
import { NsTemplateFieldModule } from '@fincloud/neoshare/template-field';
import { StateLibDocumentEffects } from '@fincloud/state/document';
import { COMPANY_ANALYSIS_FEATURE_KEY } from '@fincloud/state/utils';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinDropdownModule } from '@fincloud/ui/dropdown';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinInputModule } from '@fincloud/ui/input';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { NgxPermissionsModule } from 'ngx-permissions';

import {
  FIN_MODAL_DEFAULT_OPTIONS,
  FIN_MODAL_REF_PROVIDER,
  FinModalService,
} from '@fincloud/ui/modal';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import { FinSearchComponent } from '@fincloud/ui/search';
import { FinHorizontalSeparatorDirective } from '@fincloud/ui/separators';
import { CompanyGraphModule } from '../company-graph/company-graph.module';
import { reducers } from './+state';
import * as effects from './+state/effects';
import { CompanyAnalysisRoutingModule } from './company-analysis-routing.module';
import { AcceptRemoveExplicitModalComponent } from './components/accept-remove-explicit-modal/accept-remove-explicit-modal.component';
import { CommercialRegisterDocumentsComponent } from './components/commercial-register-documents/commercial-register-documents.component';
import { CompanyBranchEditorComponent } from './components/company-branch-editor/company-branch-editor.component';
import { CompanyBranchesComponent } from './components/company-branches/company-branches.component';
import { CompanyDocumentsDownloadComponent } from './components/company-documents-download/company-documents-download.component';
import { CompanyInformationSectionsComponent } from './components/company-information-sections/company-information-sections.component';
import { CompanyInformationComponent } from './components/company-information/company-information.component';
import { ConfirmEmptyCadrShareModalComponent } from './components/confirm-empty-cadr-share-modal/confirm-empty-cadr-share-modal.component';
import { CopyInformationFieldsModalComponent } from './components/copy-information-fields-modal/copy-information-fields-modal.component';
import { DataRoomTabsComponent } from './components/data-room-tabs/data-room-tabs.component';
import { EditCompanyFieldComponent } from './components/edit-field/edit-company-field.component';
import { CompanyFieldModalComponent } from './components/field-modal/company-field-modal.component';
import { InfoCardComponent } from './components/info-card/info-card.component';
import { LeftSideNavigationComponent } from './components/left-side-navigation/left-side-navigation.component';
import { ManageLinkedCasesModalComponent } from './components/manage-linked-cases-modal/manage-linked-cases-modal.component';
import { OtherBusinessCasesComponent } from './components/other-business-cases/other-business-cases.component';
import { OwnCompanyDataRoomComponent } from './components/own-company-data-room/own-company-data-room.component';
import { RegisterDocumentsTreeComponent } from './components/register-documents-tree/register-documents-tree.component';
import { CompanyFieldRevisionsComponent } from './components/revisions/company-field-revisions.component';
import { SameAddressCompaniesComponent } from './components/same-address-companies/same-address-companies.component';
import { ShareCadrModalComponent } from './components/share-cadr-modal/share-cadr-modal.component';
import { SharedCompanyDataRoomsComponent } from './components/shared-company-data-rooms/shared-company-data-rooms.component';
import { SidebarNavComponent } from './components/sidebar-nav/sidebar-nav.component';
import { CompanyModalService } from './services/company-modal.service';

@NgModule({
  declarations: [
    CompanyInformationComponent,
    SidebarNavComponent,
    InfoCardComponent,
    CompanyInformationSectionsComponent,
    CommercialRegisterDocumentsComponent,
    SameAddressCompaniesComponent,
    OtherBusinessCasesComponent,
    CompanyBranchEditorComponent,
    RegisterDocumentsTreeComponent,
    CompanyDocumentsDownloadComponent,
    OwnCompanyDataRoomComponent,
    SharedCompanyDataRoomsComponent,
    DataRoomTabsComponent,
    LeftSideNavigationComponent,
    ManageLinkedCasesModalComponent,
    EditCompanyFieldComponent,
    CompanyFieldModalComponent,
    CompanyFieldRevisionsComponent,
    CopyInformationFieldsModalComponent,
    ShareCadrModalComponent,
    AcceptRemoveExplicitModalComponent,
    ConfirmEmptyCadrShareModalComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    FinButtonModule,
    FinInputModule,
    FinDropdownModule,
    ReactiveFormsModule,
    CompanyAnalysisRoutingModule,
    DragDropModule,
    AvatarComponent,
    NsDataRoomModule,
    NsDocumentModule,
    NsTemplateFieldModule,
    NgxPermissionsModule.forChild(),
    NsBusinessCaseRefactoringModule,
    NsUiModalsModule,
    NsBusinessCaseModalBaseModule,
    LocationPreviewMapComponent,
    CompanyBranchesComponent,
    NsCoreDirectivesModule,
    NsCoreLayoutModule,
    NsCorePipesModule,
    NsCoreScrollModule,
    NsCoreDateModule,
    NsUiNgBootstrapModule,
    NsUiNgxModule,
    NsUiNavigationModule,
    NsUiBooleansModule,
    NsUiDataRoomModule,
    NsUiIconsModule,
    NsUiLoadersModule,
    NsUiMessagePanelModule,
    NsUiSelectsModule,
    NsUiTruncatedTextModule,
    NsUiSearchFilterModule,
    NsUiTooltipModule,
    NsUiTreesModule,
    NsUiButtonsModule,
    NsUiHorizontalDividerModule,
    NsUiGroupVisibilityStatusInfoModule,
    NsUiModalsModule,
    NsUiListsModule,
    NsUiNumberModule,
    NsUiDropdownsModule,
    NsUiDataGridModule,
    NsUiTextModule,
    NsUiFormsModule,
    CompanyGraphModule,
    FinHorizontalSeparatorDirective,
    FinSearchComponent,
    FinIconModule,
    FinScrollbarModule,
  ],
  providers: [
    DatePipe,
    PercentPipe,
    RemoveTrailingZerosPipe,
    provideState(COMPANY_ANALYSIS_FEATURE_KEY, reducers),
    provideEffects(
      effects.CompanyEffects,
      StateLibDocumentEffects,
      effects.CompanyDataRoomEffects,
    ),
    CompanyModalService,
    { provide: FIN_MODAL_DEFAULT_OPTIONS, useValue: {} },
    FIN_MODAL_REF_PROVIDER,
    FinModalService,
  ],
})
export class CompanyAnalysisModule {}
