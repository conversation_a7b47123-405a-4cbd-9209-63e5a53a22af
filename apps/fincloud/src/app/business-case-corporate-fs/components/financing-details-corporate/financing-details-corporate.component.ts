import { ChangeDetectionStrategy, Component } from '@angular/core';
import {
  StateLibBusinessCasePageActions,
  selectFinancingDetailsCorporateView,
  selectHasAnyBusinessCasePermission,
  selectInvitationTabVisibility,
  selectShowApplicationView,
} from '@fincloud/state/business-case';
import {
  BusinessCasePermission,
  FinancingDetailsPath,
} from '@fincloud/types/enums';
import { FinTabType } from '@fincloud/ui/tabs';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-financing-details-corporate',
  templateUrl: './financing-details-corporate.component.html',
  styleUrl: './financing-details-corporate.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinancingDetailsCorporateComponent {
  readonly tabType: FinTabType = FinTabType.SECONDARY;
  readonly financingDetailsPath = FinancingDetailsPath;

  readonly editModeLabel = $localize`:@@dashboard.overview.finiancingDetails.header.editMode:Bearbeitungsmodus`;
  ownFinancingStructureTabIndex = 0;

  readonly getFinancingDetailsView$ = this.store.select(
    selectFinancingDetailsCorporateView,
  );

  readonly hasApplication$: Observable<boolean> = this.store.select(
    selectShowApplicationView,
  );

  readonly hasInvitation$: Observable<boolean> = this.store.select(
    selectInvitationTabVisibility,
  );

  readonly canSeeOwnParticipation$: Observable<boolean> = this.store.select(
    selectHasAnyBusinessCasePermission([
      BusinessCasePermission.BCP_00140,
      BusinessCasePermission.BCP_00063,
    ]),
  );

  constructor(private store: Store) {}

  editToggleChange(editMode: boolean): void {
    this.store.dispatch(
      StateLibBusinessCasePageActions.updateEditTemplateMode({
        payload: editMode,
      }),
    );
  }
}
