import {
  selectBusinessCaseInformation,
  selectUsersById,
} from '@fincloud/state/business-case';
import { selectGroupsOrdered } from '@fincloud/state/data-room';
import { BusinessCaseDataRoomFiltersState } from '@fincloud/types/models';
import { extractUserIdsFromFolder } from '@fincloud/utils';
import { createSelector } from '@ngrx/store';
import { BaseSelectors } from '@ngrx/store/src/feature_creator';
export const selectDataRoomFiltersSelectors = (
  businessCaseDataRoomFiltersFeature: BaseSelectors<
    'businessCaseDataRoomFilters',
    BusinessCaseDataRoomFiltersState
  >,
) => {
  const selectAllFilters = createSelector(
    businessCaseDataRoomFiltersFeature.selectFieldType,
    businessCaseDataRoomFiltersFeature.selectGroups,
    businessCaseDataRoomFiltersFeature.selectDateUpdated,
    businessCaseDataRoomFiltersFeature.selectUpdatedBy,
    businessCaseDataRoomFiltersFeature.selectFromDate,
    businessCaseDataRoomFiltersFeature.selectToDate,
    businessCaseDataRoomFiltersFeature.selectToggleAllTypes,
    businessCaseDataRoomFiltersFeature.selectDataTypesSelected,
    (
      fieldType,
      groups,
      dateUpdated,
      updatedBy,
      fromDate,
      toDate,
      toggleAllTypes,
      dataTypesSelected,
    ) => {
      return {
        fieldType,
        groups,
        dateUpdated,
        updatedBy,
        fromDate,
        toDate,
        toggleAllTypes,
        dataTypesSelected,
      };
    },
  );

  const selectFiltersCounter = createSelector(
    businessCaseDataRoomFiltersFeature.selectFiltersCounter,
    (filtersCounter) => (filtersCounter ? `(${filtersCounter})` : ''),
  );

  const selectGroupOptions = createSelector(selectGroupsOrdered, (groups) =>
    groups.map((group) => ({
      label: group.value,
      value: group.key,
    })),
  );
  const selectFiltersWithStatus = createSelector(
    selectAllFilters,
    businessCaseDataRoomFiltersFeature.selectFiltersCounter,
    (filters, filtersCounter) => ({
      filters,
      filterMode: !!filtersCounter,
    }),
  );
  const selectUserOptions = createSelector(
    selectBusinessCaseInformation,
    selectGroupsOrdered,
    selectUsersById,
    (information, groups, usersById) => {
      const fieldUserIds = Object.values(information)
        .map((info) => info?.userId)
        .filter(Boolean);

      const folderUserIds = groups
        .filter((group) => group?.rootFolder)
        .flatMap((group) =>
          Array.from(extractUserIdsFromFolder(group.rootFolder)),
        );

      const allUserIds = new Set([...fieldUserIds, ...folderUserIds]);

      const userOptions = [];
      for (const id of allUserIds) {
        const user = usersById[id];
        if (user) {
          userOptions.push({
            label: `${user.firstName} ${user.lastName}`,
            value: id,
          });
        }
      }

      return userOptions;
    },
  );

  return {
    selectAllFilters,
    selectFiltersCounter,
    selectGroupOptions,
    selectUserOptions,
    selectFiltersWithStatus,
  };
};
