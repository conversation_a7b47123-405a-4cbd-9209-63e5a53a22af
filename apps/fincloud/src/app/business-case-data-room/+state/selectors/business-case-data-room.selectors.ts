import { selectHasDataRoomWriteAccess } from '@fincloud/state/access';
import {
  selectAllParticipantsCaseVisibility,
  selectAutoGeneratedBusinessCaseName,
  selectBusinessCaseDashboardState,
  selectBusinessCaseId,
  selectCanSeeTopicChats,
  selectCaseFieldsAccess,
  selectCaseFieldsInputsRequests,
  selectCustomerNamesByKey,
  selectGlobalRequiredFieldKeys,
  selectIsBusinessCaseActive,
  selectIsCommissionFieldDisabled,
  selectIsEmployeeOfLeadCustomer,
  selectIsRepresentingCurrentUserCustomer,
  selectLeaderCustomerKey,
  selectMirroredCalculatableFieldKeys,
  selectMirroredFieldKeys,
  selectParticipantsCustomer,
} from '@fincloud/state/business-case';
import {
  selectActiveTopicChats,
  selectArchivedTopicChats,
} from '@fincloud/state/chat';
import { selectIsRealEstateCustomer } from '@fincloud/state/customer';
import { selectGroupsOrderedWithoutFolderStructure } from '@fincloud/state/data-room';
import { selectSelectedFieldId } from '@fincloud/state/neogpt-chat';
import { sideNavigationsFeature } from '@fincloud/state/side-navigations';
import { selectUserCustomerKey } from '@fincloud/state/user';
import { CaseFieldInputRequest } from '@fincloud/swagger-generator/portal';
import {
  BusinessCaseDashboardState,
  DataRoomTemplateFieldData,
  Dictionary,
  UploadFilesBreakdown,
} from '@fincloud/types/models';
import { LIST_VIEW_DISPLAY_COLUMN_THRESHOLD_LIST } from '@fincloud/utils';
import { createSelector } from '@ngrx/store';
import { keyBy } from 'lodash-es';

export const selectCanShowDataRoomEditToggle = createSelector(
  selectIsBusinessCaseActive,
  selectHasDataRoomWriteAccess,
  (isCaseActive, hasDrWriteAccess) => isCaseActive && hasDrWriteAccess,
);

export const selectCaseFieldsParticipantAccessDict = createSelector(
  selectCaseFieldsAccess,
  (caseFieldsAccess) =>
    keyBy(
      caseFieldsAccess.filter(
        (caseFieldAccess) => caseFieldAccess.requestType === 'CUSTOMER',
      ),
      'informationId',
    ),
);

export const selectCaseFieldsParticipantInputsReqsDict = createSelector(
  selectCaseFieldsInputsRequests,
  selectIsEmployeeOfLeadCustomer,
  selectUserCustomerKey,
  (
    caseFieldsInputsRequests,
    isLeadCustomer,
    userCustomerKey,
  ): Dictionary<CaseFieldInputRequest> =>
    keyBy(
      caseFieldsInputsRequests.filter(
        (caseFieldInputRequest) =>
          (caseFieldInputRequest.requestType === 'CUSTOMER' &&
            isLeadCustomer) ||
          caseFieldInputRequest.customerKey === userCustomerKey,
      ),
      'informationId',
    ),
);

export const selectParticipantsWithNames = createSelector(
  selectParticipantsCustomer,
  selectCustomerNamesByKey,
  (participants, customerNamesByKey) =>
    (participants || []).map((p) => customerNamesByKey[p.customerKey]),
);

export const selectDataRoomUploadFilesBreakdownList = createSelector(
  selectBusinessCaseDashboardState,
  (state: BusinessCaseDashboardState): UploadFilesBreakdown[] =>
    state.uploadFilesBreakdownList,
);

export const selectDataRoomTemplateFieldData = createSelector(
  selectActiveTopicChats,
  selectArchivedTopicChats,
  selectCaseFieldsParticipantAccessDict,
  selectIsEmployeeOfLeadCustomer,
  selectCaseFieldsParticipantInputsReqsDict,
  selectUserCustomerKey,
  selectLeaderCustomerKey,
  selectIsCommissionFieldDisabled,
  selectCanSeeTopicChats,
  selectAllParticipantsCaseVisibility,
  selectIsRealEstateCustomer,
  selectIsRepresentingCurrentUserCustomer,
  selectParticipantsWithNames,
  selectMirroredFieldKeys,
  selectMirroredCalculatableFieldKeys,
  selectGlobalRequiredFieldKeys,
  selectSelectedFieldId,
  selectBusinessCaseId,
  selectAutoGeneratedBusinessCaseName,
  selectHasDataRoomWriteAccess,
  selectDataRoomUploadFilesBreakdownList,
  selectGroupsOrderedWithoutFolderStructure,
  sideNavigationsFeature.selectNavigationAndChatAreOpen,
  sideNavigationsFeature.selectMainPanelWidth,
  (
    activeTopicChats,
    archivedTopicChats,
    caseFieldsParticipantAccessDict,
    isEmployeeOfLeadCustomer,
    caseFieldsParticipantInputsReqsDict,
    userCustomerKey,
    caseLeaderCustomerKey,
    isCommissionFieldDisabled,
    canSeeTopicChats,
    allParticipantsCaseVisibility,
    isRealEstateCustomer,
    isRepresentingCurrentUserCustomer,
    participantsWithNames,
    mirroredFieldKeys,
    mirroredCalculatableFieldKeys,
    globalRequiredFieldKeys,
    selectedAIAssistantSourceId,
    businessCaseId,
    autoGeneratedBusinessCaseName,
    hasDataRoomWriteAccess,
    uploadingFilesBreakdownList,
    groupsWithoutFolderStructure,
    navigationAndChatAreOpen,
    mainPanelWidth,
  ): DataRoomTemplateFieldData => ({
    dynamicErrorSpace: 'dynamic',
    countOfOccupiedColumns:
      navigationAndChatAreOpen.pageNavigationIsOpen ||
      navigationAndChatAreOpen.isChatVisible
        ? 3
        : 4,
    fieldActionsConfig: {
      supportsParticipantFieldRequest: isEmployeeOfLeadCustomer,
      supportsChat: true,
      supportsFieldDescription: true,
      supportsFieldVisibility: false,
      supportsFolderStructure: true,
    },
    folderStructureListViewDisplayOptions: {
      fileSize:
        mainPanelWidth > LIST_VIEW_DISPLAY_COLUMN_THRESHOLD_LIST.fileSize,
      dateUpdated:
        mainPanelWidth > LIST_VIEW_DISPLAY_COLUMN_THRESHOLD_LIST.dateUpdated,
    },
    dataRoomOwnerId: businessCaseId,
    dataRoomOwnerName: autoGeneratedBusinessCaseName,
    existingTopicChats: activeTopicChats,
    existingArchivedTopicChats: archivedTopicChats,
    isEmployeeOfLeadCustomer: isEmployeeOfLeadCustomer,
    caseFieldsParticipantInputsReqsDict,
    isVisibilityForAllParticipantsOn: allParticipantsCaseVisibility,
    isCustomerRealEstate: isRealEstateCustomer,
    isPartOfBusinessCase: isRepresentingCurrentUserCustomer,
    businessCaseParticipants: participantsWithNames,
    hasDataRoomWriteAccess,
    caseFieldsParticipantAccessDict,
    userCustomerKey,
    caseLeaderCustomerKey,
    isCommissionFieldDisabled,
    canSeeTopicChats,
    mirroredFieldKeys,
    mirroredCalculatableFieldKeys,
    globalRequiredFieldKeys,
    selectedAIAssistantSourceId,
    uploadingFilesBreakdownList,
    groupsWithoutFolderStructure,
  }),
);
