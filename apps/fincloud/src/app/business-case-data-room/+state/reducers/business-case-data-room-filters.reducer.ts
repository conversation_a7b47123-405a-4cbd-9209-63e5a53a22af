import { DataRoomFilterFieldType } from '@fincloud/types/enums';
import { BusinessCaseDataRoomFiltersState } from '@fincloud/types/models';
import { createFeature, createReducer, on } from '@ngrx/store';
import { BusinessCaseDataRoomFiltersPageActions } from '../actions';
import { selectDataRoomFiltersSelectors } from '../selectors';

export const initialState: BusinessCaseDataRoomFiltersState = {
  fieldType: DataRoomFilterFieldType.ALL,
  groups: [],
  dateUpdated: '',
  updatedBy: [],
  fromDate: '',
  toDate: '',
  toggleAllTypes: false,
  dataTypesSelected: {},
  filtersCounter: 0,
};

export const businessCaseDataRoomFiltersFeature = createFeature({
  name: 'businessCaseDataRoomFilters',
  reducer: createReducer(
    initialState,
    on(
      BusinessCaseDataRoomFiltersPageActions.saveFilters,
      (state, { filters }): BusinessCaseDataRoomFiltersState => {
        const counter = [
          DataRoomFilterFieldType.ALL !== filters.fieldType,
          filters?.dataTypesSelected &&
            Object.values(filters.dataTypesSelected).some(Boolean),
          filters.updatedBy?.length,
          filters.groups?.length,
          !!filters.dateUpdated,
        ].filter(Boolean).length;

        return {
          ...state,
          ...filters,
          filtersCounter: counter,
        };
      },
    ),
    on(
      BusinessCaseDataRoomFiltersPageActions.resetFilters,
      (): BusinessCaseDataRoomFiltersState => initialState,
    ),
  ),
  extraSelectors: selectDataRoomFiltersSelectors,
});
