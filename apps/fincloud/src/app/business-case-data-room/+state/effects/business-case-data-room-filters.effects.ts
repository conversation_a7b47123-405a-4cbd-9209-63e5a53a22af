import { Injectable } from '@angular/core';
import { LayoutCommunicationService } from '@fincloud/core/layout';
import { StateLibBusinessCasePageActions } from '@fincloud/state/business-case';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { tap } from 'rxjs';

@Injectable()
export class BusinessCaseDataRoomFiltersEffects {
  constructor(
    private actions$: Actions,
    private layoutCommunicationService: LayoutCommunicationService,
  ) {}

  closeSideFilters$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(StateLibBusinessCasePageActions.clearBusinessCase),
        tap(() =>
          this.layoutCommunicationService.clearRightOverlayPanelTemplate(),
        ),
      ),
    { dispatch: false },
  );
}
