@if (navigationsAreOpen$ | async; as navigation) {
  <ng-template #filtersTemplate>
    <app-business-case-data-room-filters></app-business-case-data-room-filters>
  </ng-template>
  @if (!groupsTemplateFieldsInitial?.length) {
    <ng-container [ngTemplateOutlet]="noInitialResult"></ng-container>
  } @else {
    <app-sidebar-content-manager
      class="tw-h-full"
      [sidePanelStyles]="{ height: sidePanelHeight$ | async }"
      [pagePanelIsOpen]="navigation.pageNavigationIsOpen"
      [observeMainPanelResize]="true"
      (toggle)="toggleNavigation(navigation)"
      cdkDropListGroup
    >
      <ng-template #navPanel>
        <div class="tw-flex tw-flex-col tw-h-full">
          @if (hasDataRoomWriteAccess$ | async) {
            <fin-switch-toggle
              [formControl]="activeDataRoomSidebarTabControl"
              [options]="dataRoomSidebarOptions"
              [stretched]="false"
              class="tw-select-none tw-py-[1.6rem] tw-px-[1.2rem]"
            ></fin-switch-toggle>
          }
          @if (
            activeDataRoomSidebarTabControl?.value ===
            dataRoomSidebarTabs.GROUPS
          ) {
            <fin-scrollbar class="tw-grow">
              <app-data-room-groups-side-bar></app-data-room-groups-side-bar>
            </fin-scrollbar>

            @if (canShowEditToggle$ | async) {
              <div
                class="tw-flex tw-text-[1.4rem] tw-font-bold tw-border-t tw-border-color-border-default-primary tw-h-[5.2rem] tw-p-[0.8rem]"
              >
                <button
                  fin-button
                  [disabled]="editMode$ | async | isFalsy"
                  [appearance]="finButtonAppearance.STEALTH"
                  (click)="addGroup()"
                >
                  <fin-icon name="add" [size]="finSize.L"></fin-icon>
                  <span i18n="@@sideNav.button.add"
                    >Neue Gruppe hinzufügen</span
                  >
                </button>
              </div>
            }
          } @else {
            <fin-scrollbar>
              <div
                class="tw-px-[1.2rem] tw-pb-[1.6rem]"
                cdkDropList
                cdkDropListSortingDisabled
                [cdkDropListData]="dataRoomFieldType"
              >
                @for (fieldType of dataRoomFieldType; track $index) {
                  <div
                    cdkDrag
                    [cdkDragData]="fieldType"
                    (cdkDragStarted)="dragStart(fieldType)"
                    (cdkDragEnded)="dragEnded()"
                    class="tw-flex tw-justify-center tw-items-center tw-gap-[0.8rem] tw-mb-[0.4rem] tw-cursor-grabbing tw-h-[4rem] tw-py-[1.6rem] tw-bg-color-background-tertiary-minimal tw-px-[1.6rem] tw-rounded-[0.8rem] tw-select-none tw-overflow-hidden [&.cdk-drag-preview]:tw-shadow-elevation-medium"
                  >
                    <fin-icon
                      [name]="fieldType.iconName"
                      [size]="finSize.S"
                    ></fin-icon>

                    <div
                      class="tw-flex-grow tw-text-color-text-primary tw-text-[1.2rem] tw-font-semibold"
                    >
                      {{ fieldType.label }}
                    </div>

                    <fin-icon
                      name="drag_indicator"
                      [size]="finSize.S"
                      class="tw-text-color-icons-tertiary"
                    ></fin-icon>
                  </div>
                }
              </div>
            </fin-scrollbar>
          }
        </div>
      </ng-template>

      <ng-template #mainPanel>
        <div class="tw-h-full">
          <div
            class="tw-flex tw-items-center tw-bg-color-surface-primary tw-px-[2.4rem] tw-py-[2rem] tw-mx-[0.1rem]"
          >
            @if (groupsTemplateFieldsInitial?.length) {
              <fin-search
                dynamicErrorSpace="dynamic"
                [formControl]="searchControl"
                [showLoader]="false"
                [options]="[]"
                [autocomplete]="false"
                [size]="finSize.M"
                [numberOfResults]="searchResultsCount"
                class="tw-grow"
              ></fin-search>
            }
            <div class="tw-flex tw-gap-4 tw-ml-[2.4rem]">
              <button
                fin-button
                [shape]="finButtonShape.RECTANGLE"
                [size]="finSize.L"
                [appearance]="finButtonAppearance.INFORMATIVE"
                (click)="onToggleFilters()"
              >
                @if (getFiltersCounter$ | async) {
                  <fin-icon
                    src="assets/svg/svgFilter.svg"
                    [size]="finSize.L"
                  ></fin-icon>
                } @else {
                  <fin-icon name="filter_list" [size]="finSize.L"></fin-icon>
                }

                <span i18n="@@cases.filters.title">Filter</span>
                @if (getFiltersCounter$ | async; as filtersCounter) {
                  <span>{{ filtersCounter }}</span>
                }
              </button>
              @if (canSeeInbox$ | async) {
                <button
                  fin-button
                  [shape]="finButtonShape.RECTANGLE"
                  [size]="finSize.L"
                  [appearance]="finButtonAppearance.INFORMATIVE"
                  [disabled]="editMode$ | async | isFalsy"
                  (click)="onNavigateToInbox()"
                >
                  <fin-icon name="inbox" [size]="finSize.M"></fin-icon>
                  <span i18n="@@documentInbox.title">Inbox</span>
                </button>
              }

              @if (isActionButtonVisible$ | async) {
                <div class="tw-flex">
                  <button
                    fin-button
                    [shape]="finButtonShape.RECTANGLE"
                    [size]="finSize.L"
                    [appearance]="finButtonAppearance.INFORMATIVE"
                    [finActionMenuTrigger]="finMenu.panel"
                    #trigger="finActionMenuTrigger"
                  >
                    <fin-icon name="more_vert" [size]="finSize.M"></fin-icon>
                    <span i18n="@@button.label.actions">Aktionen</span>
                  </button>
                  <fin-actions-menu #finMenu="finActionMenu">
                    @if (editMode && (hasPermissionForGroupAccess$ | async)) {
                      <app-group-visibility-card
                        [groups]="
                          businessCase?.businessCaseTemplate?.template
                            .groupsOrdered
                        "
                        [participants]="caseParticipants$ | async"
                        [businessCaseId]="businessCase?.id"
                      ></app-group-visibility-card>
                    }
                    @if (hasPermissionForDocumentExports$ | async) {
                      <app-download-files-as-zip
                        page="business-case"
                        [isCADR]="isCompanyDataRoomTab$ | async"
                        [customerKey]="userCustomerKey$ | async"
                        (downloadClicked)="handleDownloadClick()"
                      ></app-download-files-as-zip>
                    }
                    @if (hasPermissionsForTeaserExport$ | async) {
                      <app-teaser-download
                        [businessCase]="businessCase"
                      ></app-teaser-download>
                    }
                  </fin-actions-menu>
                </div>
              }
            </div>
          </div>
          <fin-scrollbar
            [id]="mainScrollbarId"
            appBusinessCaseMainContentScroll
            [smoothResize]="true"
            [style.height]="contentWrapperHeight$ | async"
          >
            <div
              class="tw-flex tw-flex-col"
              [style.min-height]="contentHeight$ | async"
            >
              @if (groupsTemplateFields?.length) {
                @if (draggableitem$ | async; as draggableitem) {
                  <div class="tw-px-[2.4rem] tw-pb-8">
                    @for (
                      group of groupsTemplateFields;
                      track trackByKey(groupIndex, group);
                      let groupIndex = $index
                    ) {
                      <div
                        class="tw-flex tw-flex-col tw-gap-[2.4rem] tw-pt-1"
                        (appInsideView)="onGroupVisible(group.key)"
                        (elementNotInView)="onGroupNotVisible(group.key)"
                      >
                        <div class="section-header-wrapper">
                          <div>
                            <fin-card-label
                              [id]="groupPrefix + group.key"
                              [label]="group.groupIndex + '. ' + group.value"
                              [size]="finSize.M"
                            >
                              <ng-container finCardIcon>
                                @defer (on viewport) {
                                  @if (isEmployeeOfLeadCustomer$ | async) {
                                    <app-data-room-group-visibility-state
                                      [group]="group"
                                      [isChangeable]="false"
                                    ></app-data-room-group-visibility-state>
                                  }
                                } @placeholder {
                                  <div></div>
                                }
                              </ng-container>
                            </fin-card-label>
                          </div>
                        </div>
                        <div
                          class="tw-grid tw-gap-[2.4rem] tw-grid-cols-3"
                          [class.tw-grid-cols-4]="
                            !navigation.pageNavigationIsOpen &&
                            !navigation.isChatVisible
                          "
                          cdkDropList
                          [cdkDropListEnterPredicate]="
                            dropListEnterPredicate(groupIndex, true)
                          "
                          (cdkDropListDropped)="
                            openCreateField($event, group, groupIndex)
                          "
                          (cdkDropListEntered)="dropListHover(true, groupIndex)"
                          (cdkDropListExited)="dropListHover(false, groupIndex)"
                          appSectionHightlightOnHover
                          [showHightlightOnHover]="
                            draggableitem === dataRoomDraggedItemType.OTHER &&
                            !searchControl.value &&
                            !filterMode
                          "
                          [extraPadding]="true"
                          [draggingActive]="
                            draggableitem !== dataRoomDraggedItemType.NONE
                          "
                        >
                          @if (hasDataRoomWriteAccess$ | async) {
                            @if (
                              !group.templateFields?.length &&
                              editMode &&
                              !searchControl.value &&
                              !filterMode
                            ) {
                              <div
                                class="tw-col-span-full"
                                [ngClass]="{
                                  'nextfolder-additional-height':
                                    isServiceSynchronizedWithNextfolder$
                                    | async,
                                }"
                              >
                                <fin-empty-state
                                  [type]="finEmptyStateType.BASIC"
                                  [title]="emptyGeneralGroupText"
                                >
                                  <ng-template #finIcon>
                                    <fin-icon
                                      name="view_timeline"
                                      class="tw-text-color-icons-tertiary"
                                      [size]="finSize.L"
                                    >
                                    </fin-icon>
                                  </ng-template>
                                </fin-empty-state>
                              </div>
                            }
                          }
                          @for (
                            templateField of group.templateFields;
                            track trackByKeyTemplateField($index, templateField)
                          ) {
                            <ui-template-field
                              class="tw-pointer-events-auto"
                              [field]="templateField.field"
                              [information]="templateField.information"
                              [isEditMode]="editMode"
                              [isSearchMode]="!!searchControl.value"
                              [isFilterMode]="filterMode"
                              [templateFieldData]="
                                dataRoomTemplateFieldData$ | async
                              "
                              (fieldDeleted)="
                                openDeleteFieldModal(templateField.information)
                              "
                              (openModalRequested)="
                                openEditModal(
                                  $event.modalTab,
                                  templateField.field
                                )
                              "
                              (valueChanged)="onFieldValueChanged($event)"
                            >
                            </ui-template-field>
                          }
                        </div>
                        <div
                          cdkDropList
                          [cdkDropListEnterPredicate]="
                            dropListEnterPredicate(groupIndex, false)
                          "
                          (cdkDropListDropped)="
                            openCreateField($event, group, groupIndex)
                          "
                          (cdkDropListEntered)="dropListHover(true, groupIndex)"
                          (cdkDropListExited)="dropListHover(false, groupIndex)"
                          appSectionHightlightOnHover
                          [showHightlightOnHover]="
                            (draggableitem ===
                              dataRoomDraggedItemType.DOCUMENT ||
                              draggableitem ===
                                dataRoomDraggedItemType.FOLDER) &&
                            !searchControl.value &&
                            !filterMode
                          "
                          [draggingActive]="
                            draggableitem !== dataRoomDraggedItemType.NONE
                          "
                        >
                          @if (group.rootFolder; as folderStructure) {
                            <app-folder-structure
                              class="tw-rounded-[0.4rem]"
                              [class.pointer-events-on-document-section]="
                                pointerEventsOnDocumentSection$ | async
                              "
                              appDragAndDropContainer
                              [hoverDelay]="hoverDelay"
                              [isActive]="
                                editMode &&
                                (documentInteractionsService.canHightlightSectionOnDrag$
                                  | async)
                              "
                              [context]="folderStructureContext.BUSINESS_CASE"
                              [groupKey]="group.key"
                              [rootFolder]="folderStructure"
                              [documentFields]="
                                currentFolderDocumentFieldsPerGroup$
                                  | async
                                  | executeFunc
                                    : getCurrentFolderDocumentsForGroup
                                    : group
                              "
                              [documentTemplateFieldData]="
                                dataRoomTemplateFieldData$ | async
                              "
                              [isServiceSynchronizedWithDracoon]="
                                isServiceSynchronizedWithDracoon$ | async
                              "
                              [isServiceSynchronizedWithNextfolder]="
                                isServiceSynchronizedWithNextfolder$ | async
                              "
                              [nextfolderDocuments]="
                                nextfolderDocuments$ | async
                              "
                              [editMode]="editMode"
                              [searchMode]="!!searchControl.value"
                              [filterMode]="filterMode"
                              [highlight]="highlighted$ | async"
                              (filesDropped)="
                                addDocumentToSection(group, $event)
                              "
                              (searchResultFolderClicked)="
                                scrollToGroup($event)
                              "
                              (showFolderInEnclosingFolder)="
                                showFolderInEnclosingFolder($event, group.key)
                              "
                              (fileUpload)="onFileUpload($event)"
                              (fieldDeleted)="openDeleteFieldModal($event)"
                              (openModalRequested)="
                                openEditModal($event.modalTab, $event.field)
                              "
                              (showDocumentInEnclosingFolder)="
                                showDocumentInEnclosingFolder($event, group.key)
                              "
                              (documentFieldValueChanged)="
                                onFieldValueChanged($event)
                              "
                            >
                            </app-folder-structure>
                          }
                        </div>
                      </div>
                    }
                  </div>
                }
              } @else {
                <ng-container
                  [ngTemplateOutlet]="noResultsTemplate"
                ></ng-container>
              }
            </div>
          </fin-scrollbar>
        </div>

        <ng-template #noResult>
          @if (searchControl.value) {
            <div
              class="tw-text-color-text-tertiary tw-text-body-1-strong tw-text-center tw-pt-10"
              i18n="@@businessCase.dataRoom.noResult"
            >
              Keine Ergebnisse gefunden. Versuchen Sie andere Schlüsselwörter
              oder überprüfen Sie die Rechtschreibung
            </div>
          }
        </ng-template>
      </ng-template>

      @if (isNeoGptVisible$ | async; as neoGptState) {
        @if (neoGptState.isChatVisible && neoGptState.isNeoGptActive) {
          <ng-template #rightPanel>
            <div class="tw-grow">
              <app-neogpt-chat></app-neogpt-chat>
            </div>
          </ng-template>
        }
      }
    </app-sidebar-content-manager>
  }
}

<ng-template #noInitialResult>
  <div
    [ngStyle]="{ height: sidePanelHeight$ | async }"
    class="tw-bg-color-surface-primary"
  >
    <fin-empty-state
      [type]="finEmptyStateType.BASIC"
      i18n-title="@@businessCase.DataRoom.noInformation"
      title="Es werden aktuell keine Informationen mit Ihnen geteilt. Sobald Data
    Room Felder für Sie freigegeben werden, werden diese hier
    angezeigt."
    >
      <ng-template #finIcon>
        <fin-icon
          src="assets/svg/svgDocument.svg"
          class="tw-text-color-icons-tertiary"
          [size]="finSize.XL"
        >
        </fin-icon>
      </ng-template>
    </fin-empty-state>
  </div>
</ng-template>

<ng-template #noResultsTemplate>
  <div
    class="tw-text-color-text-tertiary tw-text-body-1-strong tw-text-center tw-pt-10"
  >
    @if (searchControl.value && filterMode) {
      <div i18n="@@businessCase.dataRoom.noResultAfterSearchAndFilter">
        Keine passenden Ergebnisse. Versuchen Sie andere Filtereinstellungen
        oder Schlüsselwörter.
      </div>
    } @else if (searchControl.value) {
      <div i18n="@@businessCase.dataRoom.noResult">
        Keine Ergebnisse gefunden. Versuchen Sie andere Schlüsselwörter oder
        überprüfen Sie die Rechtschreibung
      </div>
    } @else {
      <div i18n="@@businessCase.dataRoom.noResultAfterFilter">
        Keine passenden Ergebnisse. Versuchen Sie andere Filtereinstellungen.
      </div>
    }
  </div>
</ng-template>
