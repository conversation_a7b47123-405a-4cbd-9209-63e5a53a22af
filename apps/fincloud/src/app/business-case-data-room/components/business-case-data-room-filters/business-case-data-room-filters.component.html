<div
  class="tw-flex tw-justify-between tw-items-center tw-py-[1.2rem] tw-px-[1.6rem] tw-border-b tw-border-color-border-default-primary"
>
  <div class="tw-text-body-1-strong" i18n="@@cases.filters.title">Filter</div>
  <button
    fin-button-icon
    [appearance]="finButtonAppearance.INFORMATIVE"
    (click)="close()"
  >
    <fin-icon [size]="finSize.L" name="close"></fin-icon>
  </button>
</div>

<!-- 18.5rem = header + footer + spacing -->
<fin-scrollbar class="tw-max-h-[calc(100vh-18.5rem)]">
  <div [formGroup]="filtersGroup" class="tw-px-[2.4rem] tw-pb-[2.4rem]">
    <fin-card-label
      i18n-label="@@businessCaseDataRoom.filters.fieldType"
      label="Feldtyp"
    ></fin-card-label>
    <div class="tw-py-[1.6rem]">
      <fin-radio-button
        formControlName="fieldType"
        [options]="filterOptions"
        (selectionChanged)="onFieldTypeChange($event)"
      >
      </fin-radio-button>
    </div>
    <fin-card-label label="Gruppe" i18n-label="@@label.group"></fin-card-label>

    <div class="tw-py-[1.6rem]">
      <fin-dropdown
        label="Gruppe auswählen"
        i18n-label="@@label.selectGroup"
        formControlName="groups"
        [multiple]="true"
        [options]="getGroupOptions$ | async"
      ></fin-dropdown>
    </div>

    <fin-card-label
      label="Aktualisiert am"
      i18n-label="@@snapshot.details.labels.dateUpdated"
    ></fin-card-label>

    <div class="tw-py-[1.6rem]">
      <fin-dropdown
        label="Zeitraum auswählen"
        i18n-label="@@businessCaseDataRoom.filters.selectPeriod"
        formControlName="dateUpdated"
        [options]="dateUpdatedOptions"
      ></fin-dropdown>
    </div>

    @if (dateUpdated.value === periodOption.CUSTOM_RANGE) {
      <div class="tw-flex tw-flex-row tw-gap-[2.4rem] tw-pb-[1.6rem] tw-w-full">
        <fin-date-picker
          class="tw-block"
          [showIcon]="true"
          [showButtonBar]="true"
          [formControl]="fromDateControl"
          [size]="finSize.L"
          [maxDate]="maxDate"
        >
          <ng-container finFieldLabel>
            <label class="control__label" i18n="@@dateRangeField.label.from"
              >Von</label
            >
          </ng-container>
        </fin-date-picker>
        <fin-date-picker
          class="tw-block"
          [showIcon]="true"
          [showButtonBar]="true"
          [formControl]="toDateControl"
          [size]="finSize.L"
          [minDate]="minDate"
        >
          <ng-container finFieldLabel>
            <label class="control__label" i18n="@@dateRangeField.label.to"
              >Bis</label
            >
          </ng-container>
        </fin-date-picker>
      </div>
    }

    <fin-card-label
      label="Aktualisiert von"
      i18n-label="@@snapshot.details.labels.updatedBy"
    ></fin-card-label>

    <div class="tw-py-[1.6rem]">
      <fin-dropdown
        label="Nutzer auswählen"
        i18n-label="@@dataRoom.participantFieldRequest.title"
        formControlName="updatedBy"
        [multiple]="true"
        [options]="getUserOptions$ | async"
      ></fin-dropdown>
    </div>

    @if (fieldType.value !== dataRoomFilterFieldType.DATA) {
      <fin-card-label
        label="Typ"
        i18n-label="@@snapshot.details.labels.type"
      ></fin-card-label>

      <div class="tw-flex tw-flex-col tw-gap-[1.2rem] tw-pt-[1.6rem]">
        <div
          class="tw-pb-[1.2rem] tw-border-b tw-border-color-border-default-primary"
        >
          <fin-checkbox
            class="tw-text-body-2-moderate"
            i18n-label="@@dataRoom.selectAll"
            label="Alle auswählen"
            formControlName="toggleAllTypes"
            (changed)="onToggleAllTypes($event)"
            #toggleAllTypesCheckbox
          >
          </fin-checkbox>
        </div>

        @for (type of dataTypeOptions; track type.label) {
          <div class="tw-flex tw-items-center">
            <fin-checkbox
              class="tw-mr-[1.2rem]"
              [formControl]="selectedDataTypeControls[type.value]"
              (changed)="onSelectDataType()"
            >
            </fin-checkbox>
            <fin-icon [size]="finSize.L" [src]="type.iconPath"></fin-icon>
            <span class="tw-text-body-2-moderate tw-ml-[0.8rem]">
              {{ type.label }}
            </span>
          </div>
        }
      </div>
    }
  </div>
</fin-scrollbar>

<div
  class="tw-flex tw-justify-between tw-p-[1.6rem] tw-border-t tw-border-color-border-default-primary"
>
  <button
    fin-button
    [appearance]="finButtonAppearance.SECONDARY"
    [shape]="finButtonShape.ROUND"
    [size]="finSize.L"
    (click)="clearFilters()"
  >
    <span i18n="@@button.label.clearFilters">Filter zurücksetzen</span>
  </button>

  <button
    fin-button
    [shape]="finButtonShape.ROUND"
    [size]="finSize.L"
    (click)="applyFilters()"
  >
    <span i18n="@@cases.filter.button.right">Anwenden</span>
  </button>
</div>
