import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  OnD<PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, NonNullableFormBuilder } from '@angular/forms';
import { LayoutCommunicationService } from '@fincloud/core/layout';
import { DataRoomFilterFieldType, PeriodOption } from '@fincloud/types/enums';
import { BusinessCaseDataRoomFilters } from '@fincloud/types/models';
import { FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import { FinCheckboxComponent } from '@fincloud/ui/checkbox';
import { FinSize } from '@fincloud/ui/types';
import {
  DATA_ROOM_FILTER_OPTIONS,
  DATA_TYPE_OPTIONS,
  SELECT_PERIOD_OPTIONS,
} from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { filter, shareReplay, skip, tap, withLatestFrom } from 'rxjs';
import {
  BusinessCaseDataRoomFiltersPageActions,
  businessCaseDataRoomFiltersFeature,
} from '../../+state';

@Component({
  selector: 'app-business-case-data-room-filters',
  templateUrl: './business-case-data-room-filters.component.html',
  styleUrl: './business-case-data-room-filters.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BusinessCaseDataRoomFiltersComponent implements OnInit, OnDestroy {
  @ViewChild('toggleAllTypesCheckbox')
  toggleAllTypesCheckbox: FinCheckboxComponent;

  readonly finSize = FinSize;
  readonly finButtonAppearance = FinButtonAppearance;
  readonly finButtonShape = FinButtonShape;
  readonly filterOptions = DATA_ROOM_FILTER_OPTIONS;
  readonly dataTypeOptions = DATA_TYPE_OPTIONS;
  readonly dateUpdatedOptions = SELECT_PERIOD_OPTIONS;
  readonly periodOption = PeriodOption;
  readonly dataRoomFilterFieldType = DataRoomFilterFieldType;

  readonly todaysDate = new Date();

  filtersGroup = this.fb.group({
    fieldType: DataRoomFilterFieldType.ALL,
    groups: [[] as string[]],
    dateUpdated: '',
    fromDate: '',
    toDate: '',
    updatedBy: [[] as string[]],
    toggleAllTypes: false,
    dataTypesSelected: this.fb.group(
      this.dataTypeOptions.reduce(
        (controls, type) => {
          controls[type.value] = new FormControl(false);
          return controls;
        },
        {} as Record<PropertyKey, FormControl>,
      ),
    ),
  });

  getGroupOptions$ = this.store.select(
    businessCaseDataRoomFiltersFeature.selectGroupOptions,
  );
  getUserOptions$ = this.store.select(
    businessCaseDataRoomFiltersFeature.selectUserOptions,
  );

  filters$ = this.store
    .select(businessCaseDataRoomFiltersFeature.selectAllFilters)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  get fromDateControl(): FormControl<string> {
    return this.filtersGroup.get('fromDate') as FormControl<string>;
  }

  get toDateControl(): FormControl<string> {
    return this.filtersGroup.get('toDate') as FormControl<string>;
  }

  get minDate(): Date {
    return new Date(this.fromDateControl.value);
  }

  get maxDate(): Date {
    return this.toDateControl.value
      ? new Date(this.toDateControl.value)
      : this.todaysDate;
  }

  get selectedDataTypeFormGroup(): FormGroup {
    return this.filtersGroup.get('dataTypesSelected') as FormGroup;
  }

  get dateUpdated(): FormControl<string> {
    return this.filtersGroup.get('dateUpdated') as FormControl<string>;
  }

  get fieldType(): FormControl<DataRoomFilterFieldType> {
    return this.filtersGroup.get(
      'fieldType',
    ) as FormControl<DataRoomFilterFieldType>;
  }

  get selectedDataTypeControls(): Record<string, FormControl> {
    return this.selectedDataTypeFormGroup.controls as Record<
      string,
      FormControl
    >;
  }

  constructor(
    private fb: NonNullableFormBuilder,
    private store: Store,
    private destroyRef: DestroyRef,
    private layoutCommunicationService: LayoutCommunicationService,
  ) {}

  ngOnInit(): void {
    this.store
      .select(businessCaseDataRoomFiltersFeature.selectFiltersWithStatus)
      .pipe(
        skip(1),
        filter((filters) => !filters.filterMode),
        tap(({ filters }) => this.resetFilters(filters)),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.layoutCommunicationService.layoutClosed$
      .pipe(
        withLatestFrom(this.filters$),
        tap(([, filters]) => this.resetFilters(filters)),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.store.dispatch(BusinessCaseDataRoomFiltersPageActions.resetFilters());
  }

  onSelectDataType(): void {
    const dataTypeControls = this.getSelectedDataTypeControls();
    const allChecked = dataTypeControls.every((c) => !!c.value);
    this.updateIndeterminateState(dataTypeControls);
    this.toggleAllTypesCheckbox?.control.setValue(allChecked);
  }

  onToggleAllTypes(isChecked: boolean): void {
    const dataTypeControls = this.getSelectedDataTypeControls();
    dataTypeControls.forEach((control) => {
      control.setValue(isChecked);
    });

    this.updateIndeterminateState(dataTypeControls);
  }

  onFieldTypeChange(value: string): void {
    if (value === DataRoomFilterFieldType.DATA) {
      this.onToggleAllTypes(false);
      this.filtersGroup.get('toggleAllTypes').setValue(false);
    }
  }

  applyFilters(): void {
    const filtersValue = this.filtersGroup.getRawValue();

    this.store.dispatch(
      BusinessCaseDataRoomFiltersPageActions.saveFilters({
        filters: filtersValue,
      }),
    );
    this.onToggleFilters();
  }

  clearFilters(): void {
    this.store.dispatch(BusinessCaseDataRoomFiltersPageActions.resetFilters());
  }

  close(): void {
    this.onToggleFilters();
  }

  private onToggleFilters(): void {
    this.layoutCommunicationService.toggleRightSideOverlayPanel();
  }

  private resetFilters(filters: BusinessCaseDataRoomFilters): void {
    this.filtersGroup.reset({
      ...filters,
    });
    this.onSelectDataType();
  }

  private getSelectedDataTypeControls(): FormControl[] {
    return Object.values(this.selectedDataTypeControls);
  }

  private updateIndeterminateState(selectedControls: FormControl[]): void {
    const allChecked = selectedControls.every((c) => !!c.value);
    const noneChecked = selectedControls.every((c) => !c.value);

    this.toggleAllTypesCheckbox?.setIndeterminateState(
      !allChecked && !noneChecked,
    );
  }
}
