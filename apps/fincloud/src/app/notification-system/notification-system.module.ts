import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NsCoreDateModule } from '@fincloud/core/date';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { FinActionsMenuModule } from '@fincloud/ui/actions-menu';
import { FinAvatarModule } from '@fincloud/ui/avatar-default';
import { FinBadgesModule } from '@fincloud/ui/badges';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinContainerModule } from '@fincloud/ui/container';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinLoaderModule } from '@fincloud/ui/loader';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import { FinSeparatorsModule } from '@fincloud/ui/separators';
import { FinSlideToggleModule } from '@fincloud/ui/slide-toggle';
import { FinTabsModule } from '@fincloud/ui/tabs';
import { NotificationSettingsComponent } from './components/notification-settings/notification-settings.component';
import { NotificationSystemBadgeComponent } from './components/notification-system-badge/notification-system-badge.component';
import { NotificationSystemNotificationComponent } from './components/notification-system-notification/notification-system-notification.component';
import { NotificationSystemPanelComponent } from './components/notification-system-panel/notification-system-panel.component';
@NgModule({
  declarations: [
    NotificationSystemBadgeComponent,
    NotificationSystemPanelComponent,
    NotificationSystemNotificationComponent,
    NotificationSettingsComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    // TourNgxBootstrapModule,
    // MessageStatusModule,
    FinButtonModule,
    FinBadgesModule,
    FinSeparatorsModule,
    FinIconModule,
    FinContainerModule,
    FinActionsMenuModule,
    FinTabsModule,
    OverlayModule,
    FinLoaderModule,
    FinMenuItemModule,
    FinAvatarModule,
    FinScrollbarModule,
    FinSlideToggleModule,
    NsCoreDateModule,
    NsCorePipesModule,
  ],
  exports: [NotificationSystemBadgeComponent],
})
export class NotificationSystemModule {}
