<button
  fin-button-icon
  [appearance]="buttonAppearance.INFORMATIVE"
  [size]="finSize.M"
  [isActive]="isExpandedMode"
  type="button"
  class="tw-relative"
>
  <fin-icon
    name="notifications"
    [size]="finSize.S"
    [matIconOutlined]="true"
  ></fin-icon>
  @if (
    !isExpandedMode && (unseenNotificationsCount$ | async);
    as unseenNotifications
  ) {
    <fin-badge-indicator
      class="tw-absolute tw-left-[22px] tw-bottom-[16px]"
      [count]="unseenNotifications"
      [type]="finBadgeType.ACTIVE"
      [maxCount]="9"
    ></fin-badge-indicator>
  }
</button>
