import { Component, Input } from '@angular/core';
import { selectUnseenNotificationsCount } from '@fincloud/state/notifications';
import { FinBadgeType } from '@fincloud/ui/badges';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';

@Component({
  selector: 'app-notification-bell',
  templateUrl: './notification-bell.component.html',
  styleUrls: ['./notification-bell.component.scss'],
})
export class NotificationBellComponent {
  @Input() isExpandedMode = false;

  buttonAppearance = FinButtonAppearance;
  finSize = FinSize;
  finBadgeType = FinBadgeType;

  unseenNotificationsCount$ = this.store.select(selectUnseenNotificationsCount);

  constructor(private store: Store) {}
}
