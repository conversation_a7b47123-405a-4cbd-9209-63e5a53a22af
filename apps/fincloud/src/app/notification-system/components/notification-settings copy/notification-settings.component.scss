@use 'styles/src/lib/common';

:host {
  display: block;
}

.settings-header {
  display: flex;
  padding: 1rem;

  .title {
    padding-left: 1rem;
    margin-bottom: 0;
  }

  .go-back {
    cursor: pointer;
  }
}

.settings-list {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .setting {
    @include common.flex-justify(space-between);
    padding: 1rem;
    background-color: theme('colors.color-surface-secondary');
    border-radius: common.$border-radius;

    .name {
      color: theme('colors.color-brand-dark');
      @include common.heading6();
    }
  }
}

