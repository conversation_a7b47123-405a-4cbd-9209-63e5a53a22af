import { DatePipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { DATE_FORMAT_CONFIG, DateService } from '@fincloud/core/date';
import { StateLibNotificationSystemPageActions } from '@fincloud/state/notifications';
import { Notification } from '@fincloud/swagger-generator/platform-notification';
import { NotificationType } from '@fincloud/types/enums';
import { NotificationMessage } from '@fincloud/types/models';
import { FinBadgeType } from '@fincloud/ui/badges';
import { FinSize } from '@fincloud/ui/types';
import { NOTIFICATION_CONFIG } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { NOTIFICATION_SYSTEM_DATE_FORMAT } from '../../utils/notification-system-date-format';

@Component({
  selector: 'app-notification-system-notification',
  templateUrl: './notification-system-notification.component.html',
  styleUrl: './notification-system-notification.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    DateService,
    DatePipe,
    {
      provide: DATE_FORMAT_CONFIG,
      useValue: NOTIFICATION_SYSTEM_DATE_FORMAT,
    },
  ],
})
export class NotificationSystemNotificationComponent implements OnChanges {
  readonly size = FinSize;
  readonly badgeType = FinBadgeType;
  readonly notificationsConfig = NOTIFICATION_CONFIG;
  notificationConfig: NotificationMessage = {} as NotificationMessage;

  @Input() notification: Notification;

  constructor(
    public datePipe: DatePipe,
    private store: Store,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['notification']) {
      this.notificationConfig =
        NOTIFICATION_CONFIG[
          this.notification.type as NotificationType
        ]?.messages?.[this.notification.message];
    }
  }

  markAsRead() {
    this.store.dispatch(
      StateLibNotificationSystemPageActions.markAsRead({
        notification: this.notification,
      }),
    );
  }

  markAsUnread() {
    this.store.dispatch(
      StateLibNotificationSystemPageActions.markAsUnread({
        notification: this.notification,
      }),
    );
  }

  hideNotification() {
    this.store.dispatch(
      StateLibNotificationSystemPageActions.deleteNotification({
        notification: this.notification,
      }),
    );
  }
}
