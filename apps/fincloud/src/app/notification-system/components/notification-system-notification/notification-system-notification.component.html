@if (notificationConfig) {
  <div
    class="tw-relative notification tw-flex tw-text-text-body-2-size tw-text-color-text-primary tw-bg-color-surface-secondary tw-mb-size-spacing-4 tw-rounded-size-corner-radius-s tw-p-size-spacing-16 tw-cursor-pointer hover:tw-bg-color-surface-hover"
  >
    <fin-avatar-default
      [size]="size.XL"
      [icon]="notificationConfig.icon"
    ></fin-avatar-default>
    <div class="tw-ms-size-spacing-12 tw-pe-[2.8rem]">
      <div
        class="notification-text tw-text-text-body-2-moderate-weight tw-color-text-primary-dark-blue tw-mb-size-spacing-1"
        [innerHtml]="
          notification
            | executeFunc: notificationConfig.translationFn : datePipe
        "
      ></div>
      <div class="tw-text-color-text-tertiary tw-text-text-body-3-size">
        {{ notification.timestamp | timeAgo }}
      </div>
    </div>
    @if (!notification.read) {
      <fin-badge-indicator
        [type]="badgeType.ACTIVE"
        [size]="size.XS"
        class="tw-absolute tw-right-size-spacing-16 tw-top-size-spacing-16 unseen-indicator"
      ></fin-badge-indicator>
    }
    <button
      fin-button-action
      [finActionMenuTrigger]="notificationSettingsMenu.panel"
      [size]="size.L"
      class="tw-absolute tw-right-size-spacing-16 tw-top-size-spacing-16 action-button"
    >
      <fin-icon name="more_vert"></fin-icon>
    </button>
    <fin-actions-menu #notificationSettingsMenu="finActionMenu">
      @if (!notification.read) {
        <button
          fin-menu-item
          iconName="check"
          [size]="size.M"
          (click)="markAsRead()"
        >
          <ng-container
            finMenuItemTitle
            i18n="@@notificationSystem.notification.actions.markAsRead"
            >Als gelesen markieren</ng-container
          >
        </button>
      }
      @if (notification.read) {
        <button
          fin-menu-item
          iconName="check"
          [size]="size.M"
          (click)="markAsUnread()"
        >
          <ng-container
            finMenuItemTitle
            i18n="@@notificationSystem.notification.actions.markAsUnread"
            >Als ungelesen markieren</ng-container
          >
        </button>
      }
      <button
        fin-menu-item
        iconName="delete"
        [size]="size.M"
        (click)="hideNotification()"
      >
        <ng-container
          finMenuItemTitle
          i18n="@@notificationSystem.notification.actions.hide"
          >Ausblenden</ng-container
        >
      </button>
    </fin-actions-menu>
  </div>
}
