@if (badgeData$ | async; as badgeData) {
  <button
    fin-button-icon
    [appearance]="buttonAppearance.INFORMATIVE"
    [size]="finSize.M"
    [isActive]="false"
    type="button"
    class="tw-relative"
    cdkOverlayOrigin
    (click)="toggleNotificationPanel()"
    #notificationButton="cdkOverlayOrigin"
  >
    <fin-icon
      name="notifications"
      [size]="finSize.S"
      [matIconOutlined]="true"
    ></fin-icon>
    @if (badgeData.isCounterVisible) {
      <fin-badge-indicator
        class="tw-absolute tw-left-[22px] tw-bottom-[16px]"
        [count]="badgeData.unseenCount"
        [type]="finBadgeType.ACTIVE"
        [maxCount]="9"
      ></fin-badge-indicator>
    }
  </button>
  <ng-template
    cdkConnectedOverlay
    [cdkConnectedOverlayOrigin]="notificationButton"
    [cdkConnectedOverlayOpen]="isNotificationPanelOpen$ | async"
    [cdkConnectedOverlayPush]="true"
    [cdkConnectedOverlayPanelClass]="['tw-pt-[1.2rem]']"
    [cdkConnectedOverlayBackdropClass]="'tw-bg-transparent'"
    [cdkConnectedOverlayHasBackdrop]="true"
    (backdropClick)="closeNotificationSystem()"
  >
    @if (currentView$ | async; as currentView) {
      @if (currentView === notificationSystemView.NOTIFICATIONS) {
        <app-notification-system-panel
          finContainer
          [boxShadow]="true"
          [borderRadius]="true"
        ></app-notification-system-panel>
      } @else if (currentView === notificationSystemView.SETTINGS) {
        <app-notification-settings
          finContainer
          [boxShadow]="true"
          [borderRadius]="true"
        >
        </app-notification-settings>
      }
    }
  </ng-template>
}
