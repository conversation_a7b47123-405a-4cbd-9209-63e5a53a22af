import { ConnectedPosition } from '@angular/cdk/overlay';
import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import {
  StateLibNotificationSystemPageActions,
  notificationSystemFeature,
  selectBadgeCounter,
} from '@fincloud/state/notifications';
import { NotificationSystemView } from '@fincloud/types/enums';
import { FinBadgeType } from '@fincloud/ui/badges';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';

@Component({
  selector: 'app-notification-system-badge',
  templateUrl: './notification-system-badge.component.html',
  styleUrl: './notification-system-badge.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotificationSystemBadgeComponent implements OnInit {
  readonly finSize = FinSize;
  readonly buttonAppearance = FinButtonAppearance;
  readonly finBadgeType = FinBadgeType;
  readonly notificationSystemView = NotificationSystemView;
  readonly notificationPanelPositions: ConnectedPosition[] = [
    {
      originX: 'end',
      originY: 'bottom',
      overlayX: 'end',
      overlayY: 'top',
    },
  ];

  isNotificationPanelOpen$ = this.store.select(
    notificationSystemFeature.selectIsNotificationPanelOpen,
  );

  currentView$ = this.store.select(notificationSystemFeature.selectCurrentView);

  badgeData$ = this.store.select(selectBadgeCounter);

  constructor(private store: Store) {}

  ngOnInit(): void {
    this.store.dispatch(
      StateLibNotificationSystemPageActions.getNotifications(),
    );
  }

  closeNotificationSystem() {
    this.store.dispatch(
      StateLibNotificationSystemPageActions.closeNotificationSystemPanel(),
    );
  }

  toggleNotificationPanel() {
    this.store.dispatch(
      StateLibNotificationSystemPageActions.toggleNotificationSystemPanel(),
    );
  }
}
