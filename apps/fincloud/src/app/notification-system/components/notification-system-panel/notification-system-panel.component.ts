import { ChangeDetectionStrategy, Component } from '@angular/core';
import {
  StateLibNotificationSystemPageActions,
  selectNotificationPanelData,
  selectNotificationsContent,
} from '@fincloud/state/notifications';
import {
  NotificationSystemTab,
  NotificationSystemView,
} from '@fincloud/types/enums';
import { FinBadgeType } from '@fincloud/ui/badges';
import { FinSeparator } from '@fincloud/ui/separators';
import { FinTabType } from '@fincloud/ui/tabs';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';

@Component({
  selector: 'app-notification-system-panel',
  templateUrl: './notification-system-panel.component.html',
  styleUrl: './notification-system-panel.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotificationSystemPanelComponent {
  readonly finBadgeType = FinBadgeType;
  readonly separatorTypes = FinSeparator;
  readonly size = FinSize;
  readonly badgeType = FinBadgeType;
  readonly tabType = FinTabType;
  readonly notificationSystemTab = NotificationSystemTab;
  readonly allTabEmpty = $localize`:@@notificationSystem.notificationList.allTab.empty:Derzeit gibt es keine neuen Benachrichtigungen.`;
  readonly unreadTabEmpty = $localize`:@@notificationSystem.notificationList.unreadTab.empty:Derzeit gibt es keine ungelesenen Benachrichtigungen.`;
  readonly allTabAllNotificationsListed = $localize`:@@notificationSystem.notificationList.upToDate:Sie sind auf dem neuesten Stand.`;

  notifications$ = this.store.select(selectNotificationsContent);
  notificationPanelData$ = this.store.select(selectNotificationPanelData);

  constructor(private store: Store) {}

  loadMoreTodos(isLastPage: boolean) {
    if (isLastPage) {
      return;
    }

    this.store.dispatch(
      StateLibNotificationSystemPageActions.getNotifications(),
    );
  }

  markAllAsRead() {
    this.store.dispatch(StateLibNotificationSystemPageActions.markAllAsRead());
  }

  goToSettings() {
    this.store.dispatch(
      StateLibNotificationSystemPageActions.changeCurrentView({
        view: NotificationSystemView.SETTINGS,
      }),
    );
  }

  tabChange(index: number) {
    this.store.dispatch(
      StateLibNotificationSystemPageActions.changeCurrentTab({
        tab:
          index === 1
            ? NotificationSystemTab.UNREAD
            : NotificationSystemTab.ALL,
      }),
    );
  }
}
