<div
  class="tw-flex tw-items-center tw-justify-between tw-py-size-spacing-12 tw-px-size-spacing-16"
>
  <h2
    class="tw-font-semibold tw-text-[1.6rem] tw-leading-[2.4rem]"
    i18n="@@notificationSystem.header.title"
  >
    Benachrichtigungen
  </h2>
  <div>
    <button
      fin-button-action
      [finActionMenuTrigger]="settingsActionMenu.panel"
      [size]="size.L"
    >
      <fin-icon name="more_vert"></fin-icon>
    </button>
    <fin-actions-menu #settingsActionMenu="finActionMenu">
      <button
        fin-menu-item
        iconName="done_all"
        [size]="size.M"
        (click)="markAllAsRead()"
      >
        <ng-container
          finMenuItemTitle
          i18n="@@notificationSystem.header.actions.markAllAsRead"
          >Alle als gelesen markieren</ng-container
        >
      </button>
      <button
        fin-menu-item
        iconName="settings"
        [size]="size.M"
        (click)="goToSettings()"
      >
        <ng-container
          finMenuItemTitle
          i18n="@@notificationSystem.header.actions.settings"
          >Einstellungen</ng-container
        >
      </button>
    </fin-actions-menu>
  </div>
</div>
<fin-tabs
  #finTabs
  [type]="tabType.SECONDARY_COMPACT"
  [size]="size.XL"
  (selectedTabChange)="tabChange($event.index)"
>
  <fin-tab>
    <ng-template finTabLabel>
      <div class="tw-flex">
        <span
          class="tw-me-[0.6rem]"
          i18n="@@notificationSystem.header.filters.all"
          >Alle</span
        >
      </div>
    </ng-template>
    <ng-template finTabBody>
      <ng-container *ngTemplateOutlet="tabsBody"></ng-container>
    </ng-template>
  </fin-tab>
  <fin-tab>
    <ng-template finTabLabel>
      <div class="tw-flex">
        <span
          class="tw-me-[0.6rem]"
          i18n="@@notificationSystem.header.filters.unread"
          >Ungelesen</span
        >
      </div>
    </ng-template>
    <ng-template finTabBody>
      <ng-container *ngTemplateOutlet="tabsBody"></ng-container>
    </ng-template>
  </fin-tab>
</fin-tabs>

<ng-template #tabsBody>
  @if (notifications$ | async; as notifications) {
    <div class="tw-p-size-spacing-12 tw-pe-size-spacing-6">
      @if (notificationPanelData$ | async; as panelData) {
        @if (panelData.isEmptyContent && panelData.isLoading) {
          <ng-container *ngTemplateOutlet="loader"></ng-container>
        } @else if (
          !panelData.isLoading &&
          !panelData.isEmptyContent &&
          notifications.length === 0
        ) {
          @if (panelData.currentTab === notificationSystemTab.ALL) {
            <ng-container
              *ngTemplateOutlet="
                emptyTemplate;
                context: { text: allTabEmpty, isFullHeight: true }
              "
            ></ng-container>
          } @else {
            <ng-container
              *ngTemplateOutlet="
                emptyTemplate;
                context: { text: unreadTabEmpty, isFullHeight: true }
              "
            ></ng-container>
          }
        } @else {
          <ng-container
            [ngTemplateOutlet]="notificationList"
            [ngTemplateOutletContext]="{ notifications, panelData }"
          ></ng-container>
        }
      }
    </div>
  }
</ng-template>

<ng-template
  #notificationList
  let-notifications="notifications"
  let-panelData="panelData"
>
  <div class="tw-h-[35.2rem]">
    <fin-scrollbar
      (reachedBottom)="loadMoreTodos(panelData.isLastPage)"
      [enableInfinityScroll]="true"
      [reachedBottomOffset]="150"
    >
      <div class="tw-me-size-spacing-6">
        @for (notification of notifications; track notification.id) {
          <app-notification-system-notification
            [notification]="notification"
          ></app-notification-system-notification>
        }
        @if (
          panelData.isLoading &&
          !panelData.isEmptyContent &&
          notifications.length > 0
        ) {
          <div class="tw-flex tw-justify-center tw-py-size-spacing-8">
            <fin-loader></fin-loader>
          </div>
        }
        @if (
          panelData.isLastPage &&
          panelData.currentTab === notificationSystemTab.ALL
        ) {
          <ng-container
            *ngTemplateOutlet="
              emptyTemplate;
              context: { text: allTabEmpty, isFullHeight: false }
            "
          ></ng-container>
        }
      </div>
    </fin-scrollbar>
  </div>
</ng-template>

<ng-template #loader>
  <div
    class="tw-flex tw-justify-center tw-items-center tw-ps-[6.4rem] tw-pe-[7.2rem] tw-pt-[16rem] tw-text-color-text-disabled"
  >
    <fin-loader></fin-loader>
  </div>
</ng-template>

<ng-template #emptyTemplate let-text="text" let-isFullHeight="isFullHeight">
  <div
    class="tw-flex tw-flex-col tw-justify-center tw-items-center"
    [ngClass]="{
      'tw-h-[35.2rem]': isFullHeight,
      'tw-my-size-spacing-24': !isFullHeight,
    }"
  >
    <fin-icon
      src="/assets/svg/svgNotificationSystemEmptyState.svg"
      class="tw-mb-size-spacing-16 tw-text-[7.4rem]"
    ></fin-icon>
    <div
      class="tw-text-text-body-1-size tw-text-color-brand-dark tw-font-text-body-1-strong-weight"
    >
      {{ text }}
    </div>
  </div>
</ng-template>
