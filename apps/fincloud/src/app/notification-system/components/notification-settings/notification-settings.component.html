<div
  class="tw-flex tw-items-center tw-py-size-spacing-12 tw-px-size-spacing-16"
>
  <button fin-button-action [size]="size.L" (click)="backButton()">
    <fin-icon name="keyboard_arrow_left"></fin-icon>
  </button>
  <h2
    class="tw-ms-size-spacing-6 tw-font-semibold tw-text-[1.6rem] tw-leading-[2.4rem]"
    i18n="@@notificationSystem.settings.title"
  >
    Einstellungen
  </h2>
</div>
<hr finHorizontalSeparator />
<div
  class="tw-h-[43rem] tw-ps-size-spacing-12 tw-pe-size-spacing-6 tw-py-size-spacing-12"
>
  @if (isSettingsLoading$ | async) {
    <div class="tw-flex tw-justify-center tw-items-center tw-h-full">
      <fin-loader></fin-loader>
    </div>
  } @else {
    <fin-scrollbar>
      <div class="tw-pe-size-spacing-6" [formGroup]="settingsForm">
        @for (setting of settings$ | async; track setting.key) {
          <div
            class="tw-flex tw-flex-col tw-text-text-body-1-size tw-text-color-text-primary tw-bg-color-surface-secondary tw-mb-size-spacing-8 tw-rounded-size-corner-radius-s tw-p-size-spacing-12"
          >
            <fin-slide-toggle
              [formControlName]="setting.key"
              [label]="setting.label"
              [size]="size.M"
              (slideChange)="slideChange($event, setting.key)"
              class="fin-block"
            ></fin-slide-toggle>
          </div>
        }
      </div>
    </fin-scrollbar>
  }
</div>
