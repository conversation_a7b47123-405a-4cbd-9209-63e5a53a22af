import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { NonNullableFormBuilder } from '@angular/forms';
import {
  StateLibNotificationSystemPageActions,
  selectIsSettingsLoading,
  selectSettingsData,
} from '@fincloud/state/notifications';
import {
  NotificationSystemView,
  NotificationType,
} from '@fincloud/types/enums';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { tap } from 'rxjs';

@Component({
  selector: 'app-notification-settings',
  templateUrl: './notification-settings.component.html',
  styleUrls: ['./notification-settings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotificationSettingsComponent implements OnInit {
  readonly size = FinSize;

  settings$ = this.store.select(selectSettingsData).pipe(
    tap((settings) => {
      settings.forEach(({ key, value }) => {
        this.settingsForm.addControl(key, this.fb.control(value));
      });
    }),
  );

  isSettingsLoading$ = this.store.select(selectIsSettingsLoading);

  settingsForm = this.fb.group({});

  constructor(
    private store: Store,
    private fb: NonNullableFormBuilder,
  ) {}

  ngOnInit(): void {
    this.store.dispatch(StateLibNotificationSystemPageActions.getSettings());
  }

  slideChange(event: boolean, key: NotificationType) {
    console.log(event, key);
  }

  backButton() {
    this.store.dispatch(
      StateLibNotificationSystemPageActions.changeCurrentView({
        view: NotificationSystemView.NOTIFICATIONS,
      }),
    );
  }
}
