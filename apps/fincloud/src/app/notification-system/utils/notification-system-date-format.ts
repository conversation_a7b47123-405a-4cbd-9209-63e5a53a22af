export const NOTIFICATION_SYSTEM_DATE_FORMAT = {
  past: '%s',
  s: 'Now',
  m: '1 minute ago',
  mm: '%d minutes ago',
  h: '1 hour ago',
  hh: '%d hours ago',
  d: '1 day ago',
  dd: (number: any, withoutSuffix: any, key: any, isFuture: any) => {
    const weekes = Math.floor(number / 7);

    switch (weekes) {
      case 0:
        return `${number} days ago`;
      case 1:
        return '1 week ago';
      default:
        return `${weekes} weeks ago`;
    }
  },
  M: '4 weeks ago',
  MM: '4 weeks ago',
  y: '4 weeks ago',
  yy: '4 weeks ago',
};
