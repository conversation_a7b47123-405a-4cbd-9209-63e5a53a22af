import { Injectable } from '@angular/core';
import { TemplateControllerService } from '@fincloud/swagger-generator/business-case-manager';
import { CadrTemplateControllerService } from '@fincloud/swagger-generator/company';
import { TemplateErrorCode } from '@fincloud/types/enums';
import { FinToastService, FinToastType } from '@fincloud/ui/toast';
import { templateToastError } from '@fincloud/utils';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, exhaustMap, map, of, tap } from 'rxjs';
import { MESSAGES_TRANSLATION } from '../../utils/messages-translation';
import {
  BusinessCaseTemplateManagementApiActions,
  BusinessCaseTemplateManagementPageActions,
} from '../actions';

@Injectable()
export class BusinessCaseTemplateManagementEffects {
  constructor(
    private templateService: TemplateControllerService,
    private cadrTemplateService: CadrTemplateControllerService,
    private actions$: Actions,
    private finToastService: FinToastService,
  ) {}

  fetchInitialBusinessCaseTemplates$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        BusinessCaseTemplateManagementPageActions.fetchInitialTemplates,
        BusinessCaseTemplateManagementApiActions.businessCaseTemplateEditSuccess,
        BusinessCaseTemplateManagementApiActions.businessCaseTemplateCreateSuccess,
      ),
      exhaustMap(() =>
        this.templateService.getAllTemplates().pipe(
          map((businessCaseTemplateResponse) => {
            return BusinessCaseTemplateManagementApiActions.fetchInitialBusinessCaseTemplatesSuccess(
              {
                businessCaseTemplateResponse,
              },
            );
          }),
          catchError((error) => {
            return of(
              BusinessCaseTemplateManagementApiActions.fetchInitialBusinessCaseTemplatesFailure(
                {
                  error,
                },
              ),
            );
          }),
        ),
      ),
    ),
  );

  initiateBusinessCaseTemplateEdit$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        BusinessCaseTemplateManagementPageActions.initiateBusinessCaseTemplateEdit,
      ),
      exhaustMap((request) =>
        this.templateService
          .updateTemplate({
            templateId: request.templateId,
            body: request.body,
          })
          .pipe(
            map((template) => {
              return BusinessCaseTemplateManagementApiActions.businessCaseTemplateEditSuccess(
                {
                  template: template.updatedTemplate,
                  isCadrSelected: false,
                  isNewTemplateMode: false,
                },
              );
            }),
            catchError((error) => {
              return of(
                BusinessCaseTemplateManagementApiActions.businessCaseTemplateEditFailure(
                  {
                    error,
                  },
                ),
              );
            }),
          ),
      ),
    ),
  );

  businessCaseTemplateEditSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          BusinessCaseTemplateManagementApiActions.businessCaseTemplateEditSuccess,
        ),
        tap((editedTemplate) => {
          this.finToastService.show({
            type: FinToastType.SUCCESS,
            message: `${MESSAGES_TRANSLATION.globalTemplateTranslation} '${editedTemplate.template.name}' ${MESSAGES_TRANSLATION.toastSuccessUpdateMessageSuffix}`,
          });
        }),
      ),
    { dispatch: false },
  );

  businessCaseTemplateEditFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          BusinessCaseTemplateManagementApiActions.businessCaseTemplateEditFailure,
        ),
        tap((editedTemplateError) => {
          const errorMessage = (
            editedTemplateError.error?.error as { message: string }
          )?.message;
          const errorCode: TemplateErrorCode =
            editedTemplateError.error?.error?.code;
          if (Object.values(TemplateErrorCode).includes(errorCode)) {
            this.finToastService.show({
              type: FinToastType.ERROR,
              message: templateToastError(errorMessage)[errorCode],
            });
          } else {
            this.finToastService.show({
              type: FinToastType.ERROR,
              message: `${MESSAGES_TRANSLATION.toastErrorMessage} ${errorMessage}`,
            });
          }
        }),
      ),
    { dispatch: false },
  );

  initiateBusinessCaseTemplateCreate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        BusinessCaseTemplateManagementPageActions.initiateBusinessCaseTemplateCreate,
      ),
      exhaustMap((request) =>
        this.templateService
          .createTemplate({
            body: request.body,
          })
          .pipe(
            map((template) => {
              return BusinessCaseTemplateManagementApiActions.businessCaseTemplateCreateSuccess(
                {
                  template,
                  isNewTemplateMode: false,
                  isCadrSelected: false,
                },
              );
            }),
            catchError((error) => {
              return of(
                BusinessCaseTemplateManagementApiActions.businessCaseTemplateCreateFailure(
                  {
                    error,
                  },
                ),
              );
            }),
          ),
      ),
    ),
  );

  businessCaseTemplateCreateSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          BusinessCaseTemplateManagementApiActions.businessCaseTemplateCreateSuccess,
        ),
        tap((newBusinessCase) => {
          this.finToastService.show({
            type: FinToastType.SUCCESS,
            message: `${MESSAGES_TRANSLATION.globalTemplateTranslation} '${newBusinessCase.template.name}' ${MESSAGES_TRANSLATION.toastSuccessMessageSuffix}`,
          });
        }),
      ),
    { dispatch: false },
  );

  businessCaseTemplateCreateFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          BusinessCaseTemplateManagementApiActions.businessCaseTemplateCreateFailure,
        ),
        tap((newBusinessCaseError) => {
          const errorCode: TemplateErrorCode =
            newBusinessCaseError.error?.error?.code;
          const errorMessage = newBusinessCaseError.error?.error?.message;
          if (Object.values(TemplateErrorCode).includes(errorCode)) {
            this.finToastService.show({
              type: FinToastType.ERROR,
              message: templateToastError(errorMessage)[errorCode],
            });
          } else {
            this.finToastService.show({
              type: FinToastType.ERROR,
              message: ` ${MESSAGES_TRANSLATION.toastErrorMessageNewTemplate} ${errorMessage}`,
            });
          }
        }),
      ),
    { dispatch: false },
  );

  fetchInitialCadrTemplates$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        BusinessCaseTemplateManagementPageActions.fetchInitialTemplates,
        BusinessCaseTemplateManagementApiActions.cadrTemplateEditSuccess,
      ),
      exhaustMap(() =>
        this.cadrTemplateService.getTemplate().pipe(
          map((cadrTemplateResponse) => {
            return BusinessCaseTemplateManagementApiActions.fetchInitialCadrTemplatesSuccess(
              {
                cadrTemplateResponse,
              },
            );
          }),
          catchError((error) => {
            return of(
              BusinessCaseTemplateManagementApiActions.fetchInitialCadrTemplatesFailure(
                {
                  error,
                },
              ),
            );
          }),
        ),
      ),
    ),
  );

  initiateCadrTemplateEdit$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        BusinessCaseTemplateManagementPageActions.initiateCadrTemplateEdit,
      ),
      exhaustMap((request) =>
        this.cadrTemplateService
          .editTemplate({
            body: request.body,
          })
          .pipe(
            map((template) => {
              return BusinessCaseTemplateManagementApiActions.cadrTemplateEditSuccess(
                {
                  template,
                  isCadrSelected: true,
                  isNewTemplateMode: false,
                },
              );
            }),
            catchError((error) => {
              return of(
                BusinessCaseTemplateManagementApiActions.cadrTemplateEditFailure(
                  {
                    error,
                  },
                ),
              );
            }),
          ),
      ),
    ),
  );

  cadrTemplateEditSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          BusinessCaseTemplateManagementApiActions.cadrTemplateEditSuccess,
        ),
        tap((editedCadr) => {
          this.finToastService.show({
            type: FinToastType.SUCCESS,
            message: `${MESSAGES_TRANSLATION.globalTemplateTranslation} '${editedCadr.template.versionDescription}' ${MESSAGES_TRANSLATION.toastSuccessUpdateMessageSuffix}`,
          });
        }),
      ),
    { dispatch: false },
  );

  initiateCadrTemplateCreate$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        BusinessCaseTemplateManagementPageActions.initiateCadrTemplateCreate,
      ),
      exhaustMap((request) =>
        this.cadrTemplateService
          .createTemplate({
            body: request.body,
          })
          .pipe(
            map((template) => {
              return BusinessCaseTemplateManagementApiActions.cadrTemplateCreateSuccess(
                {
                  template,
                  isNewTemplateMode: false,
                  isCadrSelected: true,
                },
              );
            }),
            catchError((error) => {
              return of(
                BusinessCaseTemplateManagementApiActions.cadrTemplateCreateFailure(
                  {
                    error,
                  },
                ),
              );
            }),
          ),
      ),
    ),
  );

  createNewCadrTemplateSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          BusinessCaseTemplateManagementApiActions.cadrTemplateCreateSuccess,
        ),
        tap(() => {
          const cadrPlaceHolder = 'CADR';
          this.finToastService.show({
            type: FinToastType.SUCCESS,
            message: `${MESSAGES_TRANSLATION.globalTemplateTranslation} ${cadrPlaceHolder} ${MESSAGES_TRANSLATION.toastSuccessMessageSuffix}`,
          });
        }),
      ),
    { dispatch: false },
  );

  createNewCadrTemplateFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          BusinessCaseTemplateManagementApiActions.cadrTemplateCreateFailure,
          BusinessCaseTemplateManagementApiActions.cadrTemplateEditFailure,
        ),
        tap((cadrTemplateError) => {
          const errorCode: TemplateErrorCode =
            cadrTemplateError.error?.error?.code;
          const errorMessage = cadrTemplateError.error?.error?.message;
          if (Object.values(TemplateErrorCode).includes(errorCode)) {
            this.finToastService.show({
              type: FinToastType.ERROR,
              message: templateToastError(errorMessage)[errorCode],
            });
          } else {
            this.finToastService.show({
              type: FinToastType.ERROR,
              message: $localize`:@@templateEditor.toast.error.message.ifNoErrorDisplayed2:Vorlage konnte nicht angelegt werden. Bitte überprüfen Sie Ihre Vorlage.`,
            });
          }
        }),
      ),
    { dispatch: false },
  );
}
