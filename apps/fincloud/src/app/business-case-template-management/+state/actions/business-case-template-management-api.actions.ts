import { HttpErrorResponse } from '@angular/common/http';
import {
  Template,
  TemplateDto,
} from '@fincloud/swagger-generator/business-case-manager';
import { CadrTemplate } from '@fincloud/swagger-generator/company';
import { createAction, props } from '@ngrx/store';

export const fetchInitialBusinessCaseTemplatesSuccess = createAction(
  '[Business Case Template Management - API] Fetch Business Case Templates Success',
  props<{
    businessCaseTemplateResponse: Template[];
  }>(),
);

export const fetchInitialBusinessCaseTemplatesFailure = createAction(
  '[Business Case Template Management - API] Fetch Business Case Templates Failure',
  props<{ error: string }>(),
);

export const businessCaseTemplateEditSuccess = createAction(
  '[Business Case Template Management - API]  Business Case Templates Edit Success',
  props<{
    template: TemplateDto;
    isCadrSelected: boolean;
    isNewTemplateMode: boolean;
  }>(),
);

export const businessCaseTemplateEditFailure = createAction(
  '[Business Case Template Management - API]  Business Case Templates Edit Failure',
  props<{ error: any }>(),
);

export const businessCaseTemplateCreateSuccess = createAction(
  '[Business Case Template Management - API]  Business Case Templates Create Success',
  props<{
    template: TemplateDto;
    isNewTemplateMode: boolean;
    isCadrSelected: boolean;
  }>(),
);

export const businessCaseTemplateCreateFailure = createAction(
  '[Business Case Template Management - API]  Business Case Templates Create Failure',
  props<{ error: any }>(),
);

export const fetchInitialCadrTemplatesSuccess = createAction(
  '[Cadr Template Management - API] Fetch Cadr Templates Success',
  props<{
    cadrTemplateResponse: CadrTemplate;
  }>(),
);

export const fetchInitialCadrTemplatesFailure = createAction(
  '[Cadr Template Management - API] Fetch Cadr Templates Failure',
  props<{ error: string }>(),
);

export const cadrTemplateEditSuccess = createAction(
  '[Cadr Template Management - API]  Cadr Template Edit Success',
  props<{
    template: TemplateDto;
    isCadrSelected: boolean;
    isNewTemplateMode: boolean;
  }>(),
);

export const cadrTemplateEditFailure = createAction(
  '[Cadr Template Management - API]  Cadr Template Edit Failure',
  props<{ error: HttpErrorResponse }>(),
);

export const cadrTemplateCreateSuccess = createAction(
  '[Business Case Template Management - API]  Cadr Templates Create Success',
  props<{
    template: CadrTemplate;
    isCadrSelected: boolean;
    isNewTemplateMode: boolean;
  }>(),
);

export const cadrTemplateCreateFailure = createAction(
  '[Cadr Template Management - API]  Cadr Template Create Failure',
  props<{ error: HttpErrorResponse }>(),
);
