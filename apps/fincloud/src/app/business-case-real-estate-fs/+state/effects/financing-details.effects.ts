import { Injectable } from '@angular/core';
import { Toast } from '@fincloud/core/toast';
import {
  StateLibBusinessCasePageActions,
  selectBusinessCase,
  selectBusinessCaseId,
  selectIsParticipationTypeCollaborator,
} from '@fincloud/state/business-case';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import {
  catchError,
  debounceTime,
  exhaustMap,
  filter,
  map,
  mergeMap,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs/operators';

import { ModalService } from '@fincloud/core/modal';
import { selectIsBusinessCaseRealEstate } from '@fincloud/state/business-case';
import {
  StateLibBusinessCaseRealEstateApiActions,
  StateLibBusinessCaseRealEstatePageActions,
  StateLibFinancingStructureApiActions,
  StateLibScrollToSectionPageActions,
} from '@fincloud/state/business-case-real-estate';
import { selectCustomerKey } from '@fincloud/state/customer';
import { StateLibNoopPageActions } from '@fincloud/state/utils';
import { NorthDataControllerService } from '@fincloud/swagger-generator/company';
import {
  FinStructureControllerService,
  FinStructureSharingControllerService,
  FinancingStructure,
} from '@fincloud/swagger-generator/financing-details';
import { FinModalService } from '@fincloud/ui/modal';
import { FinToastService } from '@fincloud/ui/toast';
import { concatLatestFrom } from '@ngrx/operators';
import { TypedAction } from '@ngrx/store/src/models';
import { createDynamicFieldsetBodyRequest } from '../../utils/create-dynamic-fieldset-body-request';
import { FINANCING_PARTNERS } from '../../utils/financing-partners-field-key';
import { RefsApiActions, RefsPageActions } from '../actions';
import {
  selectActiveBuildingBlockName,
  selectCatalogueDataDynamicBlocks,
  selectSharedWithMeByCustomer,
} from '../selectors';

@Injectable()
export class FinancingDetailsEffects {
  updateRefsFieldDescription$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(RefsPageActions.updateRefsFieldDescription),
      concatLatestFrom(() => [this.store.select(selectBusinessCase)]),
      exhaustMap(([action, businessCase]) => {
        return this.finStructureControllerService
          .addDescriptionToFinStructureField({
            businessCaseId: businessCase.id,
            body: {
              description: action.payload?.description,
              fieldId: action.payload.fieldId,
              fieldsetName: action.payload?.fieldsetName,
              group: action.payload?.group,
              dynamic: action.payload?.dynamic,
            },
          })
          .pipe(
            map((financingStructure) =>
              StateLibBusinessCaseRealEstateApiActions.updateRefsFieldDescriptionSuccess(
                {
                  financingStructure,
                },
              ),
            ),
            catchError(() =>
              of(
                StateLibBusinessCaseRealEstateApiActions.updateRefsFieldDescriptionFailure(),
              ),
            ),
          );
      }),
    );
  });

  notificationOnDescriptionUpdate$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(
          StateLibBusinessCaseRealEstateApiActions.updateRefsFieldDescriptionSuccess,
        ),
        tap(() => {
          this.finToastService.show(
            Toast.success(
              $localize`:@@editFinStructFieldErrorMsg: Beschreibung konnte nicht festgelegt werden`,
            ),
          );
          this.modalService.closeActiveModals();
        }),
      );
    },
    { dispatch: false },
  );

  updateDynamicGroupToggleState$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(StateLibBusinessCaseRealEstatePageActions.enableOrDisableGroup),
      switchMap((action) => {
        return this.finStructureControllerService
          .enableOrDisableFinStructureGroup({
            businessCaseId: action.businessCaseId,
            body: action.body,
          })
          .pipe(
            switchMap((financingStructure) => {
              this.finToastService.show(Toast.success());

              const outputActions: TypedAction<string>[] = [
                StateLibFinancingStructureApiActions.loadFinancingStructureSuccess(
                  {
                    financingStructure,
                  },
                ),
              ];

              let payload: {
                firstId: string;
                secondId: string;
                thirdId: string;
                highlightId?: string;
              } = {
                firstId: null,
                secondId: null,
                thirdId: null,
                highlightId: '',
              };

              if (!action.body.disabled) {
                payload = {
                  firstId: action.parentGroupId,
                  secondId: action.fieldId,
                  thirdId: null,
                  highlightId: '',
                };
              }

              if (!action.body.disabled || action.areAllSubgroupsDisabled) {
                outputActions.push(
                  StateLibScrollToSectionPageActions.scrollToSection({
                    payload,
                  }),
                );
              }

              return outputActions;
            }),
            catchError(() => {
              this.finToastService.show(Toast.error());

              return of(StateLibNoopPageActions.noop);
            }),
          );
      }),
    );
  });

  loadMainFinStructureCommonFields$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        StateLibBusinessCaseRealEstatePageActions.loadMainRefsCommonFields,
      ),
      switchMap((action) =>
        this.finStructureControllerService
          .getLeadFinancingStructureCommonFields1({
            businessCaseId: action.payload.businessCaseId,
          })
          .pipe(
            map((fields) =>
              RefsApiActions.loadMainRefsCommonFieldsSuccess({
                payload: fields,
              }),
            ),
            catchError((err) =>
              of(RefsApiActions.loadMainRefsCommonFieldsFailure({ err })),
            ),
          ),
      ),
    ),
  );

  loadCatalogData$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(RefsPageActions.loadRefs),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCaseId),
        this.store.select(selectIsBusinessCaseRealEstate),
        this.store.select(selectIsParticipationTypeCollaborator),
      ]),
      filter(
        ([, , isRealEstate, isCollaborator]) => isRealEstate && !isCollaborator,
      ),
      switchMap(([, businessCaseId]) => {
        return this.finStructureControllerService
          .getAllCatalogueData({ businessCaseId })
          .pipe(
            map((catalogueData) =>
              RefsApiActions.loadCatalogueDataSuccess({
                catalogueData,
              }),
            ),
            catchError((err) =>
              of(RefsApiActions.loadCatalogueDataFailure({ err })),
            ),
          );
      }),
    );
  });

  restoreFinStructureFieldRevision = createEffect(() => {
    return this.actions$.pipe(
      ofType(RefsPageActions.restoreRevision),
      switchMap(
        (action: {
          payload: {
            finStructureId: string;
            revisionId: string;
            newValueForPurposeOfUse?: string;
            businessCaseId?: string;
          };
        }) => {
          return this.finStructureControllerService
            .restoreRevisionInFinStructure({
              finStructureId: action.payload?.finStructureId,
              revisionId: action.payload?.revisionId,
              businessCaseId: action.payload?.businessCaseId,
            })
            .pipe(
              map((financingStructure: FinancingStructure) => {
                if (action.payload.newValueForPurposeOfUse) {
                  return RefsPageActions.renameOnlyFieldsetName({
                    payload: action.payload.newValueForPurposeOfUse,
                  });
                }
                return StateLibBusinessCaseRealEstateApiActions.restoreFinStructureFieldRevisionSuccess(
                  {
                    financingStructure,
                  },
                );
              }),
              catchError((error) =>
                of(
                  StateLibBusinessCaseRealEstateApiActions.restoreFinStructureFieldRevisionFailure(
                    {
                      error,
                    },
                  ),
                ),
              ),
            );
        },
      ),
    );
  });

  loadFinancingStructureSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          StateLibBusinessCaseRealEstateApiActions.restoreFinStructureFieldRevisionSuccess,
        ),

        tap(() => {
          this.finToastService.show(Toast.success());
        }),
      ),
    { dispatch: false },
  );

  restoreFinStructureFieldRevisionFailiure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          StateLibBusinessCaseRealEstateApiActions.restoreFinStructureFieldRevisionFailure,
        ),

        tap(() => {
          this.finToastService.show(Toast.error());
        }),
      ),
    { dispatch: false },
  );

  renameOnlyFieldsetName$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(RefsPageActions.renameOnlyFieldsetName),
      filter((x) => !!x.payload),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCaseId),
        this.store.select(selectActiveBuildingBlockName),
      ]),
      switchMap(([_action, businessCaseId, activeDfsName]) => {
        return of(
          RefsPageActions.renameDynamicFieldSet({
            payload: {
              businessCaseId,
              fieldsetName: activeDfsName,
              newFieldsetName: _action.payload,
            },
          }),
        );
      }),
    );
  });

  addDynamicFieldsetToFinStructure$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibBusinessCaseRealEstatePageActions.addDynamicFieldSetToFinStructure,
      ),
      concatLatestFrom(() => [
        this.store.select(selectCatalogueDataDynamicBlocks),
        this.store.select(selectBusinessCaseId),
      ]),
      mergeMap(([{ payload }, dynamicBlocksGroup, businessCaseId]) => {
        return this.finStructureControllerService
          .addDynamicFieldsetToFinStructure({
            businessCaseId,
            body: createDynamicFieldsetBodyRequest(payload, dynamicBlocksGroup),
          })
          .pipe(
            map((response) => {
              const newlyCreatedBlock = response.dynamicFieldsets?.find(
                (fieldset) =>
                  fieldset.group === payload.groupName &&
                  fieldset.fieldsetName === payload.fieldsetName,
              );
              return StateLibBusinessCaseRealEstateApiActions.addDynamycFieldsetToFinancingStructureSuccess(
                {
                  financingStructure: response,
                  blockId: newlyCreatedBlock?.id,
                  blockName: payload.fieldsetName,
                },
              );
            }),
            catchError((error) =>
              of(
                StateLibBusinessCaseRealEstateApiActions.addDynamycFieldsetToFinancingStructureFailure(
                  {
                    error,
                  },
                ),
              ),
            ),
          );
      }),
    );
  });

  addDynamicFieldSetToFinStructureSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          StateLibBusinessCaseRealEstateApiActions.addDynamycFieldsetToFinancingStructureSuccess,
        ),
        tap(() =>
          this.finToastService.show(
            Toast.success(
              $localize`:@@financingPartners.addDynamicFieldSetToFinStructureSuccess: Finanzierungsbaustein erfolgreich hinzugefügt`,
            ),
          ),
        ),
      ),
    { dispatch: false },
  );

  addDynamicFieldSetToFinStructureFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          StateLibBusinessCaseRealEstateApiActions.addDynamycFieldsetToFinancingStructureFailure,
        ),
        tap(() =>
          this.finToastService.show(
            Toast.error(
              $localize`:@@financingPartners.addDynamicFieldSetToFinStructureFailure: Finanzierungsbaustein konnte nicht hinzugefügt werden`,
            ),
          ),
        ),
      ),
    { dispatch: false },
  );

  addValueToFinStructureMultiselectField$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RefsPageActions.addValueToFinStructureMultiselectField),
      concatLatestFrom(() => this.store.select(selectBusinessCaseId)),
      switchMap(([{ payload }, businessCaseId]) =>
        this.finStructureControllerService
          .addValueToFinStructureField1({ ...payload, businessCaseId })
          .pipe(
            map((financingStructure) =>
              StateLibBusinessCaseRealEstateApiActions.addValueToFinStructureFieldSuccess(
                { financingStructure },
              ),
            ),
            catchError((err) =>
              of(
                StateLibBusinessCaseRealEstateApiActions.addValueToFinStructureFieldFailure(
                  err,
                ),
              ),
            ),
          ),
      ),
    ),
  );

  addValueToFinStructureField$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(RefsPageActions.addValueToFinStructureField),
      mergeMap(({ payload }) =>
        this.finStructureControllerService
          .addValueToFinStructureField1(payload)
          .pipe(
            switchMap((financingStructure) => {
              const { isSynced, skipToast } = payload;
              const responseActions: TypedAction<string>[] = [
                StateLibBusinessCaseRealEstateApiActions.addValueToFinStructureFieldSuccess(
                  {
                    financingStructure,
                    skipToast,
                    fieldKey: payload.fieldKey,
                  },
                ),
              ];
              if (isSynced) {
                responseActions.push(
                  StateLibBusinessCasePageActions.unsyncFieldInDataRoom({
                    fieldKey: payload.fieldKey,
                  }),
                );
              }

              return responseActions;
            }),
            catchError((err) =>
              of(
                StateLibBusinessCaseRealEstateApiActions.addValueToFinStructureFieldFailure(
                  {
                    err,
                    fieldKey: payload.fieldKey,
                  },
                ),
              ),
            ),
          ),
      ),
    );
  });

  addValueToFinStructureFieldSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          StateLibBusinessCaseRealEstateApiActions.addValueToFinStructureFieldSuccess,
        ),
        filter(
          ({ skipToast, fieldKey }) =>
            !skipToast && fieldKey !== FINANCING_PARTNERS,
        ),
        tap(() => {
          this.finToastService.show(Toast.success());
        }),
      ),
    { dispatch: false },
  );

  addValueToFinStructureFieldFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          StateLibBusinessCaseRealEstateApiActions.addValueToFinStructureFieldFailure,
        ),
        filter(({ fieldKey }) => fieldKey !== FINANCING_PARTNERS),
        tap(() => {
          this.finToastService.show(Toast.error());
        }),
      ),
    { dispatch: false },
  );

  addValueToFinancingPartnerFieldSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          StateLibBusinessCaseRealEstateApiActions.addValueToFinStructureFieldSuccess,
        ),
        filter(
          ({ skipToast, fieldKey }) =>
            !skipToast && fieldKey === FINANCING_PARTNERS,
        ),
        tap(() => {
          this.finToastService.show(
            Toast.success(
              $localize`:@@financingPartners.updateSuccess: Daten des Finanzierungspartners erfolgreich aktualisiert`,
            ),
          );
          this.modalService.closeActiveModals();
        }),
      ),
    { dispatch: false },
  );

  addValueToFinancingPartnerFieldFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          StateLibBusinessCaseRealEstateApiActions.addValueToFinStructureFieldFailure,
        ),
        filter(({ fieldKey }) => fieldKey === FINANCING_PARTNERS),
        tap(() => {
          this.finToastService.show(
            Toast.error(
              $localize`:@@financingPartners.updateFailure: Daten des Finanzierungspartners konnten nicht aktualisiert werden`,
            ),
          );
          this.modalService.closeActiveModals();
        }),
      ),
    { dispatch: false },
  );

  renameDynamicFieldSet$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(RefsPageActions.renameDynamicFieldSet),
      mergeMap(({ payload }) =>
        this.finStructureControllerService
          .renameDynamicFieldsetInFinStructure({
            businessCaseId: payload.businessCaseId,
            fieldsetName: payload.fieldsetName,
            newFieldsetName: payload.newFieldsetName,
          })
          .pipe(
            map((financingStructure: FinancingStructure) => {
              if (payload.fieldId) {
                return RefsPageActions.addValueToFinStructureField({
                  payload: {
                    businessCaseId: payload.businessCaseId,
                    body: {
                      fieldId: payload.fieldId,
                      dynamic: true,
                      value: payload.newFieldsetName,
                      fieldsetName: payload.newFieldsetName,
                    },
                  },
                });
              }

              return StateLibFinancingStructureApiActions.loadFinancingStructureSuccess(
                {
                  financingStructure,
                },
              );
            }),
            catchError((err) => {
              this.finToastService.show(Toast.error());

              return of(RefsApiActions.renameDynamicFieldSetFailure(err));
            }),
          ),
      ),
    );
  });

  removeDynamicFiieldSetFromCase$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(RefsPageActions.removeDynamicFieldSetFromCase),
      concatLatestFrom(() => this.store.select(selectBusinessCaseId)),
      mergeMap(([{ fieldsetName }, businessCaseId]) =>
        this.finStructureControllerService
          .deleteDynamicFieldsetFromFinStructure({
            fieldsetName,
            businessCaseId,
          })
          .pipe(
            map((financingStructure) => {
              this.finToastService.show(Toast.success());
              return StateLibBusinessCaseRealEstateApiActions.removeDynamicFieldSetFromCaseSuccess(
                {
                  financingStructure,
                },
              );
            }),
            catchError((err) => {
              this.finToastService.show(Toast.error());
              return of(
                StateLibBusinessCaseRealEstateApiActions.removeDynamicFieldSetFromCaseFailure(
                  {
                    payload: err,
                  },
                ),
              );
            }),
          ),
      ),
    );
  });

  shareGroupWithParticipant$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibBusinessCaseRealEstatePageActions.shareGroupWithParticipant,
      ),
      concatLatestFrom(() => this.store.select(selectBusinessCaseId)),
      mergeMap(([{ payload }, businessCaseId]) =>
        this.finStructureSharingControllerService
          .shareFinStructureWithParticipant({ ...payload, businessCaseId })
          .pipe(
            map(() =>
              RefsApiActions.shareGroupWithParticipantSuccess({
                payload: {
                  toCustomerKey: payload.toCustomerKey,
                  groupIds: payload.body.groupIds,
                },
              }),
            ),
            catchError((err) =>
              of(
                RefsApiActions.shareGroupWithParticipantFailure({
                  payload: err,
                }),
              ),
            ),
          ),
      ),
    );
  });

  shareGroupWithParticipantSuccess$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(RefsApiActions.shareGroupWithParticipantSuccess),
        tap(() => {
          this.finToastService.show(Toast.success());
          this.finModalService.closeAll();
        }),
      );
    },
    { dispatch: false },
  );

  shareGroupWithParticipantFailure$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(RefsApiActions.shareGroupWithParticipantFailure),
        tap(() => {
          this.finToastService.show(Toast.error());
          this.finModalService.closeAll();
        }),
      );
    },
    { dispatch: false },
  );

  getLatestSharingGroups$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibBusinessCaseRealEstatePageActions.getSharedGroupInformation,
        StateLibBusinessCaseRealEstatePageActions.getLatestSharingGroups,
      ),
      concatLatestFrom(() => this.store.select(selectBusinessCaseId)),
      switchMap(([action, businessCaseId]) =>
        this.finStructureSharingControllerService
          .getMyLatestFinStructureSharingEntities({
            businessCaseId,
          })
          .pipe(
            map((response) =>
              RefsApiActions.getLatestSharingGroupsSuccess({
                payload: response,
              }),
            ),
            catchError((err) => {
              this.finToastService.show(Toast.error());
              return of(
                RefsApiActions.getLatestSharingGroupsFailure({ payload: err }),
              );
            }),
            takeUntil(
              this.actions$.pipe(
                ofType(
                  StateLibBusinessCaseRealEstatePageActions.clearSharedGroupInformation,
                ),
              ),
            ),
          ),
      ),
    );
  });

  getSharingEntitiesWithMe$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        RefsPageActions.loadRefs,
        StateLibBusinessCaseRealEstatePageActions.getSharedEntitiesWithMe,
      ),
      concatLatestFrom(() => [
        this.store.select(selectIsParticipationTypeCollaborator),
        this.store.select(selectBusinessCase),
      ]),
      filter(([action, isCollaborator]) => !isCollaborator),
      switchMap(([action, canLoad, businessCase]) =>
        this.finStructureSharingControllerService
          .getFinStructureSharingEntitiesSharedWithMe({
            businessCaseId: businessCase.id,
          })
          .pipe(
            map((response) =>
              RefsApiActions.getSharedEntitiesWithMeSuccess({
                payload: response,
              }),
            ),
            catchError((err) => {
              this.finToastService.show(Toast.error());
              return of(
                RefsApiActions.getSharedEntitiesWithMeFailure({ payload: err }),
              );
            }),
          ),
      ),
    );
  });

  getSharedFinancingStructure$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibBusinessCaseRealEstatePageActions.getSharedFinancingStructure,
      ),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCaseId),
        this.store.select(selectSharedWithMeByCustomer),
      ]),
      switchMap(([action, businessCaseId, sharedWithMeByCustomer]) => {
        return this.finStructureControllerService
          .getFinStructureBySharingEntityId({
            businessCaseId,
            sharingEntityId:
              sharedWithMeByCustomer[
                action.payload.selectedSharedEntityTabIndex
              ].id,
          })
          .pipe(
            map((response) =>
              RefsApiActions.getSharedFinancingStructureSuccess({
                payload: response,
              }),
            ),
            catchError((err) => {
              this.finToastService.show(Toast.error());
              return of(
                RefsApiActions.getSharedFinancingStructureFailure({
                  payload: err,
                }),
              );
            }),
          );
      }),
    );
  });

  getSharedGroupInformation$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibBusinessCaseRealEstatePageActions.getSharedGroupInformation,
      ),
      concatLatestFrom(() => this.store.select(selectBusinessCaseId)),
      switchMap(([{ payload }, businessCaseId]) =>
        this.finStructureControllerService
          .getFinStructureSharingGroupsInformation({
            ...payload,
            businessCaseId,
          })
          .pipe(
            map((response) =>
              RefsApiActions.getSharedGroupInformationSuccess({
                payload: response,
              }),
            ),
            catchError((err) => {
              this.finToastService.show(Toast.error());
              return of(
                RefsApiActions.getSharedGroupInformationFailure({
                  payload: err,
                }),
              );
            }),
            takeUntil(
              this.actions$.pipe(
                ofType(
                  StateLibBusinessCaseRealEstatePageActions.clearSharedGroupInformation,
                ),
              ),
            ),
          ),
      ),
    );
  });

  loadCompanyFromNorthData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RefsPageActions.getNorthDataCompanies),
      debounceTime(400),
      concatLatestFrom(() => this.store.select(selectCustomerKey)),
      switchMap(([action, customerKey]) => {
        return this.northDataControllerService
          .getCompaniesFromNorthData({ query: action.query, customerKey })
          .pipe(
            map((companies) =>
              RefsApiActions.getNorthDataCompaniesSuccess({
                companies,
              }),
            ),
            catchError((error) =>
              of(
                RefsApiActions.getNorthDataCompaniesFailure({
                  error,
                }),
              ),
            ),
          );
      }),
    ),
  );
  constructor(
    private actions$: Actions,
    private finStructureControllerService: FinStructureControllerService,
    private finStructureSharingControllerService: FinStructureSharingControllerService,
    private finToastService: FinToastService,
    private modalService: ModalService,
    private store: Store,
    private finModalService: FinModalService,
    private northDataControllerService: NorthDataControllerService,
  ) {}
}
