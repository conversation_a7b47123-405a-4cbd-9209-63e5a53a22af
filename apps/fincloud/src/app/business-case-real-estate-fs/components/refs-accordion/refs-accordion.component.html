<fin-accordion
  class="tw-flex tw-flex-col tw-h-full tw-w-full"
  [togglePosition]="togglePosition"
  [size]="finSize.M"
  [multi]="!!matchingToSearchGroupIds.length"
  [hideToggle]="false"
  [toggleDirection]="toggleDirection"
  (shown)="expandSubAccordions()"
  [type]="finAccordionType.LIGHT"
>
  @for (mainGroup of staticGroups; track mainGroup.id; let first = $first) {
    <fin-expansion-panel
      [id]="mainGroup.id"
      #panel
      [expanded]="
        !!matchingToSearchGroupIds.length ||
        (matchingToSearchGroupIds | includes: mainGroup.id)
      "
      [appExpand]="
        mainGroup.id === firstIdToExpand ||
        !!matchingToSearchGroupIds.length ||
        (matchingToSearchGroupIds | includes: mainGroup.id)
      "
      (afterExpand)="onAfterExpand(mainGroup.id)"
      (click)="onClearScrollToSection()"
    >
      <ng-template #finTitle>
        <div class="tw-flex tw-justify-between tw-items-center tw-w-full">
          <p [id]="highlightPrefix + mainGroup.id">
            {{ mainGroup?.group }}
          </p>

          @if (
            isCustomerLead && mainGroup
              | countAllMirroredFieldsForGroup: mirroredFieldKeys;
            as mirroredFieldsCount
          ) {
            <span class="tw-flex tw-gap-x-[0.8rem] tw-mr-[1.6rem]">
              <fin-badge-status
                finTooltip
                iconName="compare_arrows"
                [content]="infoBadgeTooltip"
                [openDelay]="400"
                [type]="finBadgeStatus.DRAFT"
                [text]="mirroredFieldsCount | number"
              ></fin-badge-status>
            </span>
          }
        </div>
      </ng-template>

      @if (panel.isExpanded) {
        <ng-template #finContent>
          @if (mainGroup.key !== refsBuildingBlocks) {
            @if (mainGroup.fields.length) {
              <div
                class="tw-flex tw-flex-wrap tw-justify-between tw-px-[2.4rem] tw-py-[1.2rem] tw-gap-y-[1rem]"
              >
                @for (field of mainGroup?.fields; track field.id) {
                  @if (!field.disabled) {
                    <ng-container
                      [ngTemplateOutlet]="uiRefsField"
                      [ngTemplateOutletContext]="{
                        field: field,
                        group: mainGroup,
                      }"
                    />
                  }
                }
              </div>
            }
            @if (mainGroup.subGroups.length) {
              <fin-accordion
                togglePosition="before"
                [size]="finSize.M"
                [multi]="
                  matchingToSearchGroupIds.length ||
                  (matchingToSearchGroupIds | includes: mainGroup.id)
                "
                [hideToggle]="false"
                [toggleDirection]="toggleDirection"
                borderless
              >
                @for (subGroup of mainGroup.subGroups; track subGroup.id) {
                  <fin-expansion-panel
                    [id]="subGroup.id"
                    #subPanel
                    [expanded]="
                      !!matchingToSearchGroupIds.length ||
                      (matchingToSearchGroupIds | includes: subGroup.id)
                    "
                    [appExpand]="
                      secondIdToExpand === subGroup.id ||
                      !!matchingToSearchGroupIds.length ||
                      (matchingToSearchGroupIds | includes: subGroup.id)
                    "
                    (afterExpand)="onScrollToExpandedGroup(subGroup.id)"
                    (click)="onClearScrollToSection()"
                  >
                    <ng-template #finTitle>
                      <span
                        class="block tw-w-full tw-flex justify-content-between"
                      >
                        <span
                          [id]="highlightPrefix + subGroup.id"
                          class="tw-text-[1.6rem]"
                        >
                          {{ subGroup.group }}
                        </span>
                        @if (
                          isCustomerLead && subGroup
                            | countAllMirroredFieldsForGroup: mirroredFieldKeys;
                          as mirroredFieldsCount
                        ) {
                          <span
                            class="tw-flex tw-gap-x-[0.8rem] tw-mr-[1.6rem]"
                          >
                            <fin-badge-status
                              finTooltip
                              iconName="compare_arrows"
                              [content]="infoBadgeTooltip"
                              [openDelay]="400"
                              [type]="finBadgeStatus.DRAFT"
                              [text]="mirroredFieldsCount | number"
                            ></fin-badge-status>
                          </span>
                        }
                      </span>
                    </ng-template>

                    @if (subPanel.isExpanded) {
                      <ng-template #finContent>
                        <div
                          class="tw-flex tw-flex-wrap tw-justify-between tw-gap-[2.4rem]"
                        >
                          @for (
                            subInnerGroupField of subGroup.fields;
                            track subInnerGroupField.id
                          ) {
                            @if (!subInnerGroupField.disabled) {
                              <ng-container
                                [ngTemplateOutlet]="uiRefsField"
                                [ngTemplateOutletContext]="{
                                  field: subInnerGroupField,
                                  group: subGroup,
                                }"
                              />
                            }
                          }
                        </div>
                        @for (
                          subInnerGroupGroup of subGroup.subGroups;
                          track subInnerGroupGroup.id
                        ) {
                          <div>
                            <div class="tw-mb-[3.2rem]">
                              <fin-card-label
                                [id]="highlightPrefix + subInnerGroupGroup.id"
                                [label]="subInnerGroupGroup?.group"
                              ></fin-card-label>
                            </div>
                            <div
                              class="tw-flex tw-flex-wrap tw-justify-between tw-gap-y-[2.4rem]"
                            >
                              @for (
                                subInnerGroupGroupField of subInnerGroupGroup.fields;
                                track subInnerGroupGroupField.id
                              ) {
                                @if (!subInnerGroupGroupField.disabled) {
                                  <ng-container
                                    [ngTemplateOutlet]="uiRefsField"
                                    [ngTemplateOutletContext]="{
                                      field: subInnerGroupGroupField,
                                      group: subInnerGroupGroup,
                                    }"
                                  />
                                }
                              }
                            </div>
                          </div>
                        }
                      </ng-template>
                    }
                  </fin-expansion-panel>
                }
              </fin-accordion>
            }
          } @else {
            <ng-content></ng-content>
          }
        </ng-template>
      }
    </fin-expansion-panel>
  }
</fin-accordion>

<ng-template #uiRefsField let-field="field" let-group="group">
  <app-refs-field
    [id]="field.id"
    [class.disabled]="field.isReadOnly"
    [businessCase]="businessCase"
    [isEditMode]="!field.isReadOnly && isEditMode"
    [field]="field"
    [isDynamic]="isDynamic"
    [finStructureId]="finStructureId"
    [fieldGroupFieldsetName]="group.fieldsetName"
    [group]="group"
    [hasWarning]="
      activeBuildingBlockAmount === 0 &&
      (field.label | includes: fieldLabelSuffix)
    "
    [mirroredFieldKeys]="mirroredFieldKeys"
    [isCustomerLead]="isCustomerLead"
    [customerType]="customerType"
    [errorText]="errorText"
    [hasError]="
      field.key === dynamicFieldsetKey.INTENDED_USE &&
      (errors | includes: field.id)
    "
    [valueHandling]="
      field.key === dynamicFieldsetKey.INTENDED_USE ? 'emit-event' : 'persist'
    "
    [instantFeedback]="field.key === dynamicFieldsetKey.INTENDED_USE"
    (feedback)="onFeedback($event, field.id)"
    (valueChange)="onValueChange($event, field)"
  ></app-refs-field
></ng-template>
