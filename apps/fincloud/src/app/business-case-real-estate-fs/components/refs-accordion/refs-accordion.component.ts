import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { RefsGroupType } from '@fincloud/components/refs';
import { FinStructureField } from '@fincloud/swagger-generator/business-case-manager';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { CustomerType, FieldType } from '@fincloud/types/enums';
import {
  CustomFinStructureField,
  CustomFinStructureGroup,
} from '@fincloud/types/models';
import { FinBadgeStatus } from '@fincloud/ui/badges';
import {
  FinAccordionComponent,
  FinAccordionToggleDirection,
  FinAccordionTogglePosition,
  FinAccordionType,
} from '@fincloud/ui/expansion-panel';
import { FinSize } from '@fincloud/ui/types';
import { REFS_BLOCKS_GROUP_KEY } from '@fincloud/utils';
import { isEmpty } from 'lodash-es';
import { DynamicFieldsetKey } from '../../enums/dynamic-fieldset-key';
import { FIELD_LABEL_SUFFIX } from '../../utils/field-label-suffix';

@Component({
  selector: 'app-refs-accordion',
  templateUrl: './refs-accordion.component.html',
  styleUrls: ['./refs-accordion.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RefsAccordionComponent {
  @Input() staticGroups: CustomFinStructureGroup[] = [];
  @Input() mirroredFieldKeys: string[] = [];
  @Input() isCustomerLead: boolean;
  @Input() firstIdToExpand = '';
  @Input() secondIdToExpand = '';
  @Input() highlightId = '';
  @Input() closeOthers = true;
  @Input() highlightPrefix = '';
  @Input() matchingToSearchGroupIds: string[] = [];
  @Input() businessCase: ExchangeBusinessCase;
  @Input() finStructureId = '';
  @Input() isEditMode = false;
  @Input() shouldExpandSubAccordions = false;
  @Input() customerType: CustomerType;
  @Input() errorText: string;
  @Input() isDynamic = false;
  @Input() errors: string[] = [];
  @Input() activeBuildingBlockAmount: number;
  @Input() togglePosition: FinAccordionTogglePosition =
    FinAccordionTogglePosition.BEFORE;

  @Output() valueChange = new EventEmitter<{
    newFieldValue: FinStructureField['value'];
    field: CustomFinStructureField;
  }>();

  @Output() feedback = new EventEmitter<{
    text: string;
    fieldId: string;
  }>();

  @Output() scrollToExpandedGroup = new EventEmitter<string>();

  @Output() clearScrollToSection = new EventEmitter<void>();
  refsGroupType = RefsGroupType;
  fieldType = FieldType;
  refsBuildingBlocks = REFS_BLOCKS_GROUP_KEY;
  toggleDirection = FinAccordionToggleDirection.AUTO;
  finSize = FinSize;
  readonly finBadgeStatus = FinBadgeStatus;
  readonly finAccordionType = FinAccordionType;
  readonly dynamicFieldsetKey = DynamicFieldsetKey;
  readonly fieldLabelSuffix = FIELD_LABEL_SUFFIX;

  @ViewChild('subPanel') private subPanel: FinAccordionComponent;

  infoBadgeTooltip = $localize`:@@refs.accordion.infoBadge.tooltip:Mit Datenraum synchronisiert`;

  onAfterExpand(groupId: string) {
    this.onScrollToExpandedGroup(groupId);
  }

  onScrollToExpandedGroup(groupId: string) {
    if (isEmpty(this.matchingToSearchGroupIds) && !this.highlightId) {
      this.scrollToExpandedGroup.emit(groupId);
    }
  }

  expandSubAccordions(): void {
    if (this.shouldExpandSubAccordions) {
      this.subPanel?.openAll();
    }
  }

  onClearScrollToSection() {
    if (this.firstIdToExpand || this.secondIdToExpand || this.highlightId) {
      this.clearScrollToSection.emit();
    }
  }

  onFeedback(text: string, fieldId: string) {
    this.feedback.emit({ text, fieldId });
  }

  onValueChange(
    newFieldValue: FinStructureField,
    field: CustomFinStructureField,
  ) {
    this.valueChange.emit({ newFieldValue, field });
  }
}
