import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  Inject,
  Input,
  LOCALE_ID,
  OnInit,
  ViewChild,
} from '@angular/core';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { Store } from '@ngrx/store';
import { cloneDeep, isString, uniq } from 'lodash-es';
import { Observable, combineLatest, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  DataGridComponent,
  DataGridConfig,
  DataGridViewMode,
} from '@fincloud/components/data-grid';
import { TableColumn, TableComponent } from '@fincloud/components/lists';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { Toast } from '@fincloud/core/toast';
import { RevisionTableRow } from '@fincloud/neoshare/business-case-fields';
import { selectUsersById } from '@fincloud/state/business-case';
import { selectFinancingStructureId } from '@fincloud/state/business-case-real-estate';
import { StateLibUserPageActions } from '@fincloud/state/user';
import { User } from '@fincloud/swagger-generator/authorization-server';
import {
  FinStructureControllerService,
  FinStructureRevision,
} from '@fincloud/swagger-generator/financing-details';
import { Locale } from '@fincloud/types/enums';
import { CustomFinStructureGroup, Dictionary } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { RefsPageActions } from '../../+state/actions';
import { DynamicFieldsetKey } from '../../enums/dynamic-fieldset-key';
import { FinStructureFieldInfo } from '../../models/fin-structure-field-info';
import { refsFieldRevisionsTableColumnsConfig } from '../../utils/refs-field-revisions-table-columns-config';

@Component({
  selector: 'app-refs-field-revisions',
  templateUrl: './refs-field-revisions.component.html',
  styleUrls: ['./refs-field-revisions.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RefsFieldRevisionsComponent implements OnInit {
  @Input() businessCase: ExchangeBusinessCase;
  @Input() field: FinStructureFieldInfo;
  @Input() finStructureId: string;
  @Input() parentGroup: CustomFinStructureGroup;

  @ViewChild('table', { static: true }) table: TableComponent;
  @ViewChild('dataGrid', { static: false }) dataGrid: DataGridComponent;

  get selectedRevisionValue() {
    const value = this.field.value;
    if (isString(value)) {
      return value;
    }
    return '';
  }

  columns: TableColumn[] = refsFieldRevisionsTableColumnsConfig(
    this.locale,
    this.regionalSettings.dateFormat,
  );
  selectedRow: RevisionTableRow;
  selectedRevision: RevisionTableRow;
  userNames: Dictionary<User> = {};
  rows$: Observable<RevisionTableRow[]>;
  loading = false;
  refsField: FinStructureFieldInfo;
  dataGridConfig: DataGridConfig | null;
  DataGridViewMode = DataGridViewMode;

  private allRevisions: RevisionTableRow[];
  readonly financingStructureId$: Observable<string> = this.store.select(
    selectFinancingStructureId,
  );

  constructor(
    private destroyRef: DestroyRef,
    private finStructureControllerService: FinStructureControllerService,
    private finToastService: FinToastService,
    private store: Store,
    @Inject(LOCALE_ID) private locale: Locale,
    private regionalSettings: RegionalSettingsService,
  ) {}

  ngOnInit(): void {
    this.getAllRevisions();
    this.refsField = cloneDeep(this.field);
  }

  getAllRevisions() {
    this.loading = true;

    this.rows$ = combineLatest([
      this.finStructureControllerService
        .getAllRevisionsForFieldInFinStructure({
          businessCaseId: this.businessCase.id,
          finStructureId: this.finStructureId,
          fieldId: this.field.id,
        })
        .pipe(
          tap((revisions: FinStructureRevision[]) =>
            this.store.dispatch(
              StateLibUserPageActions.loadUsers({
                payload: uniq(
                  revisions.map((r: FinStructureRevision) => r.userId),
                ),
              }),
            ),
          ),
        ),
      this.store.select(selectUsersById),
    ]).pipe(
      map(([revisions, usersById]) => {
        this.userNames = usersById;
        this.loading = false;
        return revisions.map((revision: FinStructureRevision) =>
          this.mapRevisionToRevisionTableRow(
            revision,
            this.isCurrentVersion(revision),
          ),
        );
      }),
      tap((revisions) => {
        this.allRevisions = revisions;

        const currentRevision = revisions.find(
          (revision: RevisionTableRow) => revision.isCurrentVersion,
        );
        if (currentRevision) {
          this.selectRevision(currentRevision);
        }
      }),
      catchError(() => {
        this.loading = false;
        this.finToastService.show(Toast.error());

        return of([]);
      }),
      takeUntilDestroyed(this.destroyRef),
    );
  }

  getUserName(item: { userId?: string }) {
    return [
      this.userNames[item.userId]?.firstName,
      this.userNames[item.userId]?.lastName,
    ]
      .filter(Boolean)
      .join(' ');
  }

  mapRevisionToRevisionTableRow(
    revision: FinStructureRevision,
    isCurrentVersion: boolean,
  ): RevisionTableRow {
    return {
      date: revision.lastModifiedDate,
      revisionId: revision.id,
      information: revision?.fieldValue || '',
      isCurrentVersion,
      changedFrom: this.getUserName(revision),
      changedFromId: revision.userId,
    };
  }

  reactivateRevision(finStructureId: string) {
    this.store.dispatch(
      RefsPageActions.restoreRevision({
        payload: {
          businessCaseId: this.businessCase.id,
          finStructureId,
          revisionId: this.selectedRow.revisionId,
          newValueForPurposeOfUse:
            this.field.key === DynamicFieldsetKey.INTENDED_USE
              ? this.selectedRow.information.toString()
              : '',
        },
      }),
    );

    this.allRevisions.forEach(
      (revision) => (revision.isCurrentVersion = false),
    );
    this.rows$ = of(this.allRevisions);
    this.selectedRow.isCurrentVersion = true;
  }

  showRevision(row: RevisionTableRow) {
    this.table.selectRow(row);
    this.selectRevision(row);
  }

  private selectRevision(row: RevisionTableRow) {
    this.selectedRow = row;
    this.selectedRevision = this.selectedRow;
    this.refsField = { ...this.refsField, value: row.information };

    if (this.isDataTable()) {
      this.dataGridConfig = DataGridConfig.buildFromInformationValue(
        this.selectedRevision.information
          ? typeof this.selectedRevision.information === 'string'
            ? JSON.parse(this.selectedRevision.information as string)
            : this.selectedRevision.information
          : (this.selectedRevision.information as DataGridConfig),
      );

      this.dataGrid?.reloadGrid(this.dataGridConfig);
    } else {
      this.dataGridConfig = null;
    }
  }

  isDataTable() {
    return this.field?.fieldType === 'TABLE';
  }

  private isCurrentVersion(revision: FinStructureRevision) {
    return (
      this.field.isRestoredFromRevision &&
      this.field.appliedRevisionId === revision.id
    );
  }
}
