@if (refContentView$ | async; as refContentView) {
  @if (navigationsAreOpen$ | async; as navigation) {
    <app-sidebar-content-manager
      class="tw-h-full"
      [sidePanelStyles]="{ height: sidePanelHeight$ | async }"
      [pagePanelIsOpen]="navigation.pageNavigationIsOpen"
      (toggle)="toggleNavigation(navigation)"
    >
      <ng-template #navPanel>
        <fin-scrollbar>
          <app-refs-side-nav-list
            class="tw-max-w-full"
            (addField)="onFieldAdd($event.field.key)"
          ></app-refs-side-nav-list>
        </fin-scrollbar>
      </ng-template>
      <ng-template #mainPanel>
        <div class="tw-block tw-mx-[0.1rem] tw-bg-color-surface-primary">
          <div *ngxPermissionsOnly="[businessCasePermissionHtml.BCP_00130]">
            <app-refs-stats
              *ngxPermissionsOnly="[businessCasePermissionHtml.BCP_00130]"
              [class.sticky-chart]="makeChartSticky$ | async"
              [data]="chartData$ | async"
              (scrollToSection)="scrollToSection($event)"
            >
            </app-refs-stats>
          </div>
          @if (searchData$ | async; as searchData) {
            <div
              class="tw-flex tw-justify-between tw-px-[2.4rem] tw-py-[1.2rem] tw-gap-[2.4rem] tw-items-center"
            >
              <fin-search
                class="tw-w-full"
                dynamicErrorSpace="dynamic"
                [formControl]="searchFormControl"
                [showLoader]="searchData.fetchingData"
                [autocomplete]="true"
                [options]="autocomplete$ | async"
                (inputFocus)="onSearchFocus()"
                (autoCompleteOptionChange)="dispatchSearch($event)"
                (pressEnter)="dispatchSearch($event)"
                [numberOfResults]="searchData.matches"
              >
              </fin-search>
              <div>
                <button
                  fin-button
                  [shape]="finButtonShape"
                  [size]="finSize.L"
                  [appearance]="finButtonAppearance.INFORMATIVE"
                  [finActionMenuTrigger]="finMenu.panel"
                  #trigger="finActionMenuTrigger"
                >
                  <fin-icon name="more_vert" [size]="finSize.M"></fin-icon>
                  {{ actionsButtonLabel }}
                </button>

                <fin-actions-menu #finMenu="finActionMenu">
                  @for (option of options; track option) {
                    @if (option.isVisible$ | async) {
                      <button
                        fin-menu-item
                        [size]="finSize.M"
                        [iconName]="option.icon"
                        *ngxPermissionsOnly="option.permissions || null"
                        (click)="onMenuItemClick(option.value)"
                      >
                        <ng-container finMenuItemTitle>
                          {{ option.label }}</ng-container
                        >
                      </button>
                    }
                  }
                </fin-actions-menu>
              </div>
            </div>
          }
        </div>
        <fin-scrollbar
          #finScrollbar
          appBusinessCaseMainContentScroll
          [smoothResize]="true"
          [ngStyle]="{ height: contentWrapperHeight$ | async }"
        >
          <div
            class="tw-flex tw-flex-col tw-h-full"
            [ngStyle]="{ 'min-height': contentHeight$ | async }"
          >
            @if (searchData$ | async; as searchData) {
              @if (searchData.hasResults) {
                <app-refs-accordion
                  [staticGroups]="staticGroups$ | async"
                  [firstIdToExpand]="firstIdToExpand$ | async"
                  [secondIdToExpand]="secondIdToExpand$ | async"
                  [highlightId]="highlightId$ | async"
                  [closeOthers]="!searchData.groupIds.length"
                  [highlightPrefix]="highlightPrefix"
                  [businessCase]="businessCase$ | async"
                  [finStructureId]="finStructureId$ | async"
                  [isEditMode]="isEditMode$ | async"
                  [matchingToSearchGroupIds]="searchData.groupIds"
                  [shouldExpandSubAccordions]="!!searchData.searchTerm.length"
                  (clearScrollToSection)="clearScrollToSection()"
                  (scrollToExpandedGroup)="
                    scrollToExpandedGroup($event, refContentView.headerHeight)
                  "
                  [mirroredFieldKeys]="mirroredFieldKeys$ | async"
                  [isCustomerLead]="isCustomerLead$ | async"
                  [customerType]="customerType$ | async"
                >
                  <app-refs-building-blocks
                    [activeTab]="overviewActiveTab$ | async"
                    (tabChanged)="onTabChanged($event)"
                  >
                    @if (
                      (canShowDataExportTips$ | async) &&
                      (buildingBlocksCount$ | async) > 0
                    ) {
                      <fin-warning-message
                        [label]="hintMessage"
                        [showIcon]="true"
                        [appearance]="warningMessagAppearance.INFORMATIVE"
                      ></fin-warning-message>
                    }
                  </app-refs-building-blocks>
                </app-refs-accordion>
              } @else {
                <app-refs-no-results></app-refs-no-results>
              }
            }
          </div>
        </fin-scrollbar>
      </ng-template>

      @if (isNeoGptVisible$ | async; as neoGptState) {
        @if (neoGptState.isChatVisible && neoGptState.isNeoGptActive) {
          <ng-template #rightPanel>
            <div class="tw-grow">
              <app-neogpt-chat></app-neogpt-chat>
            </div>
          </ng-template>
        }
      }
    </app-sidebar-content-manager>
  }
}
