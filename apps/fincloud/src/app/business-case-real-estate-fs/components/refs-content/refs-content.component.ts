import { DOCUMENT } from '@angular/common';
import {
  AfterViewInit,
  Component,
  DestroyRef,
  Inject,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import { FieldTypeEnum } from '@fincloud/core/formly';
import { LayoutCommunicationService } from '@fincloud/core/layout';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import { CssService } from '@fincloud/core/services';
import { PERCENTAGE_MASK_CONFIG } from '@fincloud/core/utils';
import { NumberFieldConfigBuilder } from '@fincloud/neoshare/business-case-fields';
import {
  StateLibBusinessCasePageActions,
  selectBusinessCase,
  selectBusinessCaseContentHeightRealEstateFinanceStructure,
  selectBusinessCaseSidePanelHeightDefault,
  selectBusinessCaseWrapperHeightRealEstateFinanceStructure,
  selectEditTemplateMode,
  selectIsLead,
  selectMirroredFieldKeys,
} from '@fincloud/state/business-case';
import {
  StateLibScrollToSectionPageActions,
  businessCaseRealEstateFeature,
  selectDynamicFieldsetsCount,
  selectFinancingStructureId,
  selectScrollToSectionFirstId,
  selectScrollToSectionHighlightId,
  selectScrollToSectionSecondId,
} from '@fincloud/state/business-case-real-estate';
import {
  selectCustomerType,
  selectIsVolksbankGroupWithBmsSales,
} from '@fincloud/state/customer';
import {
  selectIsChatVisible,
  selectIsNeoGptVisible,
} from '@fincloud/state/neogpt-chat';
import {
  StateLibSideNavigationsPageActions,
  sideNavigationsFeature,
} from '@fincloud/state/side-navigations';
import { ExchangeBusinessCase } from '@fincloud/swagger-generator/exchange';
import { BusinessCasePermission } from '@fincloud/types/enums';
import {
  CustomFinStructureGroup,
  OVERVIEW_PAGE_NAV,
} from '@fincloud/types/models';
import { FinBadgeStatus } from '@fincloud/ui/badges';
import { FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import { FinModalService } from '@fincloud/ui/modal';
import { FinScrollbarComponent } from '@fincloud/ui/scrollbar';
import { FinSearchAutocompleteOption } from '@fincloud/ui/search';
import { FinSize } from '@fincloud/ui/types';
import { FinWarningMessageAppearance } from '@fincloud/ui/warning-message';
import { HIGHLIGHT_PREFIX, SCROLL_TO_DELAY } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { TraceClass, TraceMethod } from '@sentry/angular';
import { isEqual, isNil } from 'lodash-es';
import { Observable, asyncScheduler, of } from 'rxjs';
import {
  auditTime,
  distinctUntilChanged,
  filter,
  map,
  observeOn,
  shareReplay,
  tap,
} from 'rxjs/operators';
import {
  RefsPageActions,
  SearchPageActions,
  TeaserExportPageActions,
} from '../../+state/actions';
import {
  selectAutocomplete,
  selectChartData,
  selectOverviewActiveTab,
  selectRefContentView,
  selectStaticGroups,
} from '../../+state/selectors';
import { ActionMenu } from '../../enums/action-menu';
import { OverviewPageNavEnum } from '../../enums/overview-page-nav-enum';
import { ScrollToSectionPayload } from '../../models/scroll-to-section';
import { RefsContentService } from '../../services/refs-content.service';
import { SearchFinancingService } from '../../services/search-financing.service';
import { RefsAddFinancingBlockModalComponent } from '../refs-add-financing-block-modal/refs-add-financing-block-modal.component';
import { RefsGroupsSharingModalComponent } from '../refs-groups-sharing-modal/refs-groups-sharing-modal.component';
import { RefsTeaserModalComponent } from '../refs-teaser-modal/refs-teaser-modal.component';

@TraceClass({ name: 'RefsContentComponent' })
@Component({
  selector: 'app-refs-content',
  templateUrl: './refs-content.component.html',
  styleUrls: ['./refs-content.component.scss'],
})
export class RefsContentComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('finScrollbar') finScrollbar: FinScrollbarComponent;

  readonly overviewPageNavEnum = OverviewPageNavEnum;
  readonly hintMessage = $localize`:@@financing.details.hint:Kreditbetrag, Zahlungstermin, Disagio, Gesamtzinssatz p.a., Gesamtlaufzeit bis, 1. Zinsbindung bis und Valutierung sind Pflichtfelder für den Datenexport ins Kernbankensystem.`;
  buildingBlocksCount$ = this.store.select(selectDynamicFieldsetsCount);
  warningMessagAppearance = FinWarningMessageAppearance;

  finButtonShape = FinButtonShape.RECTANGLE;
  finSize = FinSize;
  finBadgeStatus = FinBadgeStatus;
  highlightPrefix = HIGHLIGHT_PREFIX;
  fieldType = FieldTypeEnum;
  businessCasePermissionHtml = BusinessCasePermission;
  configurationOverrides =
    PERCENTAGE_MASK_CONFIG[this.regionalSettingsService.locale];
  targetAmount: number;

  actionsButtonLabel = $localize`:@@button.label.actions:Aktionen`;

  readonly finButtonAppearance = FinButtonAppearance;
  refContentView$ = this.store.select(selectRefContentView);
  navigationsAreOpen$ = this.store.select(
    sideNavigationsFeature.selectNavigationAndChatAreOpen,
  );

  canShowDataExportTips$ = this.store.select(
    selectIsVolksbankGroupWithBmsSales,
  );

  isChatVisible$ = this.store.select(selectIsChatVisible);
  percentIntegerConfig = new NumberFieldConfigBuilder()
    .setPrecision(0)
    .getConfiguration();

  overviewActiveTab$: Observable<OVERVIEW_PAGE_NAV> = this.store
    .select(selectOverviewActiveTab)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  businessCase$: Observable<ExchangeBusinessCase> =
    this.store.select(selectBusinessCase);
  chartData$ = this.store.select(selectChartData);
  isCustomerLead$ = this.store.select(selectIsLead);
  makeChartSticky$: Observable<boolean> =
    this.layoutCommunicationService.scrollY$.pipe(
      takeUntilDestroyed(this.destroyRef),
      map((distanceFromTop) => distanceFromTop >= 330),
      distinctUntilChanged(),
    );

  staticGroups$: Observable<CustomFinStructureGroup[]> = this.store
    .select(selectStaticGroups)
    .pipe(filter((groups) => groups && groups.length > 0));

  firstIdToExpand$ = this.store.select(selectScrollToSectionFirstId);
  secondIdToExpand$ = this.store.select(selectScrollToSectionSecondId);

  readonly highlightId$ = this.store.select(selectScrollToSectionHighlightId);

  autocomplete$: Observable<FinSearchAutocompleteOption[]> =
    this.store.select(selectAutocomplete);

  searchData$ = this.refsContentService.searchData$;

  isEditMode$: Observable<boolean> = this.store
    .select(selectEditTemplateMode)
    .pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  finStructureId$: Observable<string> = this.store.select(
    selectFinancingStructureId,
  );

  customerType$ = this.store.select(selectCustomerType);

  isNeoGptVisible$ = this.store
    .select(selectIsNeoGptVisible)
    .pipe(distinctUntilChanged((prev, curr) => isEqual(prev, curr)));

  options = [
    {
      icon: 'accessibility',
      label: $localize`:@@groupVisibility.card.span:Zugriff verwalten`,
      value: ActionMenu.OPEN_MANAGE_ACCESS,
      permissions: [BusinessCasePermission.BCP_00070],
      isVisible$: this.isEditMode$,
    },
    {
      icon: 'downloading',
      label: $localize`:@@dashboard.businessCase.pdfExport:PDF-Export`,
      value: ActionMenu.OPEN_FINANCING_TEASER_EXPORT,
      permissions: [
        BusinessCasePermission.BCP_00151,
        BusinessCasePermission.BCP_00152,
      ],
      isVisible$: of(true),
    },
  ];
  mirroredFieldKeys$: Observable<string[]> = this.store.select(
    selectMirroredFieldKeys,
  );

  searchFormControl = new FormControl('');

  textCollapsed = $localize`:@@navigationPanel.collapse.btn.tooltip:Ausblenden`;
  textExpanded = $localize`:@@navigationPanel.expand.btn.tooltip:Erweitern`;

  readonly sidePanelHeight$ = this.store.select(
    selectBusinessCaseSidePanelHeightDefault,
  );

  readonly contentWrapperHeight$ = this.store.select(
    selectBusinessCaseWrapperHeightRealEstateFinanceStructure,
  );

  readonly contentHeight$ = this.store.select(
    selectBusinessCaseContentHeightRealEstateFinanceStructure,
  );

  constructor(
    @Inject(DOCUMENT) private document: Document,
    private destroyRef: DestroyRef,
    private store: Store,
    private finModalService: FinModalService,
    private layoutCommunicationService: LayoutCommunicationService,
    private cssService: CssService,
    private refsContentService: RefsContentService,
    private searchFinancingService: SearchFinancingService,
    private regionalSettingsService: RegionalSettingsService,
  ) {}

  @TraceMethod({ name: 'RefsContentComponent.OnInit' })
  ngOnInit() {
    this.store.dispatch(
      StateLibBusinessCasePageActions.loadMirroredFieldKeys({}),
    );
  }

  ngAfterViewInit() {
    this.store
      .select(businessCaseRealEstateFeature.selectScrollToSection)
      .pipe(
        tap(({ firstId, secondId, thirdId, highlightId }) => {
          if (firstId || secondId || thirdId || highlightId) {
            this.refsContentService.clearSearch();
            this.searchFormControl.reset('', { emitEvent: false });
          }
        }),
        observeOn(asyncScheduler),
        auditTime(SCROLL_TO_DELAY),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(({ firstId, secondId, thirdId, highlightId }) => {
        if (highlightId || thirdId || secondId || firstId) {
          const element = this.document.getElementById(
            highlightId || thirdId || secondId || firstId,
          );

          this.finScrollbar.scrollToElement(element, {
            top: 0,
          });
        }

        if (highlightId) {
          this.cssService.removeCssClass('', 'mark');
          this.cssService.addCssClass(highlightId, 'mark');
        }
      });

    this.searchFormControl.valueChanges
      .pipe(auditTime(400), takeUntilDestroyed(this.destroyRef))
      .subscribe((searchTerm) => this.dispatchAutocomplete(searchTerm));
  }

  ngOnDestroy(): void {
    this.refsContentService.clearSearch();
  }

  onTabChanged(activeTab: OverviewPageNavEnum) {
    this.store.dispatch(
      RefsPageActions.setOverviewPageActiveTab({ payload: activeTab }),
    );
  }

  clearScrollToSection(): void {
    this.store.dispatch(
      StateLibScrollToSectionPageActions.clearScrollToSection(),
    );
  }

  onSearchFocus(): void {
    this.refsContentService.setSearchFocus(this.searchFormControl.value);
  }

  dispatchSearch(searchTerm: string | undefined): void {
    if (isNil(searchTerm)) {
      return;
    }
    const { canHandle, searchLabels } =
      this.searchFinancingService.labelSearchCriteria(searchTerm);
    if (!canHandle) {
      return;
    }
    const searchCriteria =
      this.searchFinancingService.searchCriteria(searchTerm);
    this.store.dispatch(
      SearchPageActions.searchFinancingStructure({
        payload: {
          fieldSearchCriteria: searchCriteria,
          searchLabels,
        },
      }),
    );
  }

  toggleNavigation(navigation: {
    isChatVisible: boolean;
    platformNavigationIsOpen: boolean;
  }) {
    if (navigation.isChatVisible && navigation.platformNavigationIsOpen) {
      this.store.dispatch(
        StateLibSideNavigationsPageActions.toggleOpenForPlatformNavigation(),
      );
    }
    this.store.dispatch(
      StateLibSideNavigationsPageActions.toggleOpenForPageNavigation(),
    );
  }

  onFieldAdd(fieldKey: string) {
    this.finModalService.open(RefsAddFinancingBlockModalComponent, {
      data: fieldKey,
      size: this.finSize.S,
      disableClose: true,
    });
  }

  onMenuItemClick(value: string): void {
    switch (value) {
      case ActionMenu.OPEN_FINANCING_TEASER_EXPORT:
        this.openFinancingTeaserExport();
        break;
      case ActionMenu.OPEN_MANAGE_ACCESS:
        this.shareRefsGroups();
        break;
      default:
        break;
    }
  }

  shareRefsGroups() {
    this.finModalService.open(RefsGroupsSharingModalComponent, {
      size: this.finSize.M,
      disableClose: true,
    });
  }

  openFinancingTeaserExport(): void {
    this.store.dispatch(TeaserExportPageActions.getMyFinStructureTeaser());
    this.finModalService.open(RefsTeaserModalComponent, {
      size: this.finSize.M,
      disableClose: true,
    });
  }

  private dispatchAutocomplete(searchTerm: string | null): void {
    const { canDispatch } =
      this.searchFinancingService.dispatchAutocomplete(searchTerm);
    if (!canDispatch) {
      this.refsContentService.clearSearch();
      this.searchFormControl.reset('', { emitEvent: false });
      return;
    }
    if (this.searchFinancingService.isBasicSearchCriteriaMet(searchTerm)) {
      this.store.dispatch(
        SearchPageActions.autocompleteFinancingStructure({
          payload: { searchLabels: [searchTerm] },
        }),
      );
    } else {
      this.refsContentService.clearSearch();
    }
  }

  scrollToExpandedGroup(elementId: string, height: number): void {
    const element = this.document.getElementById(elementId);

    this.finScrollbar.scrollToElement(element, {
      top: this.refsContentService.remToPxConvert(-height),
    });
  }

  scrollToSection(payload: ScrollToSectionPayload): void {
    this.store.dispatch(
      StateLibScrollToSectionPageActions.scrollToSection({
        payload,
      }),
    );
  }
}
