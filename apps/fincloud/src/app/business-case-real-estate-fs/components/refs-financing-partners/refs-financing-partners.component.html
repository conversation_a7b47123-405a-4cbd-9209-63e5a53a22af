@if (rowsData$ | async; as rowsData) {
  @if (!buildingBlockAmount) {
    <fin-warning-message
      class="tw-mb-[1.6rem]"
      [appearance]="finWarningMessageAppearance.WARNING"
      [showIcon]="false"
    >
      <div
        class="tw-text-start tw-text-text-body-3-size tw-text-color-text-primary"
        i18n="@@financingPartners.buildingBlockAmount.warning"
      >
        Um die Beteiligung des Finanzierungspartners zu berechnen, muss das Feld
        <strong>'Betrag'</strong> im Finanzierungsbaustein größer als 0 sein.
      </div>
    </fin-warning-message>
  }
  @if (
    rowsData.hasPopulatedPartnerName &&
    rowsData.actionButtonsData.totalParticipationPercent !==
      maxParticipationPercent
  ) {
    <fin-warning-message
      class="tw-mb-[1.6rem]"
      [appearance]="finWarningMessageAppearance.WARNING"
      [showIcon]="false"
    >
      <ng-container
        [ngTemplateOutlet]="warningMsg"
        [ngTemplateOutletContext]="{
          data: rowsData.actionButtonsData,
        }"
      ></ng-container>
    </fin-warning-message>
  }

  <fin-table
    (finObserveResize)="onTableResize($event)"
    headerClasses="!tw-bg-color-background-neutral-minimal !tw-border-y !tw-border-solid !tw-border-color-border-default-primary"
    autoHideColumns
    [rows]="rowsData.rows"
    [columns]="columns"
    [headerHeight]="44"
    [hasRowSpacing]="true"
    [emptyMessage]="emptyMessage"
    [rowClassFn]="customRowClasses"
  >
    <ng-container headerTemplates>
      <ng-template
        name="financingPartner"
        [finHeaderTemplate]="columns"
        let-column
      >
        <div class="tw-flex tw-gap-[1.2rem]">
          <div class="tw-min-w-0" finTruncateText>{{ column.name }}</div>
          <fin-icon
            name="info"
            [size]="finSize.S"
            finTooltip
            i18n-content="@@financingPartners.financingPartner.tooltip"
            content="Als Finanzierungspartner gelten alle kreditgebenden Personen, Unternehmen und Institute sowie der Kreditgeber selbst. Sobald ein Finanzierungspartner hinzugefügt wurde, wird er aus den Auswahlmöglichkeiten entfernt."
          ></fin-icon>
        </div>
      </ng-template>
      <ng-template
        name="financingParticipationPercent"
        [finHeaderTemplate]="columns"
        let-column
      >
        <div class="tw-min-w-0" finTruncateText>
          {{ column.name }}
          <span class="tw-inline-block tw-w-[1.12rem]">{{
            participationSuffix.percent
          }}</span>
        </div>
      </ng-template>
      <ng-template
        name="financingParticipationAmount"
        [finHeaderTemplate]="columns"
        let-column
      >
        <div class="tw-min-w-0" finTruncateText>
          {{ column.name }}
          <span class="tw-inline-block tw-w-[1.12rem]">{{
            participationSuffix.amount
          }}</span>
        </div>
      </ng-template>
    </ng-container>

    <ng-template
      name="financingPartner"
      [finRowTemplate]="rowsData.rows"
      let-row
    >
      <div [formGroup]="row" class="tw-w-full">
        @if (row.controls.partnerName?.disabled) {
          <div
            class="tw-text-body-2-moderate tw-text-color-text-primary"
            finTruncateText
          >
            {{ row.controls.partnerName.value }}
          </div>
        } @else {
          <fin-dropdown
            autocomplete
            hideArrow
            i18n-placeholder="@@search"
            placeholder="Suche"
            formControlName="partnerName"
            [preserveLastSelectedOption]="false"
            [options]="
              {
                northData: northData.companiesOptions,
                names: rowsData.partnersNames,
                fetchingCompanies: northData.fetchingCompanies,
                formControls: row.controls,
              } | executeFunc: filterRemainingOptions
            "
            (autoCompleteInputChange)="onAutoCompleteInputChange($event, row)"
            (selectionChange)="onSelectionChange(row)"
            (blurred)="onBlurPartnersNameOptions()"
          >
            @if (editMode) {
              <ng-container finFieldSuffix>
                @if (
                  !northData.companiesOptions.length &&
                  row.controls.partnerName.value &&
                  row.controls.participationPercent.disabled &&
                  row.controls.partnerName.valid &&
                  (northData.fetchingCompanies | isFalsy)
                ) {
                  <button
                    fin-button-icon
                    type="button"
                    [size]="finSize.XS"
                    (click)="addManualCompany(row)"
                  >
                    <fin-icon name="add"> </fin-icon>
                  </button>
                }
              </ng-container>
            }
            <fin-field-messages>
              <ng-template
                finFieldMessage
                type="error"
                errorKey="duplicatePartnerName"
                i18n="@@financingPartners.alreadyAddedPartner"
              >
                Der Finanzierungspartner wurde bereits zum Finanzierungsbaustein
                hinzugefügt.
              </ng-template>
            </fin-field-messages>
          </fin-dropdown>
        }
      </div>
    </ng-template>

    <ng-template
      name="financingParticipationPercent"
      [finRowTemplate]="rowsData.rows"
      let-row
    >
      <div [formGroup]="row" class="tw-w-full">
        <fin-input
          formControlName="participationPercent"
          i18n-placeholder="@@financingPartners.forExample"
          placeholder="b.z 30 %"
          [type]="finType.PERCENTAGE"
          [max]="maxParticipationPercent"
          [readonly]="!editMode"
        >
          <fin-field-messages>
            <ng-template finFieldMessage type="warning" errorKey="notEqual">
            </ng-template>
          </fin-field-messages>
        </fin-input>
      </div>
    </ng-template>

    <ng-template
      name="financingParticipationAmount"
      [finRowTemplate]="rowsData.rows"
      let-row
    >
      <div class="tw-text-body-2-moderate">
        @if (row.controls.participationAmount.value | executeFunc: isNumber) {
          {{
            row.controls.participationAmount.value
              | currency
              | removeTrailingZeros
          }}
        } @else {
          -
        }
      </div>
    </ng-template>
    <ng-template name="icons" [finRowTemplate]="rowsData.rows" let-row>
      @if (editMode) {
        <button fin-button-action (click)="onDeleteRow(row)">
          <fin-icon
            name="delete"
            class="tw-text-buttons-action-tertiary-color-icon-default"
            [size]="finSize.S"
          ></fin-icon>
        </button>
      }
    </ng-template>
  </fin-table>

  @if (editMode) {
    <div class="tw-flex tw-justify-between tw-mt-[1.6rem] tw-mx-[2.4rem]">
      <div>
        @if (rowsData.actionButtonsData.canAddNewPartner) {
          @if (showIconButton) {
            <button
              fin-button-icon
              finTooltip
              [size]="finSize.L"
              [appearance]="finAppearance.SECONDARY"
              i18n-content="@@financingPartners.addPartner"
              content="Finanzierungspartner hinzufügen"
              (click)="onAddNewPartnerRow()"
            >
              <fin-icon name="add"></fin-icon>
            </button>
          } @else {
            <button
              fin-button
              [size]="finSize.L"
              [appearance]="finAppearance.SECONDARY"
              (click)="onAddNewPartnerRow()"
              i18n="@@financingPartners.addPartner"
            >
              Finanzierungspartner hinzufügen
            </button>
          }
        }
      </div>
      @if (rowsData.actionButtonsData.hasChanges) {
        <div class="tw-flex tw-gap-[1.6rem]">
          <button
            fin-button
            i18n="@@button.label.cancel"
            [size]="finSize.L"
            [appearance]="finAppearance.STEALTH"
            (click)="onCancel()"
          >
            Abbrechen
          </button>
          <button
            fin-button
            i18n="@@button.label.save"
            [size]="finSize.L"
            [disabled]="
              rowsData.actionButtonsData.totalParticipationPercent >
                maxParticipationPercent || rowsData.hasInvalidPartnerName
            "
            (click)="onSave(rowsData.rows)"
          >
            Speichern
          </button>
        </div>
      }
    </div>
  }
}

<ng-template #warningMsg let-data="data">
  <div
    class="tw-text-start tw-text-text-body-3-size tw-text-color-text-primary"
  >
    @if (data.totalParticipationPercent > maxParticipationPercent) {
      <span i18n="@@financingPartners.participationAboveThreshold">
        Der Gesamtbetrag der finanziellen Beteiligung liegt derzeit
        <strong>uber 100%.</strong> Um fortzufahren, passen Sie bitte die
        Eingaben so an, <strong>dass die Summe 100% beträgt.</strong>
      </span>
    } @else {
      <span i18n="@@financingPartners.participationBelowThreshold">
        Der Gesamtbetrag der finanziellen Beteiligung liegt derzeit
        <strong>unter 100%.</strong>
      </span>
    }
  </div>
</ng-template>
