import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import { AbstractControl, FormGroup } from '@angular/forms';
import { setTimeoutUnpatched } from '@fincloud/core/utils';
import { FinStructureField } from '@fincloud/swagger-generator/financing-details';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinDropdownOption } from '@fincloud/ui/dropdown';
import { FinInputType } from '@fincloud/ui/input';
import { FinSize } from '@fincloud/ui/types';
import { FinWarningMessageAppearance } from '@fincloud/ui/warning-message';
import { difference, isNumber } from 'lodash-es';
import { Observable } from 'rxjs';
import { TableBreakPoint } from '../../enums/financing-partners-breakpoints-table';
import { NorthCompaniesData } from '../../models/north-companies-data';
import { FinStructureFieldFinancingPartners } from '../../models/refs-fin-structure-field-financing-partners';
import { RowsData } from '../../models/rows-data';
import { RefsFinancingPartnersService } from '../../services/refs-financing-partners.service';
import { EMPTY_MESSAGE } from '../../utils/empty-message';
import { MAX_PARTICIPATION_PERCENT } from '../../utils/financing-partners-total-participation-amount';
import { FINANCING_PARTNERS_COLUMNS } from '../../utils/get-colums-config';
import { PARTICIPATION_SUFFIX } from '../../utils/participation-suffix';

@Component({
  selector: 'app-refs-financing-partners',
  templateUrl: './refs-financing-partners.component.html',
  styleUrls: ['./refs-financing-partners.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RefsFinancingPartnersComponent implements OnDestroy {
  readonly columns = FINANCING_PARTNERS_COLUMNS;
  readonly finSize = FinSize;
  readonly emptyMessage = EMPTY_MESSAGE;
  readonly finAppearance = FinButtonAppearance;
  readonly finType = FinInputType;
  readonly finWarningMessageAppearance = FinWarningMessageAppearance;
  readonly participationSuffix = PARTICIPATION_SUFFIX;
  readonly maxParticipationPercent = MAX_PARTICIPATION_PERCENT;
  showIconButton = false;

  private _editMode = false;

  get editMode(): boolean {
    return this._editMode;
  }
  @Input() set editMode(value: boolean) {
    this._editMode = value;
    setTimeoutUnpatched(() => {
      //this .next() and the one from field are happens in sequence,
      //but at that time template is not evaluated yet, therefor no subscriber (subscription via async)
      //is initialized yet, so we lose one of the two emits (as BehaviourSubject kept only last one)
      if (value) {
        this.refsFinancingPartnersService.triggerValidation();
      } else {
        this.refsFinancingPartnersService.resetSnapshot();
      }
    }, 0);
  }

  @Input() set field(data: FinStructureFieldFinancingPartners | null) {
    this.refsFinancingPartnersService.setState(data?.value || []);
    if (this.editMode) {
      this.refsFinancingPartnersService.resetWithValidation();
    }
  }

  @Input() northData: NorthCompaniesData = {
    companiesOptions: [],
    fetchingCompanies: false,
  };

  @Input() buildingBlockAmount: number;

  @Output() loadNorthDataCompany = new EventEmitter<string>();
  @Output() clearNorthDataCompanies = new EventEmitter<void>();
  @Output() addedRowsChange = new EventEmitter<FinStructureField['value']>();

  readonly rowsData$: Observable<RowsData> =
    this.refsFinancingPartnersService.rowsData$;

  constructor(
    private refsFinancingPartnersService: RefsFinancingPartnersService,
  ) {}

  onTableResize(event: ResizeObserverEntry): void {
    const inlineTableSize = event.contentBoxSize[0].inlineSize;

    this.showIconButton = inlineTableSize < TableBreakPoint.MEDIUM_RESOLUTION;
  }
  onSelectionChange(row: FormGroup): void {
    this.refsFinancingPartnersService.enableParticipationPercent(row);
    if (!row.controls.partnerName.value) {
      this.refsFinancingPartnersService.triggerValidation();
    }
  }

  onAutoCompleteInputChange(query: string, row: FormGroup): void {
    this.loadNorthDataCompany.emit(query);
    if (row.controls.participationPercent.enabled) {
      this.refsFinancingPartnersService.disableParticipationPercent(row);
    }
  }

  onBlurPartnersNameOptions(): void {
    this.clearNorthDataCompanies.emit();
  }

  onAddNewPartnerRow(): void {
    this.refsFinancingPartnersService.addFormRows();
  }

  onDeleteRow(row: FormGroup): void {
    this.refsFinancingPartnersService.deleteFormRow(row);
  }

  onSave(rows: FormGroup[]): void {
    const addedRows =
      this.refsFinancingPartnersService.getNonEmptyFormRows(rows);
    this.refsFinancingPartnersService.setState(addedRows);
    this.addedRowsChange.emit(addedRows);
  }

  onCancel(): void {
    this.refsFinancingPartnersService.resetWithValidation();
  }

  isNumber(value: number | null): boolean {
    return isNumber(value);
  }

  addManualCompany(row: FormGroup): void {
    this.refsFinancingPartnersService.enableParticipationPercent(row);
  }

  filterRemainingOptions(data: {
    northData: FinDropdownOption[];
    names: string[];
    fetchingCompanies: boolean;
    formControls: { [key: string]: AbstractControl };
  }): FinDropdownOption[] {
    const {
      northData,
      names,
      fetchingCompanies,
      formControls: { partnerName, participationPercent },
    } = data;

    const northDataLabels = northData.map((option) => option.value as string);
    const filteredOptions = difference(northDataLabels, names);
    const result = northData.filter((option) =>
      filteredOptions.includes(option.value as string),
    );

    if (result.length) {
      return result;
    }
    if (
      partnerName.value &&
      !fetchingCompanies &&
      !participationPercent.disabled
    ) {
      return [
        {
          value: partnerName.value,
          label: partnerName.value,
        },
      ];
    }
    return [];
  }

  customRowClasses() {
    return '!tw-h-[auto] !tw-py-[0.2rem] [&>.mat-mdc-cell]:!tw-items-start [&>.mat-mdc-cell>div]:tw-min-h-[4rem] [&>.mat-mdc-cell>div]:tw-flex [&>.mat-mdc-cell>div]:tw-items-center';
  }

  ngOnDestroy() {
    this.refsFinancingPartnersService.resetSnapshot();
  }
}
