import { DOCUMENT } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  Inject,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import { FieldTypeEnum } from '@fincloud/core/formly';
import { CssService } from '@fincloud/core/services';
import {
  selectBusinessCaseContentHeightRealEstateSharedFinanceStructure,
  selectBusinessCaseSidePanelHeightDefault,
  selectBusinessCaseWrapperHeightRealEstateSharedFinanceStructure,
} from '@fincloud/state/business-case';
import {
  StateLibBusinessCaseRealEstatePageActions,
  StateLibScrollToSectionPageActions,
  businessCaseRealEstateFeature,
} from '@fincloud/state/business-case-real-estate';
import { StateLibSideNavigationsPageActions } from '@fincloud/state/side-navigations';
import { BusinessCasePermission } from '@fincloud/types/enums';
import { FinBadgeStatus } from '@fincloud/ui/badges';
import { FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import { FinModalService } from '@fincloud/ui/modal';
import { FinScrollbarComponent } from '@fincloud/ui/scrollbar';
import { FinSearchAutocompleteOption } from '@fincloud/ui/search';
import { FinTabType } from '@fincloud/ui/tabs';
import { FinSize } from '@fincloud/ui/types';
import { HIGHLIGHT_PREFIX, REFS_BLOCKS_GROUP_KEY } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { isNil } from 'lodash-es';
import {
  Observable,
  animationFrameScheduler,
  asyncScheduler,
  auditTime,
  map,
  observeOn,
  shareReplay,
  subscribeOn,
  tap,
} from 'rxjs';
import {
  RefsPageActions,
  SearchPageActions,
  TeaserExportPageActions,
} from '../../+state/actions';
import {
  selectAutocomplete,
  selectSharedFinancingStructureView,
} from '../../+state/selectors';
import { OverviewPageNavEnum } from '../../enums/overview-page-nav-enum';
import { ScrollToSectionPayload } from '../../models/scroll-to-section';
import { RefsContentService } from '../../services/refs-content.service';
import { SearchFinancingService } from '../../services/search-financing.service';
import { RefsTeaserModalComponent } from '../refs-teaser-modal/refs-teaser-modal.component';

@Component({
  selector: 'app-refs-shared-financing-structure',
  templateUrl: './refs-shared-financing-structure.component.html',
  styleUrls: ['./refs-shared-financing-structure.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RefsSharedFinancingStructureComponent
  implements AfterViewInit, OnDestroy
{
  @ViewChild('finScrollbar') finScrollbar: FinScrollbarComponent;

  finSize = FinSize;
  finTabType = FinTabType;
  finButtonShape = FinButtonShape;
  finButtonAppearance = FinButtonAppearance;
  finBadgeStatus = FinBadgeStatus;
  actionsButtonLabel = $localize`:@@button.label.actions:Aktionen`;
  highlightPrefix = HIGHLIGHT_PREFIX;

  private scrollToSection$ = this.store
    .select(businessCaseRealEstateFeature.selectScrollToSection)
    .pipe(
      observeOn(animationFrameScheduler),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

  refsBuildingBlocks = REFS_BLOCKS_GROUP_KEY;
  fieldType = FieldTypeEnum;
  businessCasePermissionHtml = BusinessCasePermission;

  // Default is first
  selectedSharedEntityTabIndex = 0;

  sharedWithMeControl = new FormControl();
  searchFormControl = new FormControl('');

  autocomplete$: Observable<FinSearchAutocompleteOption[]> =
    this.store.select(selectAutocomplete);

  getSharedFinancingStructureView$ = this.store.select(
    selectSharedFinancingStructureView,
  );

  searchData$ = this.refsContentService.searchData$;

  firstIdToExpand$ = this.scrollToSection$.pipe(
    map((scrollData) => scrollData.firstId),
  );

  secondIdToExpand$ = this.scrollToSection$.pipe(
    map((scrollData) => scrollData.secondId),
  );

  readonly sidePanelHeight$ = this.store.select(
    selectBusinessCaseSidePanelHeightDefault,
  );

  readonly contentWrapperHeight$ = this.store.select(
    selectBusinessCaseWrapperHeightRealEstateSharedFinanceStructure,
  );

  readonly contentHeight$ = this.store.select(
    selectBusinessCaseContentHeightRealEstateSharedFinanceStructure,
  );

  constructor(
    @Inject(DOCUMENT) private document: Document,
    private store: Store,
    private destroyRef: DestroyRef,
    private refsContentService: RefsContentService,
    private searchFinancingService: SearchFinancingService,
    private finModalService: FinModalService,
    private cssService: CssService,
  ) {}

  ngAfterViewInit(): void {
    this.scrollToSection$
      .pipe(
        tap(({ firstId, secondId, thirdId }) => {
          if (firstId || secondId || thirdId) {
            this.searchFormControl.reset('', { emitEvent: false });
            this.refsContentService.clearSearch();
          }
        }),
        subscribeOn(asyncScheduler),
        auditTime(300),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(({ firstId, secondId, thirdId, highlightId }) => {
        if (highlightId || thirdId || secondId || firstId) {
          const element = this.document.getElementById(
            highlightId || thirdId || secondId || firstId,
          );

          this.finScrollbar.scrollToElement(element, {
            top: 0,
          });
        }
        if (highlightId) {
          this.cssService.removeCssClass('', 'mark');
          this.cssService.addCssClass(highlightId, 'mark');
        }
      });

    this.searchFormControl.valueChanges
      .pipe(auditTime(400), takeUntilDestroyed(this.destroyRef))
      .subscribe((searchTerm: string) => this.dispatchAutocomplete(searchTerm));
  }

  onTabChanged(activeTab: OverviewPageNavEnum) {
    this.store.dispatch(
      RefsPageActions.setOverviewPageActiveTab({ payload: activeTab }),
    );
  }

  onEntityTabChange(selectedSharedEntityTabIndex: number): void {
    this.selectedSharedEntityTabIndex = selectedSharedEntityTabIndex;
    this.store.dispatch(
      StateLibBusinessCaseRealEstatePageActions.getSharedFinancingStructure({
        payload: {
          selectedSharedEntityTabIndex,
        },
      }),
    );
  }

  compareWith(item: unknown, selected: unknown): boolean {
    return item !== selected;
  }

  ngOnDestroy(): void {
    this.store.dispatch(RefsPageActions.clearSharedFinancingStructure());
  }

  onSearchFocus(): void {
    this.refsContentService.setSearchFocus(this.searchFormControl.value);
  }

  clearScrollToSection(): void {
    this.store.dispatch(
      StateLibScrollToSectionPageActions.clearScrollToSection(),
    );
  }

  scrollToExpandedGroup(elementId: string, height: number): void {
    const element = this.document.getElementById(elementId);

    this.finScrollbar.scrollToElement(element, {
      top: this.refsContentService.remToPxConvert(-height),
    });
  }

  scrollToSection(payload: ScrollToSectionPayload): void {
    this.store.dispatch(
      StateLibScrollToSectionPageActions.scrollToSection({
        payload,
      }),
    );
  }

  dispatchSearch(searchTerm: string | undefined): void {
    if (isNil(searchTerm)) {
      return;
    }
    const { canHandle, searchLabels } =
      this.searchFinancingService.labelSearchCriteria(searchTerm);
    if (!canHandle) {
      return;
    }
    const searchCriteria =
      this.searchFinancingService.searchCriteria(searchTerm);
    this.store.dispatch(
      SearchPageActions.searchSharingEntity({
        payload: {
          fieldSearchCriteria: searchCriteria,
          searchLabels,
        },
      }),
    );
  }

  toggleNavigationPanel(): void {
    this.store.dispatch(
      StateLibSideNavigationsPageActions.toggleOpenForPageNavigation(),
    );
  }

  openFinancingTeaserExport(): void {
    this.store.dispatch(TeaserExportPageActions.getMyFinStructureTeaser());
    this.finModalService.open(RefsTeaserModalComponent, {
      size: this.finSize.M,
    });
  }
  private dispatchAutocomplete(searchTerm: string | null): void {
    const { canDispatch } =
      this.searchFinancingService.dispatchAutocomplete(searchTerm);
    if (!canDispatch) {
      this.refsContentService.clearSearch();
      this.searchFormControl.reset('', { emitEvent: false });
      return;
    }
    if (this.searchFinancingService.isBasicSearchCriteriaMet(searchTerm)) {
      this.store.dispatch(
        SearchPageActions.autocompleteSharingEntity({
          payload: {
            searchLabels: [searchTerm],
          },
        }),
      );
    } else {
      this.refsContentService.clearSearch();
    }
  }
}
