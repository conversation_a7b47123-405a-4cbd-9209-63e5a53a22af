@if (getSharedFinancingStructureView$ | async; as data) {
  @if (data.navigaitonGroups.length > 1) {
    <app-sidebar-content-manager
      class="tw-h-full"
      [sidePanelStyles]="{ height: sidePanelHeight$ | async }"
      [pagePanelIsOpen]="data.isPageNavigationOpen"
      (toggle)="toggleNavigationPanel()"
    >
      <ng-template #navPanel>
        <app-refs-side-nav-list></app-refs-side-nav-list>
      </ng-template>

      <ng-template #mainPanel>
        <ng-container *ngTemplateOutlet="main"></ng-container>
      </ng-template>
    </app-sidebar-content-manager>
  } @else {
    <ng-container *ngTemplateOutlet="main"></ng-container>
  }

  <ng-template #main>
    <div
      class="tw-flex tw-flex-col tw-bg-color-surface-primary tw-h-full tw-max-w-full"
    >
      <div class="tw-max-w-full tw-py-[1.6rem] tw-px-[2.4rem]">
        <fin-tabs
          #entityTabs
          [type]="finTabType.TERTIARY"
          [size]="finSize.L"
          [selectedIndex]="selectedSharedEntityTabIndex"
          (selectedIndexChange)="onEntityTabChange($event)"
        >
          @for (
            sharingEntity of data.sharedWithMe;
            track sharingEntity.id;
            let index = $index
          ) {
            <fin-tab>
              <ng-template finTabLabel>
                <span
                  finTruncateText
                  class="tw-max-w-[22.5rem]"
                  [class.tw-text-body-2-semibold]="
                    selectedSharedEntityTabIndex === index
                  "
                  [class.tw-text-body-2-regular]="
                    selectedSharedEntityTabIndex !== index
                  "
                >
                  {{ sharingEntity.name }}
                </span>
              </ng-template>
            </fin-tab>
          }
        </fin-tabs>
      </div>

      @if (searchData$ | async; as searchData) {
        <div
          class="tw-flex tw-flex-row tw-w-full tw-gap-[2.4rem] tw-py-[1.2rem] tw-px-[2.4rem]"
        >
          <fin-search
            class="tw-w-full"
            dynamicErrorSpace="dynamic"
            [formControl]="searchFormControl"
            [showLoader]="searchData.fetchingData"
            [autocomplete]="true"
            [options]="autocomplete$ | async"
            (inputFocus)="onSearchFocus()"
            (autoCompleteOptionChange)="dispatchSearch($event)"
            (pressEnter)="dispatchSearch($event)"
            [numberOfResults]="searchData.matches"
          >
          </fin-search>

          <div>
            <button
              fin-button
              [shape]="finButtonShape.RECTANGLE"
              [size]="finSize.L"
              [appearance]="finButtonAppearance.INFORMATIVE"
              [finActionMenuTrigger]="finMenu.panel"
              #trigger="finActionMenuTrigger"
            >
              <fin-icon name="more_vert" [size]="finSize.L"></fin-icon>
              {{ actionsButtonLabel }}
            </button>

            <fin-actions-menu #finMenu="finActionMenu">
              <button
                fin-menu-item
                [size]="finSize.M"
                iconName="downloading"
                *ngxPermissionsOnly="[
                  businessCasePermissionHtml.BCP_00151,
                  businessCasePermissionHtml.BCP_00152,
                ]"
                (click)="openFinancingTeaserExport()"
              >
                <ng-container finMenuItemTitle>
                  <span i18n="@@dashboard.businessCase.pdfExport">
                    PDF-Export
                  </span>
                </ng-container>
              </button>
            </fin-actions-menu>
          </div>
        </div>
        <fin-scrollbar
          #finScrollbar
          appBusinessCaseMainContentScroll
          [smoothResize]="true"
          [ngStyle]="{ height: contentWrapperHeight$ | async }"
        >
          @if (searchData.hasResults) {
            <app-refs-accordion
              #refsAccordionComponent
              [staticGroups]="data.staticGroups"
              [firstIdToExpand]="firstIdToExpand$ | async"
              [secondIdToExpand]="secondIdToExpand$ | async"
              [closeOthers]="!searchData.groupIds.length"
              [highlightPrefix]="highlightPrefix"
              [matchingToSearchGroupIds]="searchData.groupIds"
              [isEditMode]="false"
              [shouldExpandSubAccordions]="!!searchData.searchTerm.length"
              [ngStyle]="{ 'min-height': contentHeight$ | async }"
              (clearScrollToSection)="clearScrollToSection()"
              (scrollToExpandedGroup)="
                scrollToExpandedGroup($event, data.headerHeight)
              "
            >
              <app-refs-building-blocks
                [activeTab]="data.overviewActiveTab"
                (tabChanged)="onTabChanged($event)"
              ></app-refs-building-blocks>
            </app-refs-accordion>
          } @else {
            <app-refs-no-results></app-refs-no-results>
          }
        </fin-scrollbar>
      }
    </div>
  </ng-template>
}
