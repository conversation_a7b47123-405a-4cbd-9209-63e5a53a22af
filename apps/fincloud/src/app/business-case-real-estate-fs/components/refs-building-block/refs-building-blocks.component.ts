import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { OVERVIEW_PAGE_NAV } from '@fincloud/types/models';
import { FinSwitchToggleOption } from '@fincloud/ui/switch-toggle';
import { OverviewPageNavEnum } from '../../enums/overview-page-nav-enum';

@Component({
  selector: 'app-refs-building-blocks',
  templateUrl: './refs-building-blocks.component.html',
  styleUrls: ['./refs-building-blocks.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RefsBuildingBlocksComponent implements OnDestroy {
  @Input() activeTab: OVERVIEW_PAGE_NAV | string | null;

  formControl = new FormControl(OverviewPageNavEnum.OVERVIEW);
  overviewPageNavEnum = OverviewPageNavEnum;

  tabs: FinSwitchToggleOption[] = [
    {
      label: $localize`:@@dashboard.businessCase.tabs.overview:Übersicht`,
      value: OverviewPageNavEnum.OVERVIEW,
    },
    {
      label: $localize`:@@details.label:Details`,
      value: OverviewPageNavEnum.BUILDING_BLOCKS,
    },
  ];

  @Output() tabChanged = new EventEmitter<OverviewPageNavEnum>();

  ngOnDestroy(): void {
    this.tabChanged.emit(OverviewPageNavEnum.OVERVIEW);
  }

  onSwitchChange(tab: string): void {
    this.activeTab = tab;
    this.tabChanged.emit(
      tab === OverviewPageNavEnum.OVERVIEW
        ? OverviewPageNavEnum.OVERVIEW
        : OverviewPageNavEnum.BUILDING_BLOCKS,
    );
  }
}
