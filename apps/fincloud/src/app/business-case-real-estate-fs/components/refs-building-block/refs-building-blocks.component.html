<div class="tw-ml-[2.4rem] tw-mt-[0.8rem]">
  <fin-switch-toggle
    [stretched]="false"
    [formControl]="formControl"
    [options]="tabs"
    (switchChange)="onSwitchChange($event)"
  ></fin-switch-toggle>
</div>

<hr finHorizontalSeparator class="tw-mx-[-2.4rem] tw-mt-[0.8rem]" />
@if (activeTab) {
  <ng-container
    *ngTemplateOutlet="
      activeTab === overviewPageNavEnum.OVERVIEW
        ? buildingBlocks
        : financingTabs
    "
  ></ng-container>
}

<ng-template #buildingBlocks>
  <app-refs-building-blocks-aggregated-info></app-refs-building-blocks-aggregated-info>
</ng-template>

<ng-template #financingTabs>
  <ng-content></ng-content>
  <app-refs-financing-tabs></app-refs-financing-tabs>
</ng-template>
