import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { setTimeoutUnpatched } from '@fincloud/core/utils';
import { isEmpty, isEqual, isNil } from 'lodash-es';
import {
  BehaviorSubject,
  Observable,
  combineLatest,
  distinctUntilChanged,
  map,
  merge,
  scan,
  shareReplay,
  startWith,
  switchMap,
  tap,
} from 'rxjs';
import { PartnerNameStatus } from '../enums/partner-name-status';
import { RowAction } from '../enums/row-action';
import { ActionButtonsData } from '../models/action-buttons-data';
import { FinancingPartnersState } from '../models/fianancing-partners-saved-state';
import { FinancingPartner } from '../models/financing-partner';
import { RowsData } from '../models/rows-data';
import { EMPTY_FINANCING_PARTNER } from '../utils/emtpy-financing-partner';
import { MAX_ROWS } from '../utils/financing-partners-max-rows';
import { FINANCING_PARTNERS_ROWS_STATE } from '../utils/financing-partners-rows-state';
import { MAX_PARTICIPATION_PERCENT } from '../utils/financing-partners-total-participation-amount';
import { removeAdditionalSpaces } from '../utils/remove-additional-spaces';
import { duplicateFinancingPartnerValidator } from '../validators/duplicate-financing-partner.validator';

@Injectable()
export class RefsFinancingPartnersService {
  private readonly rowsAction$$ = new BehaviorSubject<{
    action: RowAction;
    rows: FormGroup[];
  }>({
    action: RowAction.SET,
    rows: [],
  });

  private readonly rowsState$: Observable<
    typeof FINANCING_PARTNERS_ROWS_STATE
  > = this.rowsAction$$.pipe(
    scan((state, { action, rows }) => {
      switch (action) {
        case RowAction.ADD: {
          state.updatedRows = state.updatedRows.concat(rows);
          break;
        }
        case RowAction.DELETE: {
          const [rowToDelete] = rows;
          state.updatedRows = state.updatedRows.filter(
            (row) => row !== rowToDelete,
          );
          this.handlePercentValidation(state.updatedRows);
          this.triggerAllPartnersNamesValidation(state.updatedRows);
          break;
        }
        case RowAction.SET: {
          state.initialRows = new FinancingPartnersState(
            rows.map((row) => row.getRawValue()),
          );
          state.updatedRows = rows;
          break;
        }
        case RowAction.RESET: {
          state.updatedRows = state.initialRows
            .getState()
            .map((row) =>
              this.createRowFormGroup(row, PartnerNameStatus.INACTIVE),
            );
          this.handlePercentValidation(state.updatedRows);
          break;
        }
        case RowAction.ENABLE: {
          const [rowToEnable] = rows;
          rowToEnable.controls.participationPercent.enable();
          rowToEnable.patchValue({ participationPercent: 0 });
          this.handlePercentValidation(state.updatedRows);
          break;
        }
        case RowAction.DISABLE: {
          const [rowToDisable] = rows;
          rowToDisable.controls.participationPercent.disable();
          rowToDisable.patchValue({ participationPercent: null });
          this.handlePercentValidation(state.updatedRows);
          break;
        }
        case RowAction.VALIDATE: {
          this.handlePercentValidation(state.updatedRows);
          this.triggerAllPartnersNamesValidation(state.updatedRows);
          break;
        }
        case RowAction.RESET_WITH_VALIDATION: {
          state.updatedRows = state.initialRows
            .getState()
            .map((row) =>
              this.createRowFormGroup(row, PartnerNameStatus.INACTIVE),
            );
          //TODO: N.Topalov check why errors are cleared
          setTimeoutUnpatched(() => {
            this.handlePercentValidation(state.updatedRows);
          }, 0);
          break;
        }
        default: {
          break;
        }
      }
      return {
        ...state,
        isBelowTreshold: state.updatedRows.length < MAX_ROWS,
      };
    }, FINANCING_PARTNERS_ROWS_STATE),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  private readonly rowsChanges$: Observable<FormGroup> = this.rowsState$.pipe(
    map((state) => state.updatedRows),
    switchMap((rows) =>
      merge(
        ...rows.map((row) =>
          row.controls.partnerName.valueChanges.pipe(
            tap(() => {
              this.handlePartnerNameValidation(row);
              this.triggerAllPartnersNamesValidation(rows);
            }),
          ),
        ),
        ...rows.map((row) =>
          row.controls.participationPercent.valueChanges.pipe(
            tap(() => this.handlePercentValidation(rows)),
          ),
        ),
      ),
    ),
  );

  readonly partnersNames$: Observable<string[]> = this.rowsState$.pipe(
    map((state) =>
      state.updatedRows.map((row) =>
        removeAdditionalSpaces(row.controls.partnerName.value),
      ),
    ),
    startWith([]),
  );

  readonly hasPopulatedPartnerName$: Observable<boolean> = this.rowsState$.pipe(
    map((data) =>
      data.updatedRows.some((formGroup: FormGroup) => {
        const partnerNameControlValue = formGroup.controls.partnerName?.value;
        return !!partnerNameControlValue;
      }),
    ),
    distinctUntilChanged(),
  );

  readonly actionButtonsData$: Observable<ActionButtonsData> = merge(
    this.rowsChanges$,
    this.rowsState$,
  ).pipe(
    switchMap(() =>
      this.rowsState$.pipe(
        map((state) => {
          const initialValues = state.initialRows.getState();
          const latestValues = state.updatedRows.map((group) =>
            group.getRawValue(),
          );
          return {
            hasChanges: !isEqual(initialValues, latestValues),
            totalParticipationPercent: latestValues.reduce(
              (total, partner) => (total += partner.participationPercent),
              0,
            ),
            canAddNewPartner: state.isBelowTreshold,
          };
        }),
      ),
    ),
    distinctUntilChanged(),
  );

  readonly rowsData$: Observable<RowsData> = combineLatest([
    this.rowsState$,
    this.partnersNames$,
    this.actionButtonsData$,
    this.hasPopulatedPartnerName$,
  ]).pipe(
    map(
      ([state, partnersNames, actionButtonsData, hasPopulatedPartnerName]) => {
        return {
          rows: state.updatedRows,
          hasInvalidPartnerName: state.updatedRows.some(
            (row) => row.controls.partnerName.invalid,
          ),
          partnersNames,
          actionButtonsData,
          hasPopulatedPartnerName,
        };
      },
    ),
  );

  constructor(private formBuilder: FormBuilder) {}

  triggerValidation() {
    this.rowsAction$$.next({
      action: RowAction.VALIDATE,
      rows: [],
    });
  }

  setState(rowData: FinancingPartner[]): void {
    this.rowsAction$$.next({
      action: RowAction.SET,
      rows: rowData.map((row) =>
        this.createRowFormGroup(row, PartnerNameStatus.INACTIVE),
      ),
    });
  }

  enableParticipationPercent(rowToUpdate: FormGroup) {
    if (
      rowToUpdate.controls.partnerName.valid &&
      rowToUpdate.controls.partnerName.value
    ) {
      this.rowsAction$$.next({
        action: RowAction.ENABLE,
        rows: [rowToUpdate],
      });
    }
  }

  disableParticipationPercent(rowToDisable: FormGroup) {
    this.rowsAction$$.next({
      action: RowAction.DISABLE,
      rows: [rowToDisable],
    });
  }

  addFormRows(): void {
    const newRow = this.createRowFormGroup(
      EMPTY_FINANCING_PARTNER,
      PartnerNameStatus.ACTIVE,
    );

    newRow.controls.partnerName.addAsyncValidators(
      duplicateFinancingPartnerValidator(this.partnersNames$),
    );

    newRow.controls.partnerName.markAsTouched();
    newRow.controls.partnerName.updateValueAndValidity();

    this.rowsAction$$.next({
      action: RowAction.ADD,
      rows: [newRow],
    });
  }

  getNonEmptyFormRows(rows: FormGroup[]): FinancingPartner[] {
    const nonEmptyFormRows = rows
      .map((row) => {
        const rowData = row.getRawValue();
        return {
          ...rowData,
          partnerName: removeAdditionalSpaces(rowData.partnerName),
        };
      })
      .filter((row) => {
        return Object.values(row).some(
          (value) => !isNil(value) && !isEmpty(value),
        );
      });
    return nonEmptyFormRows;
  }

  deleteFormRow(rowToDelete: FormGroup): void {
    this.rowsAction$$.next({ action: RowAction.DELETE, rows: [rowToDelete] });
  }

  resetSnapshot(): void {
    this.rowsAction$$.next({ action: RowAction.RESET, rows: [] });
  }

  resetWithValidation(): void {
    this.rowsAction$$.next({
      action: RowAction.RESET_WITH_VALIDATION,
      rows: [],
    });
  }

  private createRowFormGroup(
    data: FinancingPartner,
    partnerNameStatus: PartnerNameStatus,
  ) {
    const inactive = partnerNameStatus === PartnerNameStatus.INACTIVE;
    return this.formBuilder.group({
      partnerName: this.formBuilder.control({
        value: data.partnerName,
        disabled: inactive,
      }),
      participationPercent: this.formBuilder.control({
        value:
          !inactive && data.partnerName === ''
            ? null
            : data.participationPercent,
        disabled: !data.partnerName,
      }),
      participationAmount: this.formBuilder.control(
        inactive ? data.participationAmount : null,
      ),
    });
  }

  private triggerAllPartnersNamesValidation(partners: FormGroup[]): void {
    partners.forEach((partner) => {
      partner.controls.partnerName.updateValueAndValidity({
        onlySelf: true,
        emitEvent: false,
      });
    });
  }

  private handlePartnerNameValidation(group: FormGroup): void {
    const percentControl = group.controls.participationPercent;
    const partnerName = group.controls.partnerName.getRawValue();

    if (!partnerName && percentControl.enabled) {
      percentControl.setValue(0);
      //TODO: N.Topalov check why disabled status is cleared
      setTimeout(() => percentControl.disable(), 0);
    }
  }

  private handlePercentValidation(partners: FormGroup[]): void {
    const totalPercent = partners
      .map((partner) => partner.getRawValue())
      .reduce(
        (total, partner) => (total += partner.participationPercent || 0),
        0,
      );

    if (totalPercent !== MAX_PARTICIPATION_PERCENT) {
      partners.forEach((partner) => {
        partner.controls.participationPercent.setErrors({
          notEqual: true,
        });
        partner.controls.participationPercent.markAsTouched();
      });
    } else {
      partners.forEach((partner) =>
        partner.controls.participationPercent.setErrors(null),
      );
    }
  }
}
