import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FolderContentBase } from '../../directives/folder-content-base.directive';

@Component({
  selector: 'app-folder-content-grid-view',
  templateUrl: './folder-content-grid-view.component.html',
  styleUrl: './folder-content-grid-view.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FolderContentGridViewComponent extends FolderContentBase {
  handleShowFolderInEnclosingFolder(event: MouseEvent, folderId: string): void {
    event.stopPropagation();
    this.emitShowFolderInEnclosingFolder(folderId);
  }
}
