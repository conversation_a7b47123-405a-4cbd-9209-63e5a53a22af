<!-- Max height of scrollable content -->
<fin-scrollbar
  class="tw-max-h-[40.2rem]"
  [id]="documentGroupScrollContainerPrefix + groupKey"
>
  <div
    class="tw-flex tw-flex-col tw-gap-[2.4rem] tw-px-[2.4rem] tw-pb-[0.1rem]"
  >
    @if (folder.children?.length) {
      <div
        class="tw-grid tw-grid-cols-[repeat(auto-fill,_minmax(26rem,_1fr))] tw-gap-[2.4rem] tw-pointer-events-none"
      >
        @for (
          subFolder of folder.children;
          track subFolder.id;
          let index = $index
        ) {
          @defer (on viewport; when highlight?.groupKey === groupKey) {
            <fin-directory
              [id]="documentGroupItemPrefix + subFolder.id"
              class="tw-pointer-events-auto"
              (click)="emitSubfolderClicked(subFolder, index)"
            >
              <ng-container finSuffix>
                <div
                  finTruncateText
                  [openDelay]="0"
                  class="tw-text-body-2-strong"
                >
                  {{ subFolder.name }}
                </div>

                <div class="tw-flex tw-justify-between tw-items-center">
                  <div class="tw-text-body-3-moderate">
                    <ui-element-count
                      [count]="
                        (subFolder.children?.length ?? 0) +
                        (subFolder.fields?.length ?? 0)
                      "
                    >
                    </ui-element-count>
                  </div>
                  @if (editMode) {
                    <div class="tw-flex">
                      <button
                        fin-button-action
                        [size]="finSize.S"
                        (click)="$event.stopPropagation()"
                        [finActionMenuTrigger]="finMenu.panel"
                      >
                        <fin-icon name="more_vert"></fin-icon>
                      </button>

                      <fin-actions-menu #finMenu="finActionMenu">
                        @if (searchMode || filterMode) {
                          <button
                            fin-menu-item
                            iconName="content_paste_go"
                            [size]="finSize.M"
                            (click)="
                              handleShowFolderInEnclosingFolder(
                                $event,
                                subFolder.id
                              )
                            "
                          >
                            <ng-container
                              finMenuItemTitle
                              i18n="@@folderStructure.actions.showInEnclosing"
                            >
                              Im beigefügten Ordner anzeigen
                            </ng-container>
                          </button>
                        }
                        <button
                          fin-menu-item
                          [size]="finSize.M"
                          iconName="visibility"
                          (click)="showFolderDetails(subFolder)"
                        >
                          <ng-container finMenuItemTitle i18n="@@details.label">
                            Details
                          </ng-container>
                        </button>
                        <button
                          fin-menu-item
                          [size]="finSize.M"
                          iconName="edit"
                          (click)="renameFolder(subFolder)"
                        >
                          <ng-container
                            finMenuItemTitle
                            i18n="@@folderStructure.folder.actions.rename"
                          >
                            Umbenennen
                          </ng-container>
                        </button>
                        @if (!filterMode) {
                          <button
                            fin-menu-item
                            [size]="finSize.M"
                            iconName="drive_file_move"
                            (click)="moveFolder(subFolder.id)"
                          >
                            <ng-container
                              finMenuItemTitle
                              i18n="@@folderStructure.folder.actions.move"
                            >
                              Verschieben
                            </ng-container>
                          </button>
                          <button
                            fin-menu-item
                            [size]="finSize.M"
                            iconName="delete"
                            attention="true"
                            (click)="deleteFolder(subFolder.id)"
                          >
                            <ng-container
                              finMenuItemTitle
                              i18n="@@folderStructure.folder.actions.delete"
                            >
                              Löschen
                            </ng-container>
                          </button>
                        }
                      </fin-actions-menu>
                    </div>
                  }
                  @if ((searchMode || filterMode) && !editMode) {
                    <button
                      (click)="
                        handleShowFolderInEnclosingFolder($event, subFolder.id)
                      "
                    >
                      <fin-icon
                        finTooltip
                        i18n-content="@@folderStructure.actions.showInEnclosing"
                        content="Im beigefügten Ordner anzeigen"
                        class="tw-mr-[0.4rem]"
                        name="content_paste_go"
                      ></fin-icon>
                    </button>
                  }
                </div>
              </ng-container>
            </fin-directory>
          } @placeholder {
            <!-- Height of a folder card in grid view -->
            <div class="tw-h-[7.2rem]"></div>
          }
        }
      </div>
    }

    @if (documentFields?.length) {
      <div
        class="tw-grid tw-grid-cols-[repeat(auto-fill,_minmax(26rem,_1fr))] tw-gap-[2.4rem] tw-pointer-events-none"
      >
        @for (templateField of documentFields; track templateField.field.key) {
          <ui-template-field
            class="tw-pointer-events-auto"
            [field]="templateField.field"
            [information]="templateField.information"
            [isEditMode]="editMode"
            [isSearchMode]="searchMode"
            [isFilterMode]="filterMode"
            [templateFieldData]="documentTemplateFieldData"
            [hasDracoonSynchronization]="isServiceSynchronizedWithDracoon"
            [isServiceSynchronizedWithNextfolder]="
              isServiceSynchronizedWithNextfolder
            "
            [nextfolderDocuments]="nextfolderDocuments"
            (fileUpload)="emitFileUpload($event)"
            (fieldDeleted)="emitFieldDeleted(templateField.information)"
            (moveModalOpen)="moveDocument($event)"
            (openModalRequested)="
              emitOpenModalRequested($event.modalTab, templateField.field)
            "
            (showInEnclosingFolder)="emitShowDocumentInEnclosingFolder($event)"
            (valueChanged)="emitDocumentFieldValueChanged($event)"
          >
          </ui-template-field>
        }
      </div>
    }
  </div>
</fin-scrollbar>
