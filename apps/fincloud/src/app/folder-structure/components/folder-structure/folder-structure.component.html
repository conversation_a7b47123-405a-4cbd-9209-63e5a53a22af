@if (
  (editMode || rootFolder.children?.length || rootFolder.fields?.length) &&
    !(
      searchMode &&
      !rootFolder.children?.length &&
      !rootFolder.fields?.length
    ) &&
    currentFolder$ | async;
  as currentFolder
) {
  <div
    [id]="documentGroupPrefix + groupKey"
    class="tw-relative tw-max-h-[50.4rem] tw-flex tw-flex-col tw-border tw-rounded-[0.4rem] tw-border-color-border-default-primary tw-py-8 tw-gap-[0.4rem]"
  >
    <div
      class="tw-flex tw-justify-between tw-items-start tw-gap-[2.4rem] tw-px-[2.4rem]"
    >
      <div
        class="tw-flex tw-flex-col tw-gap-[0.6rem] tw-pointer-events-none tw-overflow-hidden"
      >
        <div class="tw-pr-[2.4rem] {{ documentGroupPrefix + groupKey }}">
          @if (currentFolder.breadcrumbs.length === 1) {
            <h3 class="heading6" i18n="@@folderStructure.sectionTitle">
              Dokumente
            </h3>
          } @else {
            <fin-breadcrumb
              class="tw-pointer-events-auto"
              [items]="currentFolder.breadcrumbs"
              (breadcrumbClicked)="onBreadcrumbClick($event)"
            ></fin-breadcrumb>
          }
        </div>

        @if (
          (currentFolder.children?.length || currentFolder.fields?.length) &&
          editMode
        ) {
          @if (searchMode || filterMode) {
            <p class="heading8 tw-text-color-text-tertiary">
              {{ filterMode | executeFunc: getEmptyStateMessage : searchMode }}
            </p>
          } @else {
            <p
              class="heading8 tw-text-color-text-tertiary"
              i18n="@@folderStructure.dragDropPlaceholder"
            >
              Fügen Sie Dokumente und Ordner per Drag & Drop aus der linken
              Spalte hinzu oder laden Sie eine Datei hoch
            </p>
          }
        }
      </div>
      <!-- For now, only the business case data room should support list view of the documents section -->
      @if (context === folderStructureContext.BUSINESS_CASE) {
        <div [formGroup]="viewModeForm">
          <fin-switch-toggle
            [options]="viewModeOptions"
            formControlName="viewMode"
          >
          </fin-switch-toggle>
        </div>
      }
    </div>

    @if (!currentFolder.children?.length && !currentFolder.fields?.length) {
      @if (isServiceSynchronizedWithNextfolder) {
        <fin-warning-message
          [label]="warningMessageLabel"
          [showIcon]="true"
          [iconSrc]="'assets/svg/svgNextfolderInformationIcon.svg'"
          [appearance]="finWarningMessageAppearance.WARNING"
        >
        </fin-warning-message>
      }

      @if (searchMode || filterMode) {
        <fin-empty-state
          [type]="finEmptyStateType.BASIC"
          class="!tw-bg-transparent"
          [title]="filterMode | executeFunc: getEmptyStateMessage : searchMode"
        >
          <ng-template #finIcon>
            <fin-icon
              name="description"
              class="tw-text-color-icons-tertiary"
              [size]="finSize.L"
            ></fin-icon>
          </ng-template>
        </fin-empty-state>
      } @else {
        <fin-empty-state
          [type]="finEmptyStateType.BASIC"
          [title]="emptyStateTitle"
          class="!tw-bg-transparent"
        >
          <ng-template #finIcon>
            <fin-icon
              name="description"
              class="tw-text-color-icons-tertiary"
              [size]="finSize.L"
            ></fin-icon>
          </ng-template>
        </fin-empty-state>
      }
    } @else {
      <app-folder-content-wrapper
        [groupKey]="groupKey"
        [folder]="currentFolder"
        [documentFields]="documentFields"
        [documentTemplateFieldData]="documentTemplateFieldData"
        [isServiceSynchronizedWithDracoon]="isServiceSynchronizedWithDracoon"
        [isServiceSynchronizedWithNextfolder]="
          isServiceSynchronizedWithNextfolder
        "
        [nextfolderDocuments]="nextfolderDocuments"
        [viewMode]="viewModeForm.controls.viewMode.value"
        [editMode]="editMode"
        [searchMode]="searchMode"
        [filterMode]="filterMode"
        [highlight]="highlight"
        (subfolderClicked)="navigateToSubfolder($event)"
        (showFolderInEnclosingFolder)="emitShowInEnclosingFolder($event)"
        (fileUpload)="emitFileUpload($event)"
        (fieldDeleted)="emitFieldDeleted($event)"
        (openModalRequested)="
          emitOpenModalRequested($event.modalTab, $event.field)
        "
        (documentFieldValueChanged)="emitDocumentFieldValueChanged($event)"
        (showDocumentInEnclosingFolder)="
          emitShowDocumentInEnclosingFolder($event)
        "
      >
      </app-folder-content-wrapper>
    }
  </div>
}
