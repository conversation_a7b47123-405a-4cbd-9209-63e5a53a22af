@if (viewMode === folderStructureViewMode.GRID) {
  <app-folder-content-grid-view
    [groupKey]="groupKey"
    [folder]="folder"
    [highlight]="highlight"
    [documentFields]="documentFields"
    [documentTemplateFieldData]="documentTemplateFieldData"
    [isServiceSynchronizedWithDracoon]="isServiceSynchronizedWithDracoon"
    [isServiceSynchronizedWithNextfolder]="isServiceSynchronizedWithNextfolder"
    [nextfolderDocuments]="nextfolderDocuments"
    [editMode]="editMode"
    [searchMode]="searchMode"
    [filterMode]="filterMode"
    (subfolderClicked)="emitSubfolderClicked($event)"
    (showFolderInEnclosingFolder)="emitShowInEnclosingFolder($event)"
    (fileUpload)="emitFileUpload($event)"
    (fieldDeleted)="emitFieldDeleted($event)"
    (openModalRequested)="emitOpenModalRequested($event.modalTab, $event.field)"
    (documentFieldValueChanged)="emitDocumentFieldValueChanged($event)"
    (showDocumentInEnclosingFolder)="emitShowDocumentInEnclosingFolder($event)"
  >
  </app-folder-content-grid-view>
} @else {
  <app-folder-content-list-view
    [groupKey]="groupKey"
    [folder]="folder"
    [highlight]="highlight"
    [documentFields]="documentFields"
    [documentTemplateFieldData]="documentTemplateFieldData"
    [isServiceSynchronizedWithDracoon]="isServiceSynchronizedWithDracoon"
    [isServiceSynchronizedWithNextfolder]="isServiceSynchronizedWithNextfolder"
    [nextfolderDocuments]="nextfolderDocuments"
    [editMode]="editMode"
    [searchMode]="searchMode"
    [filterMode]="filterMode"
    (subfolderClicked)="emitSubfolderClicked($event)"
    (showFolderInEnclosingFolder)="emitShowInEnclosingFolder($event)"
    (fileUpload)="emitFileUpload($event)"
    (fieldDeleted)="emitFieldDeleted($event)"
    (openModalRequested)="emitOpenModalRequested($event.modalTab, $event.field)"
    (documentFieldValueChanged)="emitDocumentFieldValueChanged($event)"
    (showDocumentInEnclosingFolder)="emitShowDocumentInEnclosingFolder($event)"
  >
  </app-folder-content-list-view>
}
