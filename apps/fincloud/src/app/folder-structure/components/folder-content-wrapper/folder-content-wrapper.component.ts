import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import {
  FieldDtoWithIndex,
  TemplateFieldViewModel,
} from '@fincloud/core/business-case';
import { FolderStructureViewMode } from '@fincloud/neoshare/folder-structure';
import {
  Folder,
  Information,
} from '@fincloud/swagger-generator/business-case-manager';
import { Information as CompanyInformation } from '@fincloud/swagger-generator/company';
import { NextFolderDocument } from '@fincloud/swagger-generator/document';
import {
  DataRoomHighlight,
  DataRoomTemplateFieldData,
  ValueChangeModel,
} from '@fincloud/types/models';
import { FinSize } from '@fincloud/ui/types';
import { SubfolderClickedEvent } from '../../models/subfolder-clicked-event';

@Component({
  selector: 'app-folder-content-wrapper',
  templateUrl: './folder-content-wrapper.component.html',
  styleUrl: './folder-content-wrapper.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FolderContentWrapperComponent {
  @Input() groupKey: string;
  @Input() folder: Folder;
  @Input() highlight: DataRoomHighlight;

  @Input() documentFields: TemplateFieldViewModel[];
  @Input() documentTemplateFieldData: DataRoomTemplateFieldData;
  @Input() isServiceSynchronizedWithDracoon: boolean;
  @Input() isServiceSynchronizedWithNextfolder: boolean;
  @Input() nextfolderDocuments: NextFolderDocument[];

  @Input() viewMode: FolderStructureViewMode;
  @Input() editMode: boolean;
  @Input() searchMode: boolean;
  @Input() filterMode: boolean;

  @Output() subfolderClicked = new EventEmitter<SubfolderClickedEvent>();
  @Output() showFolderInEnclosingFolder = new EventEmitter<string>();

  @Output() fileUpload = new EventEmitter<ValueChangeModel>();
  @Output() fieldDeleted = new EventEmitter<Information | CompanyInformation>();
  @Output() openModalRequested = new EventEmitter<{
    modalTab: 'edit' | 'revisions';
    field: FieldDtoWithIndex;
  }>();
  @Output() showDocumentInEnclosingFolder = new EventEmitter<string>();
  @Output() documentFieldValueChanged = new EventEmitter<ValueChangeModel>();

  readonly finSize = FinSize;
  readonly folderStructureViewMode = FolderStructureViewMode;

  emitSubfolderClicked({ folder, index }: SubfolderClickedEvent): void {
    this.subfolderClicked.emit({ folder, index });
  }

  emitShowInEnclosingFolder(folderId: string) {
    this.showFolderInEnclosingFolder.emit(folderId);
  }

  emitFileUpload(event: ValueChangeModel): void {
    this.fileUpload.emit(event);
  }

  emitFieldDeleted(information: Information | CompanyInformation): void {
    this.fieldDeleted.emit(information);
  }

  emitOpenModalRequested(
    modalTab: 'edit' | 'revisions',
    field: FieldDtoWithIndex,
  ): void {
    this.openModalRequested.emit({
      modalTab,
      field,
    });
  }

  emitShowDocumentInEnclosingFolder(fieldKey: string): void {
    this.showDocumentInEnclosingFolder.emit(fieldKey);
  }

  emitDocumentFieldValueChanged(valueChange: ValueChangeModel): void {
    this.documentFieldValueChanged.emit(valueChange);
  }
}
