<div
  class="tw-flex tw-items-center tw-gap-[2.4rem] tw-w-full tw-h-[4.4rem] tw-px-[1.2rem] tw-border tw-border-color-border-default-primary tw-rounded-[0.4rem]"
>
  <div
    class="tw-text-body-2-strong tw-text-color-text-tertiary tw-flex-grow tw-flex-shrink tw-w-full"
  >
    <div
      class="tw-flex tw-justify-start tw-items-center tw-gap-[1.2rem] tw-w-fit tw-cursor-pointer tw-select-none"
      (click)="sortByColumn(folderStructureListViewColumn.NAME)"
    >
      <span i18n="@@folderStructure.listView.columns.name">Name</span>
      <ng-container
        *ngTemplateOutlet="
          sortArrowsTemplate;
          context: { columnName: folderStructureListViewColumn.NAME }
        "
      ></ng-container>
    </div>
  </div>
  @if (
    documentTemplateFieldData.folderStructureListViewDisplayOptions?.fileSize
  ) {
    <div
      class="tw-text-body-2-strong tw-text-color-text-tertiary"
      [style.min-width.px]="listViewColumnSizeList.fileSize"
    >
      <div
        class="tw-flex tw-justify-start tw-items-center tw-gap-[1.2rem] tw-w-fit tw-cursor-pointer tw-select-none"
        (click)="sortByColumn(folderStructureListViewColumn.SIZE)"
      >
        <span i18n="@@folderStructure.listView.columns.fileSize">
          Dateigröße
        </span>
        <ng-container
          *ngTemplateOutlet="
            sortArrowsTemplate;
            context: { columnName: folderStructureListViewColumn.SIZE }
          "
        ></ng-container>
      </div>
    </div>
  }
  @if (
    documentTemplateFieldData.folderStructureListViewDisplayOptions?.dateUpdated
  ) {
    <div
      class="tw-text-body-2-strong tw-text-color-text-tertiary"
      [style.min-width.px]="listViewColumnSizeList.dateUpdated"
    >
      <div
        class="tw-flex tw-justify-start tw-items-center tw-gap-[1.2rem] tw-w-fit tw-cursor-pointer tw-select-none"
        (click)="sortByColumn(folderStructureListViewColumn.DATE_UPDATED)"
      >
        <span i18n="@@folderStructure.listView.columns.dateUpdated">
          Aktualisiert am
        </span>
        <ng-container
          *ngTemplateOutlet="
            sortArrowsTemplate;
            context: { columnName: folderStructureListViewColumn.DATE_UPDATED }
          "
        ></ng-container>
      </div>
    </div>
  }
  <div [style.min-width.px]="listViewColumnSizeList.actions">
    <!--Empty column placeholder-->
  </div>
</div>
<!-- Max height of scrollable content - 37rem -->
<fin-scrollbar
  class="tw-max-h-[37rem]"
  [id]="documentGroupScrollContainerPrefix + groupKey"
>
  <div class="tw-pt-[1px]">
    @if (folder.children?.length) {
      @for (
        subFolder of folder.children;
        track subFolder.id;
        let index = $index
      ) {
        @defer (on viewport; when highlight?.groupKey === groupKey) {
          <!-- Reduce the width of the row to 100% - 6px to account for the potential
          border radius of the rows above/below the current one
          when they are in selected state and overlap the current row border
          -->
          <div
            class="tw-flex tw-items-center tw-gap-[2.4rem] tw-w-[calc(100%-6px)] tw-h-[4.4rem] tw-px-[1.2rem] tw-mx-auto tw-border-b tw-border-color-border-default-minimal hover:tw-bg-color-hover-neutral"
            [id]="documentGroupItemPrefix + subFolder.id"
          >
            <div
              class="tw-flex tw-justify-start tw-items-center tw-gap-[1.2rem] tw-flex-grow tw-flex-shrink tw-cursor-pointer tw-w-full tw-overflow-hidden"
              (click)="emitSubfolderClicked(subFolder, index)"
            >
              <fin-icon
                src="assets/svg/directory.svg"
                [size]="finSize.L"
              ></fin-icon>
              <div class="tw-text-body-2-strong" finTruncateText>
                {{ subFolder.name }}
              </div>
            </div>
            @if (
              documentTemplateFieldData.folderStructureListViewDisplayOptions
                ?.fileSize
            ) {
              <div
                class="tw-text-body-2-moderate"
                [style.min-width.px]="listViewColumnSizeList.fileSize"
              >
                <ui-element-count
                  [count]="
                    (subFolder.children?.length ?? 0) +
                    (subFolder.fields?.length ?? 0)
                  "
                >
                </ui-element-count>
              </div>
            }
            @if (
              documentTemplateFieldData.folderStructureListViewDisplayOptions
                ?.dateUpdated
            ) {
              <div
                class="tw-text-body-2-moderate"
                [style.min-width.px]="listViewColumnSizeList.dateUpdated"
              >
                {{ subFolder.lastModifiedDate | date: dateTimeFormat }}
              </div>
            }
            <div
              class="tw-flex tw-justify-end tw-items-center"
              [style.min-width.px]="listViewColumnSizeList.actions"
            >
              @if (editMode) {
                <div class="tw-flex">
                  <button
                    fin-button-action
                    [size]="finSize.S"
                    (click)="$event.stopPropagation()"
                    [finActionMenuTrigger]="finMenu.panel"
                  >
                    <fin-icon name="more_vert"></fin-icon>
                  </button>

                  <fin-actions-menu #finMenu="finActionMenu">
                    @if (searchMode || filterMode) {
                      <button
                        fin-menu-item
                        iconName="content_paste_go"
                        [size]="finSize.M"
                        (click)="emitShowFolderInEnclosingFolder(subFolder.id)"
                      >
                        <ng-container
                          finMenuItemTitle
                          i18n="@@folderStructure.actions.showInEnclosing"
                        >
                          Im beigefügten Ordner anzeigen
                        </ng-container>
                      </button>
                    }
                    <button
                      fin-menu-item
                      [size]="finSize.M"
                      iconName="visibility"
                      (click)="showFolderDetails(subFolder)"
                    >
                      <ng-container finMenuItemTitle i18n="@@details.label">
                        Details
                      </ng-container>
                    </button>
                    <button
                      fin-menu-item
                      [size]="finSize.M"
                      iconName="edit"
                      (click)="renameFolder(subFolder)"
                    >
                      <ng-container
                        finMenuItemTitle
                        i18n="@@folderStructure.folder.actions.rename"
                      >
                        Umbenennen
                      </ng-container>
                    </button>
                    @if (!filterMode) {
                      <button
                        fin-menu-item
                        [size]="finSize.M"
                        iconName="drive_file_move"
                        (click)="moveFolder(subFolder.id)"
                      >
                        <ng-container
                          finMenuItemTitle
                          i18n="@@folderStructure.folder.actions.move"
                        >
                          Verschieben
                        </ng-container>
                      </button>
                      <button
                        fin-menu-item
                        [size]="finSize.M"
                        iconName="delete"
                        attention="true"
                        (click)="deleteFolder(subFolder.id)"
                      >
                        <ng-container
                          finMenuItemTitle
                          i18n="@@folderStructure.folder.actions.delete"
                        >
                          Löschen
                        </ng-container>
                      </button>
                    }
                  </fin-actions-menu>
                </div>
              }
              @if ((searchMode || filterMode) && !editMode) {
                <button (click)="emitShowFolderInEnclosingFolder(subFolder.id)">
                  <fin-icon
                    [size]="finSize.S"
                    finTooltip
                    i18n-content="@@folderStructure.actions.showInEnclosing"
                    content="Im beigefügten Ordner anzeigen"
                    name="content_paste_go"
                  ></fin-icon>
                </button>
              }
            </div>
          </div>
        } @placeholder {
          <!-- Height of a folder row in list view -->
          <div class="tw-h-[4.4rem]"></div>
        }
      }
    }
    @if (documentFields?.length) {
      @for (templateField of documentFields; track templateField.field.key) {
        <ui-template-field
          class="tw-pointer-events-auto"
          [field]="templateField.field"
          [information]="templateField.information"
          [isEditMode]="editMode"
          [isSearchMode]="searchMode"
          [isFilterMode]="filterMode"
          [templateFieldData]="documentTemplateFieldData"
          [hasDracoonSynchronization]="isServiceSynchronizedWithDracoon"
          [isServiceSynchronizedWithNextfolder]="
            isServiceSynchronizedWithNextfolder
          "
          [nextfolderDocuments]="nextfolderDocuments"
          [viewMode]="templateFieldViewMode.INLINE_ROW"
          (fileUpload)="emitFileUpload($event)"
          (fieldDeleted)="emitFieldDeleted(templateField.information)"
          (moveModalOpen)="moveDocument($event)"
          (openModalRequested)="
            emitOpenModalRequested($event.modalTab, templateField.field)
          "
          (showInEnclosingFolder)="emitShowDocumentInEnclosingFolder($event)"
          (valueChanged)="emitDocumentFieldValueChanged($event)"
        >
        </ui-template-field>
      }
    }
  </div>
</fin-scrollbar>

<ng-template #sortArrowsTemplate let-columnName="columnName">
  <fin-icon
    [src]="columnName | executeFunc: getSortIconSrc : (currentSort$ | async)"
    [size]="finSize.S"
  ></fin-icon>
</ng-template>
