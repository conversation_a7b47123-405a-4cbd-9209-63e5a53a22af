import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { RegionalSettingsService } from '@fincloud/core/regional-settings';
import {
  StateLibFolderStructurePageActions,
  folderStructureFeature,
} from '@fincloud/state/folder-structure';
import {
  FolderStructureListViewColumn,
  TemplateFieldViewMode,
} from '@fincloud/types/enums';
import { FolderListViewSort } from '@fincloud/types/models';
import { FOLDER_STRUCTURE_LIST_VIEW_COLUMN_SIZE_LIST } from '@fincloud/utils';
import { Store } from '@ngrx/store';
import { isEqual } from 'lodash-es';
import { Observable, distinctUntilChanged, map, shareReplay } from 'rxjs';
import { FolderContentBase } from '../../directives/folder-content-base.directive';

@Component({
  selector: 'app-folder-content-list-view',
  templateUrl: './folder-content-list-view.component.html',
  styleUrl: './folder-content-list-view.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FolderContentListViewComponent extends FolderContentBase {
  private store = inject(Store);
  private regionalSettingsService = inject(RegionalSettingsService);

  readonly dateTimeFormat = `${this.regionalSettingsService.dateFormat} HH:mm`;

  readonly templateFieldViewMode = TemplateFieldViewMode;
  readonly folderStructureListViewColumn = FolderStructureListViewColumn;
  readonly listViewColumnSizeList = FOLDER_STRUCTURE_LIST_VIEW_COLUMN_SIZE_LIST;

  currentSort$: Observable<FolderListViewSort> = this.store
    .select(folderStructureFeature.selectSortingPerGroup)
    .pipe(
      map((sortingPerGroup) => sortingPerGroup[this.groupKey]),
      distinctUntilChanged(isEqual),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

  sortByColumn(columnName: FolderStructureListViewColumn): void {
    this.store.dispatch(
      StateLibFolderStructurePageActions.setFolderStructureSort({
        groupKey: this.groupKey,
        columnName,
      }),
    );
  }

  getSortIconSrc(
    columnName: FolderStructureListViewColumn,
    currentSort: FolderListViewSort,
  ): string {
    if (currentSort && currentSort.prop === columnName) {
      return currentSort.dir === 'asc'
        ? '/assets/svg/arrows/arrow-up.svg'
        : '/assets/svg/arrows/arrow-down.svg';
    }

    return '/assets/arrows/double-arrows.svg';
  }
}
