import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { ElementCountComponent } from '@fincloud/components/element-count';
import { NsCoreDateModule } from '@fincloud/core/date';
import { NsCoreDirectivesModule } from '@fincloud/core/directives';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { NsFolderStructureModule } from '@fincloud/neoshare/folder-structure';
import { NsTemplateFieldModule } from '@fincloud/neoshare/template-field';
import {
  FolderStructureAddDocumentEffects,
  FolderStructureAddFolderEffects,
  FolderStructureDeleteFolderEffects,
  FolderStructureMoveDocumentEffects,
  FolderStructureMoveFolderEffects,
  FolderStructureRenameFolderEffects,
  folderStructureFeature,
} from '@fincloud/state/folder-structure';
import { FinActionsMenuModule } from '@fincloud/ui/actions-menu';
import { FinBreadcrumbModule } from '@fincloud/ui/breadcrumb';
import { FinButtonActionComponent, FinButtonModule } from '@fincloud/ui/button';
import { FinDirectoryModule } from '@fincloud/ui/directory';
import { FinDocumentModule } from '@fincloud/ui/document';
import { FinEmptyStateModule } from '@fincloud/ui/empty-state';
import { FinHeaderAndFooterModule } from '@fincloud/ui/header-and-footer';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinMenuItemModule } from '@fincloud/ui/menu-item';
import {
  FinModalCloseDirective,
  FinModalContentDirective,
  FinModalHeaderDirective,
  FinModalModule,
} from '@fincloud/ui/modal';
import { FinObserveResizeDirective } from '@fincloud/ui/observers';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import { FinSwitchToggleComponent } from '@fincloud/ui/switch-toggle';
import { FinTooltipDirective } from '@fincloud/ui/tooltip';
import { FinTruncateTextDirective } from '@fincloud/ui/truncate-text';
import { FinWarningMessageModule } from '@fincloud/ui/warning-message';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { FolderContentGridViewComponent } from './components/folder-content-grid-view/folder-content-grid-view.component';
import { FolderContentListViewComponent } from './components/folder-content-list-view/folder-content-list-view.component';
import { FolderContentWrapperComponent } from './components/folder-content-wrapper/folder-content-wrapper.component';
import { FolderStructureComponent } from './components/folder-structure/folder-structure.component';

@NgModule({
  declarations: [
    FolderStructureComponent,
    FolderContentWrapperComponent,
    FolderContentGridViewComponent,
    FolderContentListViewComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NsFolderStructureModule,
    NsCorePipesModule,
    NsCoreDirectivesModule,
    NsCoreDateModule,
    NsTemplateFieldModule,
    FinIconModule,
    FinDirectoryModule,
    FinActionsMenuModule,
    FinBreadcrumbModule,
    FinModalHeaderDirective,
    FinModalContentDirective,
    FinButtonActionComponent,
    FinModalCloseDirective,
    FinModalModule,
    FinButtonModule,
    FinDocumentModule,
    FinTruncateTextDirective,
    FinMenuItemModule,
    FinHeaderAndFooterModule,
    FinTooltipDirective,
    FinWarningMessageModule,
    FinEmptyStateModule,
    FinScrollbarModule,
    FinSwitchToggleComponent,
    ElementCountComponent,
    FinObserveResizeDirective,
  ],
  exports: [FolderStructureComponent],
  providers: [
    provideState(folderStructureFeature),
    provideEffects(
      FolderStructureAddFolderEffects,
      FolderStructureRenameFolderEffects,
      FolderStructureDeleteFolderEffects,
      FolderStructureMoveFolderEffects,
      FolderStructureMoveDocumentEffects,
      FolderStructureAddDocumentEffects,
    ),
  ],
})
export class FolderStructureModule {}
