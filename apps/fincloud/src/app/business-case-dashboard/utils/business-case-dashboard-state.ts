import { DataRoomDraggedItemType } from '@fincloud/types/enums';
import { BusinessCaseDashboardState } from '@fincloud/types/models';

export const BUSINESS_CASE_DASHBOARD_INITIAL_STATE: BusinessCaseDashboardState =
  {
    businessCase: null,
    isBusinessCaseLoading: false,
    businessCaseLoadingError: false,
    documentFieldCategories: [],
    criteria: [],
    applications: [],
    applicationsLoaded: false,
    invitations: [],
    acceptedInvitations: {
      failed: [],
      successful: [],
    },
    invitationsLoaded: false,
    userToken: null,
    selectedApplication: null,
    selectedInvitation: null,
    commonFields: [],
    customerNamesByKey: {},
    isRealEstateTabOpened: false,
    userNamesById: {},
    editMode: false,
    activityLogs: [],
    activeNavigationItem: null,
    selectedOverviewGeneral: false,
    selectedOverviewKpi: false,
    collborationsInvitationsModalLoading: false,
    dataRoomDraggerItemType: DataRoomDraggedItemType.NONE,
    selectedCollaborationMyPartners: false,
    selectedCollaborationInvitationsApplications: false,
    selectedCollaborationInvitations: false,
    selectedCollaborationApplications: false,
    selectedCollaborationTab: '',
    selectedDataRoomCase: false,
    selectedDataRoomCompany: false,
    hasCustomerDataExportToken: false,
    hasDataExportBeenAllowed: null,
    areConditionForDataExportMet: null,
    selectedFinancingDetailsMyParticipation: false,
    selectedFinancingDetailsFinancingStructure: false,
    selectedFinancingDetailsSharedFinancingStructure: false,
    existingChats: [],
    mutedChatsIds: [],
    chatHistoryRange: {
      newestChatMessageDateTime: '',
      oldestChatMessageDateTime: '',
    },
    extendedGroups: {},
    highlightedTopGroupKey: '',
    chatHistoryHasError: false,
    chatExport: {
      selectedStartDate: '',
      selectedEndDate: '',
      hasAnyMessagesForSelectedPeriod: false,
      locale: '',
      timeZone: '',
    },
    selectedChat: {},
    globalRequiredFieldKeys: [],
    lastVisitedUrl: '',
    facilities: [],
    caseFieldsAccess: [],
    caseFieldsAccessLoaded: false,
    caseFieldsInputsRequests: [],
    caseFieldsInputsRequestsLoaded: false,
    caseAssignedUsersById: {},
    masterSubChatInfo: null,
    isRevisionApplied: false,
    allParticipantsCaseVisibility: null,
    userCorrespondingCompanyId: null,
    applicationLoaded: null,
    customerContext: null,
    participantsPermissions: null,
    currentUsersFromMyOrganization: null,
    documentsFiles: [],
    uploadedFilesInChat: [],
    isChatFileUploading: false,
    selectedChatId: '',
    failedChatUpload: false,
    failedUploadChatFile: null,
    selectedTab: '',
    isCompanyDataRoomTabSelected: false,
    mirroredFieldKeys: [],
    calculatableMirroredFieldKeys: [],
    singleCompanyNorthDataCompanies: [],
    multiCompanyNorthDataCompanies: [],
    fetchingNorthDataCompanies: false,
    highlighted: {},
    isCaseHeaderInView: true,
    uploadFilesBreakdownList: [],
    financingPartners: [],
  };
