import { Injectable } from '@angular/core';
import { ModalService } from '@fincloud/core/modal';
import { Toast } from '@fincloud/core/toast';
import { BusinessCaseModalService } from '@fincloud/neoshare/business-case-fields';
import { CaseDataRoomCustomHandlerService } from '@fincloud/neoshare/data-room';
import {
  StateLibBusinessCaseDataRoomPageActions,
  StateLibBusinessCasePageActions,
  selectAllParticipantsCaseVisibility,
  selectBusinessCase,
  selectBusinessCaseId,
  selectBusinessCaseInformation,
  selectIsLead,
  selectMirroredCalculatableFieldKeys,
  selectMirroredFieldKeys,
} from '@fincloud/state/business-case';
import { selectActiveTopicChats } from '@fincloud/state/chat';
import {
  selectCustomer,
  selectIsVolksbankGroupWithBmsSales,
} from '@fincloud/state/customer';
import {
  StateLibDataRoomApiActions,
  selectBusinessCaseFields,
  selectGroupsOrdered,
} from '@fincloud/state/data-room';
import {
  StateLibFolderStructureDocumentPageActions,
  StateLibFolderStructureFolderPageActions,
} from '@fincloud/state/folder-structure';
import { StateLibNeogptChatPageActions } from '@fincloud/state/neogpt-chat';
import { StateLibNoopPageActions } from '@fincloud/state/utils';
import { BusinessCaseControllerService } from '@fincloud/swagger-generator/business-case-manager';
import { DocumentCategoryControllerService } from '@fincloud/swagger-generator/document';
import { InformationRecord } from '@fincloud/swagger-generator/exchange';
import {
  DataRoomGroupTemplateName,
  FieldType,
  NeoGptActiveSession,
} from '@fincloud/types/enums';
import { FinToastService } from '@fincloud/ui/toast';
import {
  DATA_ROOM_DOCUMENTS_SECTION_ITEM_PREFIX,
  DATA_ROOM_DOCUMENTS_SECTION_PREFIX,
  DATA_ROOM_GROUP_PREFIX,
  DATA_ROOM_TEMPLATE_FIELD_PREFIX,
  HIGHLIGHT_PREFIX,
  VOLKSBANK_GROUP_CATEGORIES,
} from '@fincloud/utils';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { routerNavigatedAction } from '@ngrx/router-store';
import { Store } from '@ngrx/store';
import { cloneDeep, isString } from 'lodash-es';
import { of } from 'rxjs';
import { catchError, filter, map, switchMap, tap } from 'rxjs/operators';
import {
  BusinessCaseDataRoomApiActions,
  NavigationPageActions,
} from '../actions';

@Injectable()
export class BusinessCaseDataRoomEffects {
  editFieldDataRoom$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibBusinessCaseDataRoomPageActions.editFieldBusinessCaseDataRoom,
      ),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCase),
        this.store.select(selectIsLead),
        this.store.select(selectActiveTopicChats),
        this.store.select(selectAllParticipantsCaseVisibility),
        this.store.select(selectMirroredFieldKeys),
        this.store.select(selectMirroredCalculatableFieldKeys),
      ]),
      switchMap(
        ([
          payload,
          businessCase,
          isLead,
          activeTopicChatsDictionary,
          isVisibilityForAllParticipantsOn,
          mirroredFieldKeys,
          mirroredCalculatableFieldKeys,
        ]) => {
          const orderedGroups =
            businessCase.businessCaseTemplate.template.groupsOrdered;
          const information =
            businessCase.information[
              payload.fieldKey as keyof InformationRecord
            ];

          const belongingGroup = orderedGroups.find((group) =>
            group.fields.includes(payload.fieldKey),
          );

          return this.businessCaseModalService
            .openManageFieldModal(
              payload.activeTab,
              {
                information,
                field: information.field,
                orderedGroups,
                fieldGroup: belongingGroup,
                existingChatId: activeTopicChatsDictionary[information.id]?.id,
                canSeeRevisionsTab: isVisibilityForAllParticipantsOn || isLead,
                isMirrored: mirroredFieldKeys.includes(payload.fieldKey),
                isLead,
                isFieldEditable:
                  !mirroredCalculatableFieldKeys.includes(payload.fieldKey) &&
                  !information.field.expression?.length,
              },
              cloneDeep(businessCase),
            )
            .afterClosed()
            .pipe(
              filter(Boolean),
              map(() => {
                // TODO: Ideally no need to load the business case
                return StateLibBusinessCasePageActions.loadBusinessCase({
                  payload: businessCase.id,
                });
              }),
            );
        },
      ),
    );
  });

  deleteCaseDataRoomField$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(
        StateLibBusinessCaseDataRoomPageActions.deleteFieldBusinessCaseDataRoom,
      ),
      concatLatestFrom(() => [this.store.select(selectBusinessCaseId)]),
      switchMap(([payload, businessCaseId]) => {
        return this.businessCaseControllerService
          .deleteFieldByKey({
            businessCaseId,
            fieldKey: payload.field.key,
          })
          .pipe(
            map((businessCase) =>
              BusinessCaseDataRoomApiActions.deleteFieldBusinessCaseDataRoomSuccess(
                {
                  field: payload.field,
                  chat: payload.chat,
                  successMessage: payload.successMessage,
                  groupsOrdered:
                    businessCase?.businessCaseTemplate?.template?.groupsOrdered,
                },
              ),
            ),
            catchError((err) => {
              return of(
                BusinessCaseDataRoomApiActions.deleteFieldBusinessCaseDataRoomFailure(
                  {
                    errorMessage: err?.message || '',
                  },
                ),
              );
            }),
          );
      }),
    );
  });

  deleteFieldBusinessCaseDataRoomSuccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        BusinessCaseDataRoomApiActions.deleteFieldBusinessCaseDataRoomSuccess,
      ),
      filter(
        ({ field }) =>
          field.field.fieldType === FieldType.DOCUMENT && isString(field.value),
      ),
      map(() => StateLibDataRoomApiActions.deleteDocumentFieldSuccess()),
    ),
  );
  deleteCaseDataRoomFieldSuccess$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          BusinessCaseDataRoomApiActions.deleteFieldBusinessCaseDataRoomSuccess,
        ),
        tap(() => {
          this.modalService.closeActiveModals(true);
          this.finToastService.show(Toast.success());
        }),
      ),
    { dispatch: false },
  );

  deleteCaseDataRoomFieldFailure$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          BusinessCaseDataRoomApiActions.deleteFieldBusinessCaseDataRoomFailure,
        ),
        tap((action) => {
          this.finToastService.show(Toast.error(action?.errorMessage || ''));
        }),
      ),
    { dispatch: false },
  );

  highlightOnNavigationEnd$ = createEffect(() =>
    this.actions$.pipe(
      ofType(routerNavigatedAction),
      filter(
        ({ payload }) => !!payload.routerState.root.queryParams['highlighted'],
      ),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCaseFields),
        this.store.select(selectGroupsOrdered),
      ]),
      map(([{ payload }, businessCaseFields, businessCaseGroups]) => {
        const highlighted = payload.routerState.root.queryParams['highlighted'];

        const highlightedField = businessCaseFields.find(
          (field) => field.key === highlighted,
        );

        if (!highlightedField) {
          return StateLibNoopPageActions.noop();
        }

        const highlightedFieldGroup = businessCaseGroups.find((group) =>
          group.fields.includes(highlightedField.key),
        );

        const nodeTemplateName =
          highlightedField.fieldType === FieldType.DOCUMENT
            ? DataRoomGroupTemplateName.DOCUMENT
            : DataRoomGroupTemplateName.FIELD;

        return StateLibBusinessCaseDataRoomPageActions.highlightDataRoomItem({
          node: {
            templateName: nodeTemplateName,
            key: highlightedField.key,
            groupKey: highlightedFieldGroup.key,
            fieldType: highlightedField.fieldType,
          },
        });
      }),
    ),
  );

  highlightOnChatSourceSelect$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibNeogptChatPageActions.selectField),
      filter(({ session }) => session === NeoGptActiveSession.DATA_ROOM),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCaseInformation),
        this.store.select(selectGroupsOrdered),
      ]),
      map(
        ([
          { selectedFieldId },
          businessCaseInformation,
          businessCaseGroups,
        ]) => {
          const informationRecords = Object.values(
            businessCaseInformation || {},
          );

          const sourceId = selectedFieldId.split(HIGHLIGHT_PREFIX)[1];

          const information = informationRecords.find((information) =>
            information.field.fieldType !== FieldType.DOCUMENT
              ? information.id === sourceId
              : information.value === sourceId,
          );

          if (!information) {
            return StateLibNoopPageActions.noop();
          }

          const group = businessCaseGroups.find((group) =>
            group.fields.includes(information.field.key),
          );

          const nodeTemplateName =
            information.field.fieldType === FieldType.DOCUMENT
              ? DataRoomGroupTemplateName.DOCUMENT
              : DataRoomGroupTemplateName.FIELD;

          return StateLibBusinessCaseDataRoomPageActions.highlightDataRoomItem({
            node: {
              templateName: nodeTemplateName,
              key: information.field.key,
              groupKey: group.key,
              fieldType: information.field.fieldType.fieldType,
            },
          });
        },
      ),
    ),
  );

  highlightDataRoomItem$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCaseDataRoomPageActions.highlightDataRoomItem),
      map(({ node }) => {
        switch (node.templateName) {
          case DataRoomGroupTemplateName.FOLDER:
            return StateLibFolderStructureFolderPageActions.locateFolderOnHighlight(
              {
                groupKey: node.groupKey,
                target: node.id,
                highlight: DATA_ROOM_DOCUMENTS_SECTION_ITEM_PREFIX + node.id,
                fieldType: FieldType.FOLDER,
              },
            );
          case DataRoomGroupTemplateName.DOCUMENT:
            return StateLibFolderStructureDocumentPageActions.locateDocumentOnHighlight(
              {
                groupKey: node.groupKey,
                target: node.key,
                highlight: DATA_ROOM_DOCUMENTS_SECTION_ITEM_PREFIX + node.key,
                fieldType: FieldType.DOCUMENT,
              },
            );
          case DataRoomGroupTemplateName.ROOT_FOLDER_GROUP:
            return StateLibBusinessCaseDataRoomPageActions.setHighlightedField({
              highlight: DATA_ROOM_DOCUMENTS_SECTION_PREFIX + node.groupKey,
              groupKey: node.groupKey,
            });
          case DataRoomGroupTemplateName.ROOT_GROUP:
            return StateLibBusinessCaseDataRoomPageActions.setHighlightedField({
              highlight: DATA_ROOM_GROUP_PREFIX + node.key,
              groupKey: node.key,
            });
          default:
            return StateLibBusinessCaseDataRoomPageActions.setHighlightedField({
              highlight: DATA_ROOM_TEMPLATE_FIELD_PREFIX + node.key,
              fieldType: node.fieldType,
              groupKey: node.groupKey,
            });
        }
      }),
    ),
  );

  loadCategories$ = createEffect(() =>
    this.actions$.pipe(
      ofType(NavigationPageActions.selectDataRoomCase),
      concatLatestFrom(() => [
        this.store.select(selectCustomer),
        this.store.select(selectIsVolksbankGroupWithBmsSales),
      ]),
      filter(
        ([, customer, isBmsBankCustomer]) => isBmsBankCustomer && !!customer,
      ),
      switchMap(([, customer]) =>
        this.documentCategoryService
          .getDocumentCategoriesByBankingGroup({
            bankingGroup: customer.bankingGroup,
          })
          .pipe(
            map((data) => {
              const translatedCategories = data.map((item) => {
                return {
                  ...item,
                  name: `[${item.code}] ${VOLKSBANK_GROUP_CATEGORIES[item.key]}`,
                };
              });
              return BusinessCaseDataRoomApiActions.loadDocumentFieldCategoriesSuccess(
                { payload: translatedCategories },
              );
            }),
            catchError(() =>
              of(
                BusinessCaseDataRoomApiActions.loadDocumentFieldCategoriesFailure(),
              ),
            ),
          ),
      ),
    ),
  );

  dataRoomUploadFile$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCaseDataRoomPageActions.dataRoomUploadFile),
      switchMap(({ valueChange }) =>
        this.caseDataRoomCustomHandlerService
          .handleFile(valueChange, 'BUSINESS_CASE')
          .pipe(
            map(() =>
              BusinessCaseDataRoomApiActions.dataRoomUploadFileSuccess({
                fieldKey: valueChange.information.field.key,
              }),
            ),
            catchError((error) =>
              of(
                BusinessCaseDataRoomApiActions.dataRoomUploadFileFailure({
                  error,
                  fieldKey: valueChange.information.field.key,
                }),
              ),
            ),
          ),
      ),
    ),
  );

  constructor(
    private actions$: Actions,
    private finToastService: FinToastService,
    private businessCaseControllerService: BusinessCaseControllerService,
    private modalService: ModalService,
    private store: Store,
    private businessCaseModalService: BusinessCaseModalService,
    private documentCategoryService: DocumentCategoryControllerService,
    private caseDataRoomCustomHandlerService: CaseDataRoomCustomHandlerService,
  ) {}
}
