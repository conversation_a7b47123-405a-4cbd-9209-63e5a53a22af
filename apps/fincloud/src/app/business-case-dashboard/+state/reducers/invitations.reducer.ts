import {
  StateLibBusinessCasePageActions,
  StateLibInvitationsApiActions,
  StateLibInvitationsPageActions,
} from '@fincloud/state/business-case';
import { StateLibInvitationApiActions } from '@fincloud/state/invitation';
import { BusinessCaseDashboardState } from '@fincloud/types/models';
import { Action, ActionReducer, createReducer, on } from '@ngrx/store';
import { BUSINESS_CASE_DASHBOARD_INITIAL_STATE } from '../../utils/business-case-dashboard-state';
import { InvitationsApiActions, InvitationsPageActions } from '../actions';

export const invitationsReducer: ActionReducer<
  BusinessCaseDashboardState,
  Action
> = createReducer(
  BUSINESS_CASE_DASHBOARD_INITIAL_STATE,
  on(
    InvitationsPageActions.loadInvitations,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        invitationsLoaded: false,
      };
    },
  ),
  on(
    StateLibInvitationsApiActions.loadInvitationsSuccess,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        invitations: action.payload,
        invitationsLoaded: true,
      };
    },
  ),
  on(
    InvitationsPageActions.selectInvitation,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        selectedInvitation: state.invitations.find(
          (invitation) => invitation.id === action.payload,
        ),
      };
    },
  ),
  on(
    InvitationsPageActions.clearSelectedInvitation,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        selectedInvitation: null,
      };
    },
  ),
  on(
    InvitationsApiActions.inviteUsersToGuestCustomerFailure,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        collborationsInvitationsModalLoading: false,
      };
    },
  ),
  on(
    StateLibInvitationsPageActions.setCollaborationsInvitationsModalLoading,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        collborationsInvitationsModalLoading: action.payload,
      };
    },
  ),
  on(
    InvitationsPageActions.acceptInvitation,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        invitations: state.invitations.map((i) => {
          if (i.id === action.payload.invitation.id) {
            return action.payload.invitation;
          }
          return i;
        }),
      };
    },
  ),
  on(
    StateLibInvitationsPageActions.rejectInvitation,
    StateLibInvitationsPageActions.cancelInvitation,
    StateLibInvitationsPageActions.resendCancelledInvitation,
    StateLibInvitationsPageActions.updateInvitation,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        invitations: state.invitations.map((i) => {
          if (i.id === action.payload.id) {
            return action.payload;
          }
          return i;
        }),
      };
    },
  ),
  on(
    StateLibInvitationsPageActions.updateInvitation,
    (state, { payload }): BusinessCaseDashboardState => {
      return {
        ...state,
        selectedInvitation: { ...payload },
      };
    },
  ),
  on(
    StateLibInvitationApiActions.acceptInvitationSuccess,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        acceptedInvitations: action.result,
      };
    },
  ),
  on(
    StateLibInvitationsPageActions.clearAcceptedInvitations,
    StateLibBusinessCasePageActions.clearBusinessCase,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        acceptedInvitations:
          BUSINESS_CASE_DASHBOARD_INITIAL_STATE.acceptedInvitations,
      };
    },
  ),
);
