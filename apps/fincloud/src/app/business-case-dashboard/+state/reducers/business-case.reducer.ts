import {
  StateLibBusinessCaseApiActions,
  StateLibBusinessCaseDataRoomPageActions,
  StateLibBusinessCaseKpiPageActions,
  StateLibBusinessCasePageActions,
  StateLibCollaborationManagementApiActions,
} from '@fincloud/state/business-case';
import {
  StateLibBusinessCaseRealEstateApiActions,
  StateLibBusinessCaseRealEstatePageActions,
} from '@fincloud/state/business-case-real-estate';
import {
  StateLibFacilitiesApiActions,
  StateLibFacilitiesPageActions,
} from '@fincloud/state/facilities';
import {
  StateLibFolderStructureDocumentPageActions,
  StateLibFolderStructureFolderPageActions,
} from '@fincloud/state/folder-structure';
import { StateLibInboxDocumentsPageActions } from '@fincloud/state/inbox-documents';
import { Customer } from '@fincloud/swagger-generator/authorization-server';
import {
  BusinessCaseGroup,
  BusinessCaseParticipantCustomer,
  InformationRecord,
} from '@fincloud/swagger-generator/exchange';
import { DataRoomDraggedItemType } from '@fincloud/types/enums';
import { BusinessCaseDashboardState } from '@fincloud/types/models';
import { Action, ActionReducer, createReducer, on } from '@ngrx/store';
import { keyBy } from 'lodash-es';
import { BUSINESS_CASE_DASHBOARD_INITIAL_STATE } from '../../utils/business-case-dashboard-state';
import {
  BusinessCaseAdministrationApiActions,
  BusinessCaseApiActions,
  BusinessCaseDataRoomApiActions,
  BusinessCasePageActions,
  ChatPageActions,
} from '../actions';

export const businessCaseReducer: ActionReducer<
  BusinessCaseDashboardState,
  Action
> = createReducer(
  BUSINESS_CASE_DASHBOARD_INITIAL_STATE,
  on(
    BusinessCasePageActions.setBusinessCaseLoading,
    (state, action): BusinessCaseDashboardState => {
      // potential problem with participants
      return {
        ...state,
        isBusinessCaseLoading: action.payload,
        businessCaseLoadingError: false,
      };
    },
  ),
  on(
    StateLibBusinessCasePageActions.setIsBusinessCaseHeaderInView,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        isCaseHeaderInView: action.payload,
      };
    },
  ),
  on(
    StateLibBusinessCaseKpiPageActions.setKpiActiveTab,
    StateLibBusinessCaseRealEstatePageActions.getSharedFinancingStructure,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        isCaseHeaderInView: true,
      };
    },
  ),
  on(
    BusinessCaseApiActions.loadMainRefsCommonFieldsSuccess,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        commonFields: action.payload,
      };
    },
  ),
  on(
    StateLibBusinessCaseDataRoomPageActions.dataRoomFieldDraggingStarted,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        dataRoomDraggerItemType: action.payload,
      };
    },
  ),
  on(
    StateLibBusinessCaseDataRoomPageActions.dataRoomFieldDraggingEnded,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        dataRoomDraggerItemType: DataRoomDraggedItemType.NONE,
      };
    },
  ),
  on(
    StateLibBusinessCaseApiActions.loadBusinessCaseSuccess,
    (state, action): BusinessCaseDashboardState => {
      // potential problem with participants
      return {
        ...state,
        businessCase: {
          ...action.payload,
          minParticipationAmount:
            action.payload.minParticipationAmount > 0
              ? action.payload.minParticipationAmount
              : null,
          maxParticipationAmount:
            action.payload.maxParticipationAmount > 0
              ? action.payload.maxParticipationAmount
              : null,
        },
        isBusinessCaseLoading: false,
      };
    },
  ),
  on(
    BusinessCaseApiActions.loadBusinessCaseFailure,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        isBusinessCaseLoading: false,
        businessCaseLoadingError: true,
      };
    },
  ),
  on(
    BusinessCaseApiActions.addPlatformManagerToCaseSuccess,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        businessCase: {
          ...state.businessCase,
          participants: state.businessCase.participants.map((participant) => {
            if (participant.customerKey === action.payload.customerKey) {
              return action.payload as BusinessCaseParticipantCustomer;
            }

            return participant;
          }),
        },
      };
    },
  ),
  on(
    BusinessCaseAdministrationApiActions.getEnableExportInformationFailure,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        areConditionForDataExportMet: false,
      };
    },
  ),
  on(
    BusinessCaseAdministrationApiActions.getEnableExportInformationSuccess,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        areConditionForDataExportMet: true,
      };
    },
  ),
  on(
    BusinessCaseApiActions.loadBusinessCaseFailure,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        isBusinessCaseLoading: false,
        businessCaseLoadingError: true,
      };
    },
  ),
  on(
    BusinessCaseApiActions.loadCurrentUsersFromMyOrganizationInBusinessCaseSuccess,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        currentUsersFromMyOrganization: action.payload,
      };
    },
  ),

  on(
    StateLibBusinessCasePageActions.clearBusinessCase,
    (): BusinessCaseDashboardState => {
      return { ...BUSINESS_CASE_DASHBOARD_INITIAL_STATE };
    },
  ),
  on(
    StateLibBusinessCasePageActions.updateBusinessCaseState,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        businessCase: {
          ...state.businessCase,
          state: action.payload.businessCaseState,
        },
      };
    },
  ),
  on(
    BusinessCasePageActions.updateBusinessCaseParticipants,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        businessCase: {
          ...state.businessCase,
          participants: action.payload,
        },
      };
    },
  ),
  on(
    StateLibBusinessCasePageActions.setBusinessCase,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        businessCase: {
          ...action.payload,
          minParticipationAmount:
            action.payload.minParticipationAmount > 0
              ? action.payload.minParticipationAmount
              : null,
          maxParticipationAmount:
            action.payload.maxParticipationAmount > 0
              ? action.payload.maxParticipationAmount
              : null,
        },
      };
    },
  ),
  on(
    BusinessCasePageActions.setCustomerNames,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        customerNamesByKey: {
          ...state.customerNamesByKey,
          ...keyBy<Customer>(action.payload, 'key'),
        },
      };
    },
  ),
  on(
    StateLibBusinessCasePageActions.setBusinessCaseGroups,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        businessCase: {
          ...state.businessCase,
          businessCaseTemplate: {
            ...state.businessCase.businessCaseTemplate,
            template: {
              ...state.businessCase.businessCaseTemplate.template,
              groupsOrdered: action.payload as BusinessCaseGroup[],
            },
          },
        },
      };
    },
  ),
  on(
    StateLibBusinessCaseDataRoomPageActions.dataRoomSideNavigationToggleItem,
    (state, payload): BusinessCaseDashboardState => {
      const [id, value] = Object.entries(payload)[0];
      return {
        ...state,
        extendedGroups: {
          ...state.extendedGroups,
          [id]: value,
        },
      };
    },
  ),
  on(
    StateLibBusinessCaseDataRoomPageActions.highlightTopGroupInView,
    (state, { highlightedTopGroupKey }): BusinessCaseDashboardState => {
      return {
        ...state,
        highlightedTopGroupKey,
      };
    },
  ),
  on(
    StateLibBusinessCasePageActions.updateBusinessCaseMinMaxParticipationAmount,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        businessCase: {
          ...state.businessCase,
          minParticipationAmount:
            action.payload.minParticipationAmount > 0
              ? action.payload.minParticipationAmount
              : null,
          maxParticipationAmount:
            action.payload.maxParticipationAmount > 0
              ? action.payload.maxParticipationAmount
              : null,
        },
      };
    },
  ),
  on(
    ChatPageActions.setGlobalRequiredFieldsKeys,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        globalRequiredFieldKeys: action.payload,
      };
    },
  ),
  on(
    BusinessCaseApiActions.loadCustomerDataExportTokenFailure,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        hasCustomerDataExportToken: false,
      };
    },
  ),
  on(
    BusinessCaseApiActions.loadCustomerDataExportTokenSuccess,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        hasCustomerDataExportToken: action.payload.hasActiveToken,
        hasDataExportBeenAllowed: action.payload.businessCases?.includes(
          state.businessCase?.id,
        ),
      };
    },
  ),
  on(
    BusinessCaseAdministrationApiActions.allowDataExportSuccess,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        hasDataExportBeenAllowed: true,
      };
    },
  ),
  on(
    BusinessCaseAdministrationApiActions.allowDataExportFailure,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        hasDataExportBeenAllowed: false,
      };
    },
  ),
  on(
    StateLibBusinessCasePageActions.setLastVisitedUrl,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        lastVisitedUrl: action.payload,
      };
    },
  ),
  on(
    BusinessCaseDataRoomApiActions.loadDocumentFieldCategoriesSuccess,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        documentFieldCategories: action.payload,
      };
    },
  ),
  on(
    BusinessCaseDataRoomApiActions.loadDocumentFieldCategoriesFailure,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        documentFieldCategories: [],
      };
    },
  ),
  on(
    BusinessCasePageActions.setAllCustomersImmoCaseVisibility,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        allParticipantsCaseVisibility: action.payload,
      };
    },
  ),
  on(
    StateLibBusinessCasePageActions.setBusinessCaseInformation,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        businessCase: {
          ...state.businessCase,
          information: action.payload,
        },
      };
    },
  ),
  on(
    StateLibBusinessCaseRealEstateApiActions.updateMyParticipationAmountSuccess,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        businessCase: {
          ...state.businessCase,
          participants: state.businessCase.participants.map((participant) => {
            if (participant.customerKey === action.payload.customerKey) {
              return action.payload;
            }

            return participant;
          }),
        },
      };
    },
  ),
  on(
    BusinessCaseApiActions.loadBusinessCaseStatusesSuccess,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        businessCaseStatuses: action.payload,
      };
    },
  ),
  on(
    BusinessCaseApiActions.loadBusinessCaseStatusesFailure,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        businessCaseStatuses: [],
      };
    },
  ),
  on(
    BusinessCaseApiActions.loadReasonsForClosingCaseSuccess,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        reasonsForClosingBusinessCase: action.payload,
      };
    },
  ),
  on(
    BusinessCaseApiActions.loadReasonsForClosingCaseFailure,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        reasonsForClosingBusinessCase: [],
      };
    },
  ),
  on(
    BusinessCaseApiActions.changeBusinessCaseStatusSuccess,
    (state, { payload: { customerKey, status, targetLead } }) => {
      return {
        ...state,
        businessCase: {
          ...state.businessCase,
          participants: state.businessCase?.participants.map((participant) => {
            if (
              (targetLead && participant.lead) ||
              (!targetLead && participant.customerKey === customerKey)
            ) {
              return {
                ...participant,
                participantPerceptionForCaseState: status,
              };
            }

            return participant;
          }) as BusinessCaseParticipantCustomer[],
        },
      };
    },
  ),
  on(
    StateLibBusinessCasePageActions.updateEditTemplateMode,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        editMode: action.payload,
      };
    },
  ),
  on(
    StateLibInboxDocumentsPageActions.activateInboxDocument,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        editMode: true,
      };
    },
  ),
  on(
    BusinessCaseApiActions.showCompleteBusinessCaseModalSuccess,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        editMode: false,
      };
    },
  ),
  on(
    StateLibBusinessCaseApiActions.loadMirroredFieldKeysSuccess,
    (state, action) => {
      const matchingKeys = action.eligibleFieldKeys.filter((eligibleFieldKey) =>
        action.dataRoomTemplateFieldKeys.includes(eligibleFieldKey),
      );
      return {
        ...state,
        mirroredFieldKeys: matchingKeys,
      };
    },
  ),
  on(
    StateLibBusinessCaseApiActions.loadMirroredCalculatableFieldsSuccess,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        calculatableMirroredFieldKeys: action.calculatableFieldKeys,
      };
    },
  ),
  on(StateLibBusinessCasePageActions.unsyncFieldInDataRoom, (state, action) => {
    const information = structuredClone(state.businessCase.information);
    information[action.fieldKey as keyof InformationRecord].synced = false;
    return {
      ...state,
      businessCase: {
        ...state.businessCase,
        information,
      },
    };
  }),

  on(
    StateLibBusinessCasePageActions.setSelectedCollaborationTap,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        selectedCollaborationTab: action.selectedCollaborationTab,
      };
    },
  ),
  on(
    StateLibBusinessCasePageActions.setSelectedCollaborationTap,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        selectedCollaborationTab: action.selectedCollaborationTab,
      };
    },
  ),
  on(
    StateLibFacilitiesPageActions.getMultiNorthDataCompanies,
    StateLibFacilitiesPageActions.getSingleNorthDataCompanies,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        fetchingNorthDataCompanies: true,
      };
    },
  ),
  on(
    StateLibFacilitiesApiActions.getSingleNorthDataCompaniesSuccess,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        singleCompanyNorthDataCompanies: action.companies,
        fetchingNorthDataCompanies: false,
      };
    },
  ),
  on(
    StateLibFacilitiesApiActions.getMultiNorthDataCompaniesSuccess,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        multiCompanyNorthDataCompanies: action.companies,
        fetchingNorthDataCompanies: false,
      };
    },
  ),
  on(
    StateLibCollaborationManagementApiActions.linkCadrSuccess,
    (state, action): BusinessCaseDashboardState => {
      return {
        ...state,
        businessCase: {
          ...state.businessCase,
          isCADRLinked: action.isCadrLinked,
        },
      };
    },
  ),
  on(
    StateLibBusinessCaseDataRoomPageActions.setHighlightedField,
    StateLibFolderStructureFolderPageActions.locateFolderOnHighlight,
    StateLibFolderStructureDocumentPageActions.locateDocumentOnHighlight,
    (state, { highlight, fieldType, groupKey }): BusinessCaseDashboardState => {
      return {
        ...state,
        highlighted: {
          key: highlight,
          fieldType,
          groupKey,
        },
      };
    },
  ),
  on(
    StateLibBusinessCaseDataRoomPageActions.clearHighlightedField,
    (state): BusinessCaseDashboardState => {
      return {
        ...state,
        highlighted: {},
      };
    },
  ),
  on(
    StateLibBusinessCaseDataRoomPageActions.dataRoomUploadFile,
    (state, { valueChange }): BusinessCaseDashboardState => {
      const uploadingFileBreakdown = state.uploadFilesBreakdownList.find(
        ({ key }) => key === valueChange.information.field.key,
      );

      if (uploadingFileBreakdown) {
        return {
          ...state,
          uploadFilesBreakdownList: state.uploadFilesBreakdownList.map(
            (uploadFileBreakdown) => {
              if (
                uploadFileBreakdown.key === valueChange.information.field.key
              ) {
                return {
                  ...uploadFileBreakdown,
                  isUploading: true,
                  hasError: false,
                };
              }

              return uploadFileBreakdown;
            },
          ),
        };
      }

      return {
        ...state,
        uploadFilesBreakdownList: [
          ...state.uploadFilesBreakdownList,
          {
            key: valueChange.information.field.key,
            isUploading: true,
            hasError: false,
          },
        ],
      };
    },
  ),
  on(
    BusinessCaseDataRoomApiActions.dataRoomUploadFileSuccess,
    (state, { fieldKey }): BusinessCaseDashboardState => ({
      ...state,
      uploadFilesBreakdownList: state.uploadFilesBreakdownList.filter(
        ({ key }) => key !== fieldKey,
      ),
    }),
  ),
  on(
    BusinessCaseDataRoomApiActions.dataRoomUploadFileFailure,
    (state, { fieldKey }): BusinessCaseDashboardState => ({
      ...state,
      uploadFilesBreakdownList: state.uploadFilesBreakdownList.map(
        (uploadFileBreakdown) => {
          if (uploadFileBreakdown.key === fieldKey) {
            return {
              ...uploadFileBreakdown,
              isUploading: false,
              hasError: true,
            };
          }

          return uploadFileBreakdown;
        },
      ),
    }),
  ),
);
