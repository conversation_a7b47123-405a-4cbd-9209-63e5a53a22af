import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NsUiTooltipModule } from '@fincloud/components/tooltip';
import { NsCorePipesModule } from '@fincloud/core/pipes';
import { FinBadgesModule } from '@fincloud/ui/badges';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinContainerModule } from '@fincloud/ui/container';
import { FinDropdownComponent } from '@fincloud/ui/dropdown';
import { FinExpansionPanelModule } from '@fincloud/ui/expansion-panel';
import { FinFilterTabsModule } from '@fincloud/ui/filter-tabs';
import { FinIconModule } from '@fincloud/ui/icon';
import { FinInputModule } from '@fincloud/ui/input';
import { FinLoaderComponent } from '@fincloud/ui/loader';
import { FinPaginatorModule } from '@fincloud/ui/paginator';
import { FinRowTemplateDirective, FinTableModule } from '@fincloud/ui/table';
import { FinTabsModule } from '@fincloud/ui/tabs';
import { FinTruncateTextModule } from '@fincloud/ui/truncate-text';
import { FinTypesModule } from '@fincloud/ui/types';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { TodosManagementEffects } from './+state/effects/todos-management.effects';
import { todosManagementFeature } from './+state/reducers/todos-management.reducer';
import { TodosManagementDescriptionComponent } from './components/todos-management-description/todos-management-description.component';
import { TodosManagementFiltersComponent } from './components/todos-management-filters/todos-management-filters.component';
import { TodosManagementLayoutComponent } from './components/todos-management-layout/todos-management-layout.component';
import { TodosManagementListComponent } from './components/todos-management-list/todos-management-list.component';
import { TodosManagementRoutingModule } from './todos-management-routing.module';
@NgModule({
  declarations: [
    TodosManagementLayoutComponent,
    TodosManagementFiltersComponent,
    TodosManagementListComponent,
    TodosManagementDescriptionComponent,
  ],
  imports: [
    CommonModule,
    TodosManagementRoutingModule,
    ReactiveFormsModule,
    FinFilterTabsModule,
    FinDropdownComponent,
    FinInputModule,
    FinTabsModule,
    FinTypesModule,
    FinExpansionPanelModule,
    FinPaginatorModule,
    FinTableModule,
    FinRowTemplateDirective,
    FinBadgesModule,
    FinIconModule,
    FinContainerModule,
    FinLoaderComponent,
    FinButtonModule,
    FinTruncateTextModule,
    NsCorePipesModule,
    NsUiTooltipModule,
  ],
  exports: [],
  providers: [
    provideState(todosManagementFeature),
    provideEffects(TodosManagementEffects),
  ],
})
export class TodosManagementModule {}
