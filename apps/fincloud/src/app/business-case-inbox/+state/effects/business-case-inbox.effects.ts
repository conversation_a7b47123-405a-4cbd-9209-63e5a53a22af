import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SocketService, SocketType } from '@fincloud/core/socket';
import { Toast } from '@fincloud/core/toast';
import {
  StateLibBusinessCaseApiActions,
  selectBusinessCaseId,
  selectBusinessCaseInformation,
  selectBusinessCaseTemplate,
  selectBusinessCaseTemplateOrderedFields,
} from '@fincloud/state/business-case';
import { selectQueryParams } from '@fincloud/state/router';
import { selectUserId } from '@fincloud/state/user';
import {
  BusinessCaseControllerService,
  DocumentClassificationControllerService,
} from '@fincloud/swagger-generator/business-case-manager';
import { InboxDocumentControllerService } from '@fincloud/swagger-generator/document';
import { DocumentClassification } from '@fincloud/types/models';
import { FinToastService } from '@fincloud/ui/toast';
import { DOCUMENT_CLASSIFICATION } from '@fincloud/utils';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import {
  catchError,
  concat,
  filter,
  map,
  mergeMap,
  of,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs';
import { TransferDocumentFailureCodes } from '../../enums/transfer-document-failure-codes';
import { TransferMode } from '../../enums/transfer-mode';
import { DocumentClassificationMessage } from '../../models/document-classification-message';
import { DOCUMENT_SUCCESS_MESSAGES } from '../../utils/document-success-messages';
import { DOCUMENT_TRANSFER_FAILURE_MESSAGES } from '../../utils/document-transfer-failure-messages';
import {
  BusinessCaseInboxApiActions,
  BusinessCaseInboxPageActions,
} from '../actions';
import { inboxFeature } from '../reducers';

@Injectable()
export class BusinessCaseInboxEffects {
  loadDocuments$ = createEffect(() =>
    this.actions$.pipe(
      ofType(BusinessCaseInboxPageActions.loadInboxDocument),
      concatLatestFrom(() => [
        this.store.select(selectBusinessCaseId),
        this.store.select(selectBusinessCaseTemplateOrderedFields),
        this.store.select(selectBusinessCaseInformation),
        this.store.select(selectQueryParams),
      ]),
      switchMap(
        ([
          ,
          businessCaseId,
          businessCaseGroupsOrdered,
          businessCaseInformation,
          queryParams,
        ]) =>
          this.documentInboxControllerService
            .getAllInboxDocumentsForBusinessCase({
              businessCaseId,
            })
            .pipe(
              map((documents) =>
                BusinessCaseInboxApiActions.loadInboxDocumentsSuccess({
                  documents,
                  businessCaseGroupsOrdered,
                  businessCaseInformation,
                  documentId: queryParams.documentId,
                  page: parseInt(queryParams.page || 1),
                }),
              ),
              catchError((err: HttpErrorResponse) =>
                of(BusinessCaseInboxApiActions.loadInboxDocumentsFailure(err)),
              ),
            ),
      ),
    ),
  );

  loadDocumentClassifications$ = createEffect(() =>
    this.actions$.pipe(
      ofType(BusinessCaseInboxApiActions.loadInboxDocumentsSuccess),
      concatLatestFrom(() => this.store.select(selectBusinessCaseId)),
      switchMap(([{ documents }, businessCaseId]) => {
        return this.documentClassificationControllerService
          .getDocumentClassifications({
            businessCaseId,
            body: documents.map((doc) => doc.id),
          })
          .pipe(
            map(({ dataRoomHash, documentPredictions }) =>
              BusinessCaseInboxApiActions.loadInboxDocumentClassificationSuccess(
                {
                  dataRoomHash,
                  documentClassifications:
                    documentPredictions as DocumentClassification[],
                },
              ),
            ),
            catchError((err: HttpErrorResponse) =>
              of(
                BusinessCaseInboxApiActions.loadInboxDocumentsClassificationFailure(
                  err,
                ),
              ),
            ),
          );
      }),
    ),
  );

  loadDocumentClassification$ = createEffect(() =>
    this.actions$.pipe(
      ofType(BusinessCaseInboxPageActions.getDocumentClassification),
      concatLatestFrom(() => this.store.select(inboxFeature.selectDocuments)),
      filter(([action, documents]) => {
        const currentDocument = documents.find(
          (document) => action.documentId === document.id,
        );

        return currentDocument?.isClassificationLoading;
      }),
      mergeMap(([{ documentId, dataRoomHash, businessCaseId }]) => {
        return this.documentClassificationControllerService
          .getDocumentClassification({
            businessCaseId,
            documentId,
            dataRoomHash,
          })
          .pipe(
            map((documentClassification) =>
              BusinessCaseInboxApiActions.setAdditionalDocumentClassificationsSuccess(
                {
                  documentClassification:
                    documentClassification as DocumentClassification,
                },
              ),
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                BusinessCaseInboxApiActions.setAdditionalDocumentClassificationsFailure(
                  error,
                ),
              ),
            ),
          );
      }),
    ),
  );

  documentClassificationSocketRoomJoin$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(BusinessCaseInboxApiActions.loadInboxDocumentsSuccess),
        concatLatestFrom(() => [
          this.store.select(selectBusinessCaseId),
          this.store.select(selectUserId),
        ]),
        tap(([, businessCaseId, userId]) => {
          this.socketService.joinRoomAndReceiveMessagesByDestination(
            `created-document-classification-${businessCaseId}:${userId}`,
            DOCUMENT_CLASSIFICATION,
            SocketType.PLATFORM_NOTIFICATION,
          );
        }),
      ),
    { dispatch: false },
  );

  documentClassificationSocketMessages$ = createEffect(() =>
    this.actions$.pipe(
      ofType(BusinessCaseInboxApiActions.loadInboxDocumentsSuccess),
      switchMap(() =>
        this.socketService
          .getMessagesByDestination$<DocumentClassificationMessage>(
            DOCUMENT_CLASSIFICATION,
            SocketType.PLATFORM_NOTIFICATION,
          )
          .pipe(
            map(({ documentId, dataRoomHash, businessCaseId }) =>
              BusinessCaseInboxPageActions.getDocumentClassification({
                documentId,
                dataRoomHash,
                businessCaseId,
              }),
            ),
            takeUntil(
              this.actions$.pipe(
                ofType(BusinessCaseInboxPageActions.clearState),
              ),
            ),
          ),
      ),
    ),
  );

  updateSnapshots$ = createEffect(() =>
    this.actions$.pipe(
      ofType(StateLibBusinessCaseApiActions.loadBusinessCaseSuccess),
      concatLatestFrom(() => [
        this.store.select(inboxFeature.selectUpdateSnapShotsAllowed),
      ]),
      filter(([, updateSnapShotsAllowed]) => updateSnapShotsAllowed),
      map(([businessCase]) =>
        BusinessCaseInboxApiActions.updateCaseTemplateAndInformationSuccess({
          businessCaseGroupsOrderedSnapShot:
            businessCase.payload.businessCaseTemplate.template.groupsOrdered,
          businessCaseInformationSnapShot: businessCase.payload.information,
        }),
      ),
      catchError(() =>
        of(
          BusinessCaseInboxApiActions.updateCaseTemplateAndInformationFailure(),
        ),
      ),
    ),
  );

  applyDocumentsToDataRoom$ = createEffect(() =>
    this.actions$.pipe(
      ofType(BusinessCaseInboxPageActions.applyDocumentsToDataRoom),
      concatLatestFrom(() => [
        this.store.select(inboxFeature.selectShowApplyButtonLoader),
        this.store.select(inboxFeature.selectDocuments),
        this.store.select(selectBusinessCaseId),
        this.store.select(inboxFeature.selectSelectedDocumentIds),
        this.store.select(selectBusinessCaseTemplate),
      ]),
      filter(([, showLoader]) => showLoader),
      switchMap(
        ([
          ,
          ,
          documents,
          businessCaseId,
          selectedDocumentIds,
          businessCaseTemplate,
        ]) => {
          const selectedDocuments = documents.filter((document) =>
            selectedDocumentIds.includes(document.id),
          );

          const payloadItems = selectedDocuments.map((document) => {
            const activePrediction =
              document.documentClassification?.predictions?.find(
                (prediction) => prediction.isActive,
              );

            return {
              documentId: document.id,
              existingFieldKey: activePrediction?.placeholderKey,
              newFieldLabel:
                document.manuallySetFileName ||
                activePrediction?.placeholderName ||
                document.contentReference.fileName,
              targetFolderId:
                document.manuallySetFolderId || activePrediction?.rootFolderId,
              template: businessCaseTemplate,
              transferMode: activePrediction?.placeholderKey
                ? TransferMode.USE_EXISTING_FIELD
                : TransferMode.CREATE_NEW_FIELD,
            };
          });

          return concat(
            ...payloadItems.map((payloadItem) =>
              this.businessCaseControllerService
                .transferDocumentToBusinessCase({
                  businessCaseId,
                  body: payloadItem,
                })
                .pipe(
                  map(() =>
                    BusinessCaseInboxApiActions.applyDocumentsToDataRoomSuccess(
                      {
                        documentIds: [payloadItem.documentId],
                      },
                    ),
                  ),
                  catchError((error) =>
                    of(
                      BusinessCaseInboxApiActions.applyDocumentsToDataRoomFailure(
                        {
                          error,
                        },
                      ),
                    ),
                  ),
                ),
            ),
          );
        },
      ),
    ),
  );

  applyDocumentsToDataRoomSuccess$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(BusinessCaseInboxApiActions.applyDocumentsToDataRoomSuccess),
        tap(() =>
          this.finToastService.show(
            Toast.success(DOCUMENT_SUCCESS_MESSAGES.TRANSFER_DOCUMENTS),
          ),
        ),
      );
    },
    { dispatch: false },
  );

  applyDocumentsToDataRoomFailure$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(BusinessCaseInboxApiActions.applyDocumentsToDataRoomFailure),
        tap(({ error }) => {
          const { code } = JSON.parse(error.error);

          switch (code) {
            case TransferDocumentFailureCodes.BUCM_1093:
              this.finToastService.show(
                Toast.info(
                  DOCUMENT_TRANSFER_FAILURE_MESSAGES.TRANSFER_ERROR_DATA_ROOM_STRUCTURE,
                ),
              );
              break;
            case TransferDocumentFailureCodes.BUCM_1108:
            case TransferDocumentFailureCodes.BUCM_1109:
              this.finToastService.show(
                Toast.info(
                  DOCUMENT_TRANSFER_FAILURE_MESSAGES.TRANSFER_ERROR_UNASSIGNED_DOCUMENT,
                ),
              );
              break;
            default:
              this.finToastService.show(
                Toast.error(DOCUMENT_TRANSFER_FAILURE_MESSAGES.TRANSFER_ERROR),
              );
          }
        }),
      );
    },
    { dispatch: false },
  );

  clearState$ = createEffect(
    () => {
      return this.actions$.pipe(
        ofType(BusinessCaseInboxPageActions.clearState),
        concatLatestFrom(() => [
          this.store.select(selectBusinessCaseId),
          this.store.select(selectUserId),
        ]),
        tap(([, businessCaseId, userId]) => {
          this.socketService.leaveRoom(
            `created-document-classification-${businessCaseId}:${userId}`,
            SocketType.PLATFORM_NOTIFICATION,
          );
        }),
      );
    },
    { dispatch: false },
  );

  showValidationErrorToast$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(BusinessCaseInboxPageActions.applyDocumentsToDataRoom),
        concatLatestFrom(() => [
          this.store.select(inboxFeature.selectShowApplyButtonLoader),
        ]),
        filter(([, showLoader]) => !showLoader),
        tap(() => {
          this.finToastService.show(
            Toast.error(
              DOCUMENT_TRANSFER_FAILURE_MESSAGES.DUPLICATION_FILE_CONFLICT,
            ),
          );
        }),
      ),
    { dispatch: false },
  );

  constructor(
    private actions$: Actions,
    private store: Store,
    private documentInboxControllerService: InboxDocumentControllerService,
    private documentClassificationControllerService: DocumentClassificationControllerService,
    private socketService: SocketService,
    private businessCaseControllerService: BusinessCaseControllerService,
    private finToastService: FinToastService,
  ) {}
}
