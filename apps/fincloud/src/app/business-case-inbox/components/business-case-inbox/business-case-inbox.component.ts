import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { selectAutoGeneratedBusinessCaseName } from '@fincloud/state/business-case';

import { Router } from '@angular/router';
import { FullScreenService } from '@fincloud/core/fullscreen';
import { StateLibInboxDocumentsPageActions } from '@fincloud/state/inbox-documents';
import {
  StateLibUserOnboardingPageActions,
  selectDocumentInboxFeatureOnboarding,
} from '@fincloud/state/user-onboarding';
import { FinButtonAppearance } from '@fincloud/ui/button';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { TourService } from 'ngx-ui-tour-ngx-bootstrap';
import { distinctUntilChanged, filter, take, tap } from 'rxjs';
import {
  BusinessCaseInboxEmailPageActions,
  BusinessCaseInboxPageActions,
  inboxFeature,
} from '../../+state';
import { InboxTourStepsNamesEnum } from '../../enums/inbox-tour-steps-names-enum';
import { INBOX_TIPS_CONFIG } from '../../utils/inbox-tips-config';
import { INBOX_TOUR_CONFIG } from '../../utils/inbox-tour';

@Component({
  selector: 'app-business-case-inbox',
  templateUrl: './business-case-inbox.component.html',
  styleUrl: './business-case-inbox.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [FullScreenService],
})
export class BusinessCaseInboxComponent implements AfterViewInit {
  forwardingEmail$ = this.store.select(inboxFeature.selectForwardingEmail);
  selectBusinessCaseIdAndCustomerKey$ = this.store.select(
    inboxFeature.selectBusinessCaseIdAndCustomerKey,
  );

  isForwardingEmailCopied$ = this.store.select(
    inboxFeature.selectIsForwardingEmailCopied,
  );

  autoGeneratedBusinessCaseName$ = this.store.select(
    selectAutoGeneratedBusinessCaseName,
  );

  shouldShowDocumentPdfPreview$ = this.store.select(
    inboxFeature.selectShouldShowDocumentPreview,
  );

  documents$ = this.store.select(inboxFeature.selectDocuments);

  readonly inboxStepNames = Object.keys(
    INBOX_TIPS_CONFIG,
  ) as InboxTourStepsNamesEnum[];
  readonly initialStepName =
    InboxTourStepsNamesEnum.INBOX_FEATURE_INTRODUCTION_STEP;
  readonly lastStepName =
    InboxTourStepsNamesEnum.INBOX_DATA_ROOM_TREE_SECTION_STEP;
  readonly inboxTourStepsNamesEnum = InboxTourStepsNamesEnum;
  readonly finSize = FinSize;
  readonly finButtonAppearance = FinButtonAppearance;

  activeTips = INBOX_TIPS_CONFIG.inboxFeatureIntroductionStep;
  activeStepName = this.initialStepName;

  constructor(
    private store: Store,
    private destroyRef: DestroyRef,
    private tourService: TourService,
    private router: Router,
  ) {}

  ngAfterViewInit() {
    this.store
      .select(selectDocumentInboxFeatureOnboarding)
      .pipe(
        filter((seen) => seen !== null),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((isSeen) => {
        if (isSeen) {
          this.clearTouringService();
          return;
        }

        this.tourInit();
      });
  }

  copyEmail() {
    this.store.dispatch(
      BusinessCaseInboxEmailPageActions.copyForwardingEmail(),
    );
  }

  tourInit() {
    this.tourService.disableHotkeys();
    this.initializeServiceWithConfig();
  }

  isSectionInTourPreview(
    activeStepName: InboxTourStepsNamesEnum,
    expectedStepName: InboxTourStepsNamesEnum,
  ): boolean {
    return activeStepName === expectedStepName;
  }

  handleNextStep() {
    this.tourService.end();

    if (this.activeStepName === this.lastStepName) {
      this.completeTour();
      return;
    }

    this.goToNextStep();
  }

  private completeTour() {
    this.activeStepName = this.initialStepName;
    this.store.dispatch(
      StateLibUserOnboardingPageActions.updateUserInboxOnboarding(),
    );
    this.clearTouringService();
  }

  private goToNextStep() {
    const nextStepName = INBOX_TIPS_CONFIG[this.activeStepName][0]
      .nextStepName as InboxTourStepsNamesEnum;
    this.activeStepName = nextStepName;
    this.activeTips = INBOX_TIPS_CONFIG[nextStepName];

    this.initializeServiceWithConfig();
  }

  private clearTouringService() {
    this.inboxStepNames.forEach((stepName) =>
      this.tourService.unregister(stepName),
    );
    this.tourService.steps = [];
  }

  private initializeServiceWithConfig() {
    this.tourService.initialize(INBOX_TOUR_CONFIG[this.activeStepName], {
      enableBackdrop: true,
      disableScrollToAnchor: true,
    });
    this.tourService.start();
  }

  onClose() {
    this.store.dispatch(BusinessCaseInboxPageActions.clearState());
    this.selectBusinessCaseIdAndCustomerKey$
      .pipe(
        take(1),
        tap(({ businessCaseId, userCustomerKey }) => {
          this.store.dispatch(
            StateLibInboxDocumentsPageActions.loadInboxDocuments({
              payload: { businessCaseId },
            }),
          );
          this.router.navigateByUrl(
            `/${userCustomerKey}/business-case/${businessCaseId}/data-room/case`,
          );
        }),
      )
      .subscribe();
  }
}
