import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  OnInit,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup } from '@angular/forms';
import { FinSplitButtonType } from '@fincloud/ui/split-button';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';

import {
  FileUploaderComponent,
  FileUploaderOptions,
} from '@fincloud/components/files';

import { FinCheckboxComponent } from '@fincloud/ui/checkbox';
import { FinEmptyStateType } from '@fincloud/ui/empty-state';
import { FinTableSort } from '@fincloud/ui/table';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  shareReplay,
  switchMap,
  tap,
} from 'rxjs';

import { ScrollCommunicationService } from '@fincloud/core/scroll';
import { IdentityService } from '@fincloud/core/services';
import { setTimeoutUnpatched } from '@fincloud/core/utils';
import { SelectedInboxFileSource } from '@fincloud/types/enums';
import { BusinessCaseInboxDocument } from '@fincloud/types/models';
import { FinButtonAppearance, FinButtonShape } from '@fincloud/ui/button';
import { FILE_EXTENSIONS_ARRAY } from '@fincloud/utils';
import { isEqual } from 'lodash-es';
import {
  BusinessCaseInboxDeleteDocumentPageActions,
  BusinessCaseInboxDocumentOperationsPageActions,
  BusinessCaseInboxPageActions,
  BusinessCaseInboxUploadDocumentPageActions,
  inboxFeature,
} from '../../+state';
import { UploadOptions } from '../../enums/upload-options';
import { DocumentFormManagementService } from '../../services/document-form-management.service';
import { COLUMNS_CONFIG } from '../../utils/columns-config';
import { FORM_CONTROL_SUFFIX } from '../../utils/form-control-suffix';

@Component({
  selector: 'app-document-uploader',
  templateUrl: './document-uploader.component.html',
  styleUrl: './document-uploader.component.scss',
  providers: [DocumentFormManagementService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentUploaderComponent implements OnInit, AfterViewInit {
  @ViewChild(FileUploaderComponent) fileUploader: FileUploaderComponent;
  @ViewChild('selectAllDocumentsCheckbox')
  selectAllDocumentsCheckbox: FinCheckboxComponent;

  sortingDirection$ = this.store.select(
    inboxFeature.selectDocumentsSortingDirection,
  );
  filteredDocuments$ = this.store
    .select(inboxFeature.selectFilteredDocuments)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));
  selectedDocumentIds$ = this.store
    .select(inboxFeature.selectSelectedDocumentIds)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));
  isLoading$ = this.store.select(inboxFeature.selectIsLoading);

  readonly idPrefix = 'uploads-';
  readonly templateData$ = this.store
    .select(inboxFeature.selectDocumentTemplateData)
    .pipe(
      tap(({ documentForHighlight }) => {
        if (
          !!documentForHighlight.businessCaseInboxDocument &&
          documentForHighlight.selectedInboxFileSource ===
            SelectedInboxFileSource.HIERARCHY
        ) {
          this.scrollCommunicationService.scrollToElementById(
            `${this.idPrefix}${documentForHighlight.businessCaseInboxDocument.id}`,
          );
        }
      }),
    );

  readonly uploadButtonLabel = $localize`:@@documentInbox.label.uploadFiles:Hochladen`;
  readonly emptyStateTitle = $localize`:@@documentInbox.dragAndDrop:Fügen Sie Dateien und Ordner per Drag & Drop hinzu`;
  readonly searchEmptyStateTitle = $localize`:@@search.autocomplete.noResults:Keine Ergebnisse gefunden. Versuchen Sie andere Schlüsselwörter oder überprüfen Sie die Rechtschreibung`;
  readonly finSize = FinSize;
  readonly finSplitButtonType = FinSplitButtonType;
  readonly uploadOptions = UploadOptions;
  readonly finEmptyStateType = FinEmptyStateType;
  readonly tableColumns = COLUMNS_CONFIG;
  readonly finButtonAppearance = FinButtonAppearance;
  readonly finButtonShape = FinButtonShape;
  readonly formControlSuffix = FORM_CONTROL_SUFFIX;
  readonly documentDoesNotEndWith = '.';

  searchControl = new FormControl('');
  selectAllDocumentsCtrl = new FormControl(false);
  documentsForm = new FormGroup({});
  fileUploaderOptions: FileUploaderOptions = {
    allowedFileType: FILE_EXTENSIONS_ARRAY.map((fileType) => '.' + fileType),
  };

  uploadedDocuments$ = this.store
    .select(inboxFeature.selectDocumentsWithScrolling)
    .pipe(
      tap(({ scrollToDocumentId }) => {
        if (scrollToDocumentId) {
          setTimeoutUnpatched(() => {
            this.scrollCommunicationService.scrollToElementById(
              `${this.idPrefix}${scrollToDocumentId}`,
            );
          }, 1000);
        }
      }),
      map(({ documents }) => documents),
    );

  constructor(
    private destroyRef: DestroyRef,
    private store: Store,
    private scrollCommunicationService: ScrollCommunicationService,
    private documentFormManagementService: DocumentFormManagementService,
    private identityService: IdentityService,
  ) {}

  ngOnInit(): void {
    this.searchControl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        filter((searchTerm) => {
          const searchTermLength = searchTerm?.length;

          return searchTermLength === 0 || searchTermLength >= 3;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((searchTerm) =>
        this.store.dispatch(
          BusinessCaseInboxPageActions.setSearchTerm({ searchTerm }),
        ),
      );

    this.selectAllDocumentsCtrl.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((isChecked) => {
        this.selectAllDocumentsCheckbox?.setIndeterminateState(false);

        this.documentFormManagementService
          .getEnabledSelectionControlNames(this.documentsForm)
          .forEach((controlName) => {
            this.documentsForm
              .get(controlName)
              .setValue(isChecked, { emitEvent: false });
          });

        this.toggleDocument();
      });

    this.uploadedDocuments$
      .pipe(
        distinctUntilChanged(isEqual),
        filter((documents) => !!documents.length),
        tap((documents) => this.updateFormControls(documents)),
        switchMap(() =>
          this.documentsForm.valueChanges.pipe(
            tap(() => {
              this.toggleDocument();
              this.updateSelectAllCheckboxState();
            }),
          ),
        ),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  ngAfterViewInit(): void {
    this.fileUploader.options.maxFilesCount = undefined;
    this.fileUploader.fileSelected
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((files) => this.onAddDocuments(files));
  }

  private updateSelectAllCheckboxState(): void {
    const enabledControlNames =
      this.documentFormManagementService.getEnabledSelectionControlNames(
        this.documentsForm,
      );

    this.documentFormManagementService.updateSelectAllCheckboxState(
      this.documentsForm,
      this.selectAllDocumentsCtrl,
      this.selectAllDocumentsCheckbox,
      enabledControlNames,
    );
  }

  private toggleDocument(): void {
    this.store.dispatch(
      BusinessCaseInboxPageActions.updateDocumentIds({
        documentIds: this.documentFormManagementService.getSelectedDocumentIds(
          this.documentsForm,
        ),
      }),
    );
  }

  trackDocument(document: BusinessCaseInboxDocument): string {
    return `${document.id || document.uploadId}-${document.hasError}-${document.hasClassificationError}`;
  }

  onUploadOption(selectedOption: string): void {
    switch (selectedOption) {
      case UploadOptions.FILES:
        this.fileUploader.options = {
          ...this.fileUploader.options,
          folderUploadEnabled: null,
        };
        this.fileUploader.chooseFile();
        break;
      case UploadOptions.FOLDER:
        this.fileUploader.options = {
          ...this.fileUploader.options,
          folderUploadEnabled: true,
        };
        this.fileUploader.chooseFile();
        break;
    }
  }

  onSortChange(sortObject: FinTableSort): void {
    this.store.dispatch(
      BusinessCaseInboxPageActions.setSortDirection({
        sortDirection: sortObject.dir,
      }),
    );
  }

  onAddDocuments(files: File[] | File): void {
    const filesArr = Array.isArray(files) ? files : [files];
    const fileEntries = filesArr.map((file) => {
      const uploadId = this.identityService.generateKey();
      const document = {
        fileName: file.name,
        isLoading: true,
        uploadId,
      };

      return { file, document };
    });

    this.store.dispatch(
      BusinessCaseInboxUploadDocumentPageActions.prepareFilesForUpload({
        fileEntries,
      }),
    );
  }

  hasPredictionsAndUnmodifiedDocument(
    document: BusinessCaseInboxDocument,
  ): boolean {
    return (
      !!document.documentClassification?.predictions?.length &&
      !document.manuallySetFolderId &&
      !document.manuallySetFileName
    );
  }

  isRenameDisabled(document: BusinessCaseInboxDocument): boolean {
    return (
      (document.hasError && !document.isWarningIgnored) ||
      document.hasClassificationError ||
      document.isClassificationLoading
    );
  }

  private updateFormControls(documents: BusinessCaseInboxDocument[]): void {
    this.fileUploader.removeAll();
    const hasRemovedControls =
      this.documentFormManagementService.removeObsoleteControls(
        this.documentsForm,
        documents,
      );

    let needsSelectionUpdate = hasRemovedControls;

    documents.forEach((document) => {
      this.documentFormManagementService.addNewControl(
        this.documentsForm,
        document,
      );

      this.documentFormManagementService.checkValidationControl(
        this.documentsForm,
        document,
      );
      this.documentFormManagementService.resetSelectionControlAfterManualMove(
        this.documentsForm,
        document,
      );
      const hasResetAnyControl =
        this.documentFormManagementService.resetSelectionControlAfterRegenerate(
          this.documentsForm,
          document,
        );

      if (hasResetAnyControl || document.manuallySetFolderId) {
        needsSelectionUpdate = true;
      }

      this.documentFormManagementService.updateControlValue(
        this.documentsForm,
        document,
      );
    });

    if (needsSelectionUpdate) {
      this.toggleDocument();
      this.updateSelectAllCheckboxState();
    }
  }

  onSelectDocument(event: Event, document: BusinessCaseInboxDocument): void {
    event.stopPropagation();
    this.store.dispatch(
      BusinessCaseInboxDocumentOperationsPageActions.selectDocument({
        selectedDocument: document,
      }),
    );
  }

  onDownload(document: BusinessCaseInboxDocument): void {
    this.store.dispatch(
      BusinessCaseInboxDocumentOperationsPageActions.downloadDocument({
        document,
      }),
    );
  }

  onDetails(document: BusinessCaseInboxDocument): void {
    this.store.dispatch(
      BusinessCaseInboxDocumentOperationsPageActions.showDocumentDetails({
        document,
      }),
    );
  }

  onDelete(): void {
    this.store.dispatch(
      BusinessCaseInboxDeleteDocumentPageActions.deleteDocuments({
        isSingleDeletion: true,
      }),
    );
  }

  onEdit(event: Event): void {
    event.stopPropagation();
    this.store.dispatch(
      BusinessCaseInboxDocumentOperationsPageActions.setIsEditingDocument({
        isEditingDocument: true,
      }),
    );
  }

  onDocumentBlur(
    controlValue: string,
    document: BusinessCaseInboxDocument,
    isEditMode: boolean,
  ): void {
    const control = this.documentsForm.get(
      `${document.id}${this.formControlSuffix}`,
    );

    if (!control) {
      return;
    }

    if (
      !isEditMode ||
      control.invalid ||
      document.manuallySetFileName === controlValue
    ) {
      return control.markAsTouched();
    }

    this.store.dispatch(
      BusinessCaseInboxDocumentOperationsPageActions.updateDocumentName({
        documentName: controlValue,
        document,
      }),
    );
  }

  onMove(event: Event): void {
    event.stopPropagation();
    this.store.dispatch(
      BusinessCaseInboxDocumentOperationsPageActions.moveDocument({
        isSingleMove: true,
      }),
    );
  }

  onPreview(ev: Event): void {
    ev.stopPropagation();
    ev.preventDefault();
    this.store.dispatch(
      BusinessCaseInboxDocumentOperationsPageActions.previewDocumentFromUploadSection(),
    );
  }

  onIgnore(event: Event, document: BusinessCaseInboxDocument): void {
    event.stopPropagation();
    this.store.dispatch(
      BusinessCaseInboxDocumentOperationsPageActions.ignoreDocumentDuplication({
        document,
      }),
    );
  }

  onRegenerate(event: Event, document: BusinessCaseInboxDocument): void {
    event.stopPropagation();
    this.store.dispatch(
      BusinessCaseInboxDocumentOperationsPageActions.regenerateDocuments({
        document,
      }),
    );
  }

  onCancelAnimation(document: BusinessCaseInboxDocument): void {
    this.store.dispatch(
      BusinessCaseInboxDocumentOperationsPageActions.cancelDocumentClassificationLoading(
        {
          document,
        },
      ),
    );
  }
}
