<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>neoshare</title>
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="./assets/favicon/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="./assets/favicon/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="./assets/favicon/favicon-16x16.png"
    />
    <link rel="manifest" href="./assets/favicon/site.webmanifest" />
    <link
      rel="mask-icon"
      href="./assets/favicon/safari-pinned-tab.svg"
      color="#5bbad5"
    />
    <link rel="shortcut icon" href="./assets/favicon/favicon.ico" />
    <link
      rel="preload"
      as="style"
      href="https://atlas.microsoft.com/sdk/javascript/mapcontrol/3/atlas.min.css"
      fetchpriority="low"
      onload="this.rel='stylesheet'"
    />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Symbols+Outlined"
      rel="stylesheet"
    />

    <meta name="msapplication-TileColor" content="#f8f8f8" />
    <meta
      name="msapplication-config"
      content="./assets/favicon/browserconfig.xml"
    />
    <meta name="theme-color" content="#26283E" />
    <!-- Load environment variables -->
    <script src="assets/env.js"></script>
    <script>
      (function (s, t, a, n) {
        s[t] ||
          ((s[t] = a),
          (n = s[a] =
            function () {
              n.q.push(arguments);
            }),
          (n.q = []),
          (n.v = 2),
          (n.l = 1 * new Date()));
      })(window, 'InstanaEumObject', 'ineum');

      ineum('reportingUrl', '${INSTANA_URL}');
      ineum('key', '${INSTANA_TRACKINGKEY}');
      ineum('trackSessions');
      ineum('user', 'PROD');
      ineum('meta', 'version', '${VERSION}');
    </script>
    <script
      async
      crossorigin="anonymous"
      src="${INSTANA_URL}eum.min.js"
    ></script>
    <!-- <script
      async
      data-domain="${BASEURL}"
      src="https://info.neoshare.de/js/script.js"></script> -->
    <script type="application/javascript">
      var global = window;
    </script>
  </head>
  <body>
    <app-root></app-root>
  </body>
</html>
