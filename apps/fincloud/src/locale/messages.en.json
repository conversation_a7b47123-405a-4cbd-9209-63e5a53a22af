{"locale": "en", "translations": {"2078863684537615971": "{VAR_PLURAL, plural, =1 {1 day ago} other {{INTERPOLATION} days ago}}", "4382629238310047310": "{VAR_PLURAL, plural, =1 {1 week ago} other {{INTERPOLATION} weeks ago}}", "5216529258824596799": "{VAR_PLURAL, plural, =1 {1 hour ago} other {{INTERPOLATION} hours ago}}", "5323002394718188264": "", "8830575832589857077": "{VAR_PLURAL, plural, =0 {now} =1 {1 minute ago} other {{INTERPOLATION} minutes ago}}", "ConnectedField.text": "This field is linked to another one", "acceptRemove.info-text": "Note that this does not stop sharing of the Enterprise Data Room via a funding case. Sharing via a financing case is automatically terminated when the company related Data Room is removed from all financing cases of this company. ", "acceptRemove.info-text.label": "Close", "acceptRemove.title": "Direct sharing of the enterprise-related data room has been terminated.", "accountLogs.columns.deviceAndBrowser": "<PERSON><PERSON>/Browser", "accountLogs.columns.ipAddress": "IP Address", "accountLogs.columns.operation": "Operation", "accountLogs.empty": "There are no account logs for this user.", "accountManagement.actions.view.users": "View users", "accountManagement.button.create.user": "Create user", "accountManagement.customer.createCustomerModal.BIC": "BIC", "accountManagement.customer.createCustomerModal.bankingGroup": "Banking group", "accountManagement.customer.createCustomerModal.basicTittle": " Customer details ", "accountManagement.customer.createCustomerModal.errorMessage.bicLength": "Please enter 8 or more characters for your BIC", "accountManagement.customer.createCustomerModal.errorMessage.forbiddenSymbol2": "Invalid symbol", "accountManagement.customer.createCustomerModal.errorMessage.invalidBIC": "Invalid BIC", "accountManagement.customer.createCustomerModal.featureTemplate": "neoshare Feature template", "accountManagement.customer.createCustomerModal.id": "Customer key", "accountManagement.customer.createCustomerModal.label.customerName": "Customer name", "accountManagement.customer.createCustomerModal.label.type": "Customer type", "accountManagement.customer.createCustomerModal.other": "Other", "accountManagement.customer.createCustomerModal.placeholder": "Customer type", "accountManagement.customer.createCustomerModal.salesChannel": "Sales channel", "accountManagement.customer.createCustomerModal.uploadLogo.title": "Оrganisation logo", "accountManagement.customer.createCustomerModal.validationError.forbiddenSymbol": "Invalid symbol", "accountManagement.customer.createCustomerModal.validationError.keyAlreadyExists": "This customer ID is already taken.", "accountManagement.customer.customerList.header": "Customers", "accountManagement.customer.customerList.label.createCustomer": "Create customer", "accountManagement.customer.customerList.placeholder": "Search...", "accountManagement.customer.list.search.bar.placeholder": "Search by customer name", "accountManagement.customer.modal.create.label": "Create new customer", "accountManagement.customers.guest.empty.state.description": "There are no guest customers at the moment", "accountManagement.customers.guest.empty.state.header": "There are no guest customers at the moment", "accountManagement.customers.regular.empty.state.description": "There are no regular customers at the moment", "accountManagement.customers.regular.empty.state.header": "No customers", "accountManagement.editCustomerDetails": "Edit customer details", "accountManagement.editUserDetails": "Edit user details", "accountManagement.select.user.roles": "Select user roles:", "accountManagement.stepper": "Step 1 of 2", "accountManagement.usageContract.createUsageContractModal.controlLabel.customerKey": "Customer name", "accountManagement.usageContract.createUsageContractModal.controlLabel.dueDate": "Date of signature", "accountManagement.usageContract.createUsageContractModal.controlLabel.title": "User agreement title", "accountManagement.usageContract.createUsageContractModal.errorMessage.invalidDate": "Invalid date", "accountManagement.usageContract.createUsageContractModal.errorMessage.maxFileSize": "The file is too large. The maximum file size is 15 MB.", "accountManagement.usageContract.createUsageContractModal.label.control.documentId": "Unsigned copy", "accountManagement.usageContract.createUsageContractModal.section.header": " Documents ", "accountManagement.usageContract.createUsageContractModal.section.header2": " Signer ", "accountManagement.user.list.search.bar.placeholder": "Search by email address", "accountManagement.users": "Users", "accountManagement.users.active": "Active", "accountManagement.users.deactivated": "Deactivated", "accountManagement.users.empty-state-description": "This customer has no active users at the moment", "accountManagement.users.empty-state-description-deactivated": "This customer has no deactivated users at the moment", "accountManagement.users.empty-state-header": "No users", "activity.log.label": "Activity log", "addButton.modal.title": "Add", "addNewCustomerToChat.info": "is now part of the funding case. Would you grant them access to the following themed chats?", "addNewUserToChat.label.button": "Approve chat access", "addNewUserToChat.text.footer": "Previous chat history is visible to any new participant.", "addReRequest.formControl": "Please fill in", "addReRequestDescription.modal.body": "Comment", "addReRequestDescription.modal.footer": "As soon as you click Submit, all customer portal users of this financing case will be immediately informed by e-mail. ", "addReRequestDescription.modal.footer.realEstate": "As soon as you click Submit, all partner portal users of this financing case will be immediately informed by e-mail.", "addReRequestDescription.modal.header": "Request data again.", "addUser.placeholder": "Search user", "address.placeholder.message": "Search address", "advanced-pie-chart.not-gathered-amount": "Amount to be collected", "allGroups.portal.actions.text": "Customer portal actions", "allGroups.portal.actions.text.realEstate": "Partner Portal Actions", "allGroupsPortalActions.groupTitle": "Visibility in customer portal", "allGroupsPortalActions.groupTitle.realEstate": "Visibility in the partner portal", "allGroupsPortalActions.label.requestData": "Request information", "allGroupsPortalActions.label.requestDataAgain": "Request information again", "allGroupsPortalActions.label.view.templates": " View Templates", "allGroupsPortalActions.label.withdraw": "Withdraw", "already.used.email.error": "The email address you entered is already in use.", "amount.details.label": "Amount details", "amountParticipation.sectionTitle": "Financing case participation requirements", "application.accepted.status": "Application accepted", "application.canceled.status": "Application canceled", "application.preparation.status": "In preparation", "application.rejected.status": "Application rejected", "application.status.removed.from.business.case": "Removed", "applicationInvitation.statusTag.expired": "Invitation expired", "applicationModal.button.disconnect": "Disable", "applicationModal.button.synchronize": "Synchronize", "applicationModal.title": "Available Apps", "applicationWarningModal.button.disconnect": "Disable", "applicationWarningModal.information": "Are you sure you want to disable the synchronization from your funding case with NextFolder? This will affect all uploaded documents of all participants.", "applicationWarningModal.title": "Disconnect synchronization", "applications.sortFields.amount": "Total", "applications.sortFields.startedOn": "Date created", "applications.sortfields.customerName": "Customer name", "applicationsList.toastError.youdonthaveActive": "You no longer have access to the financing case as it is no longer active. Participation is not possible.", "approve.label": "Approve", "apps.card.notConnectedToPeople": "Connect", "apps.card.statusBox": "Connected", "apps.description": "Integrate applications that your organisation's employees can use in your funding cases. ", "apps.header": "Apps", "apps.messageUnderIcons": " Further integrations are currently being worked on and will be available soon ", "archiveChat.archivedBy": "Archived from", "archiveChat.bilateralChat": "Bilateral chats", "archiveChat.dateOfArchive": "Archive date", "archiveChat.emptyChat": "No archived chats available.", "archiveChat.header": "Archive", "archiveChat.name": "Name", "archiveChat.themeChat": "Themed chats", "authenticatedDevices.columns.date.time": "Date/Hour", "bank.dashboard.exportExcel.general.filename.suffix": "General", "bank.dashboard.exportExcel.kpi.filename.suffix": "KPI", "basicFacility.model.basisInfo": "Basic information", "basicFacility.model.loanAmmount": "Credit amount", "basicIntegration.form.controlLabel": "Folder ID", "basicIntegration.form.headerText": "Connect Dracoon with your financial case.", "basicIntegration.guide.field.check.text": "Folder ID", "basicIntegration.guide.field.copyId": "Copy the folder ID and insert it in the displayed field: ", "basicIntegration.guide.header": "This way you connect your project to Dracoon", "basicIntegration.guide.label.allFiles": "All files", "basicIntegration.guide.label.copyLInk": "Copy link", "basicIntegration.guide.label.rightClick": "Right click", "basicIntegration.guide.label.selectFolder": "Select folder", "basicIntegration.guide.text": "To establish a successful connection between your data room and Dracoon, you will need to wait approximately 20 minutes after connecting.", "bilateralChat.title": "Create bilateral chat", "bilateralModal.label": "Chat participant", "billingAddress.city": "City", "billingAddress.customerName": "Bank(Firm)*", "billingAddress.email": "Email address (invoice recipient)", "billingAddress.streetNameAndNumber": "Street name and house number", "billingAddress.zipCode": "Zip code", "blankTemplate.areaDetails": "Flächen", "blankTemplate.bgfTotal": "BGF Gesamt", "blankTemplate.cadr.versionDescription": "Version description", "blankTemplate.commercialRentalSpace": "Fläche Gewerbe", "blankTemplate.constructionYear": "<PERSON><PERSON><PERSON><PERSON>", "blankTemplate.currentJNKMTotal": "IST-JNKM Gesamt", "blankTemplate.customerInformation": "Customer Information", "blankTemplate.detailedMacrolocation": "Makrolage ausführlich", "blankTemplate.detailedMicrolocation": "Mikrolage ausführlich", "blankTemplate.financingCommitmentDesiredBy": "Finanzierungszusage gewünscht bis zum", "blankTemplate.financingPurpose": "Financing Purpose", "blankTemplate.gastronomyRentalSpace": "Fläche Gastronomie", "blankTemplate.generalInformation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "blankTemplate.hotelRentalSpace": "Fläche Hotel", "blankTemplate.incomeSituation": "Ertragssituation", "blankTemplate.interestRate": "Interest rate", "blankTemplate.interestRateConsortiumPartner": "Interest income Consortium partner", "blankTemplate.investmentLocation": "Investment location", "blankTemplate.keyDates": "Termine", "blankTemplate.landSize": "Grundstücksgröße", "blankTemplate.landValue": "Bodenwert", "blankTemplate.managementCostsTotalInProcent": "Bewirtschaftungskosten (BWK) in % Gesamt Markt-/Verkehrswert", "blankTemplate.marketValue": "Markt-/ Verkehrswert", "blankTemplate.numberOfParkingSpacesGarages": "Anzahl Stellplätze/Garagen", "blankTemplate.officeRentalSpace": "Fläche Büro", "blankTemplate.otherOperatorPropertyRentalSpace": "Fläche Sonstige Betreiberimmobilie", "blankTemplate.otherRentalSpace": "Fläche Sonstige", "blankTemplate.ownerPurchaserCaseOwner": "Eigentümer / E<PERSON><PERSON><PERSON> / <PERSON>", "blankTemplate.payoutCriteria": "Payout criteria", "blankTemplate.planDisposalProceeds": "Plan-Veräußerungserlös (EXIT-MIDCASE)", "blankTemplate.product": "Product", "blankTemplate.projectDescription": "Vorhabensbeschreibung", "blankTemplate.projectName": "Projektname", "blankTemplate.propertyData": "Objektdaten", "blankTemplate.propertyLocation": "Objektstandort", "blankTemplate.purchasePriceOfLand": "Kaufpreis Grundstück/Boden", "blankTemplate.rating": "Rating", "blankTemplate.residentialRentalSpace": "Fläche W<PERSON>", "blankTemplate.retailRentalSpace": "Fläche Handel", "blankTemplate.securities": "Securities", "blankTemplate.standardLandValuePerSqm": "Bodenrichtwert pro QM", "blankTemplate.targetJNKMCommercialMarketValue": "SOLL-JNKM Gewerbe Markt-/Verkehrswert", "blankTemplate.targetJNKMGastronomyMarketValue": "SOLL-JNKM Gastronomie Markt-/Verkehrswert", "blankTemplate.targetJNKMHotelLeaseMarketValue": "SOLL-JNKM Hotel Markt-/Verkehrswert", "blankTemplate.targetJNKMOfficeMarketValue": "SOLL-JNKM Büro Markt-/Verkehrswert", "blankTemplate.targetJNKMOtherOperatorPropertyMarketValue": "SOLL-JNKM /Pacht Sonstige Betreiberimmobilie Markt-/Verkehrswert", "blankTemplate.targetJNKMOtherRentalSpaceMarketValue": "SOLL-JNKM Sonstige Mietfläche Markt-/Verkehrswert", "blankTemplate.targetJNKMParkingSpacesGaragesMarketValue": "SOLL-JNKM Stellplätze/Garagen Markt-/Verkehrswert", "blankTemplate.targetJNKMResidentialMarketValue": "SOLL-JNKM Wohnen Markt-/Verkehrswert", "blankTemplate.targetNKMRetailMarketValue": "SOLL-JNKM Handel Markt-/Verkehrswert", "blankTemplate.valuation": "Bewertung", "blankTemplate.valueDateDesiredBy": "Valutierung gewünscht zum", "blankTemplate.website": "Website", "booleanField.no": "No", "booleanField.yes": "Yes", "btn.label.ignore": "Ignore", "buildManagement.address": "Invoice address", "buildManagement.navLink": "Invoices", "businessCase.DataRoom.noInformation": "No information is currently shared with you. As soon as Data Room fields are shared with you, they will be displayed here.", "businessCase.DataRoom.noVisible.field": "No information has been shared yet.", "businessCase.accessRightsModal.visibilityChange.statusText.private": "The visibility of this group cannot be changed, as it is visible only to the case owner.", "businessCase.accessRightsModal.visibilityChange.statusText.public": "The visibility of this group cannot be changed as it is visible to everyone with access to the case.", "businessCase.accessRightsModal.visibilityChange.tooltip.unmanaged": "Not managed", "businessCase.administration.dataExport.dataExportFieldConditionsNotMet": "To allow data export, all mandatory fields must be completed", "businessCase.administration.dataExport.modalSuccessNotification": "Data export enabled successfully", "businessCase.administration.dataExport.noActiveCustomerTokenError": "Missing or expired authentification token. Contact admin for assistance", "businessCase.application.title": "Applicant criteria", "businessCase.apply.conformation.container": "Applicant criteria", "businessCase.apply.conformation.participationAmountContainer": " Minimum and maximum participation amount.", "businessCase.apply.conformation.title": "Please confirm the applicant criteria", "businessCase.applyConfirmation.button.label": "Submit application", "businessCase.card.moreInformation": "Other company information", "businessCase.card.navigateToCase": "To the case", "businessCase.card1": "Financing volume", "businessCase.caseTransfer.warningModal.description": "<PERSON><PERSON> continue if you are sure you want the groups visible to the participant and share the information with him once he is the case owner. Click review to see which groups are hidden.", "businessCase.caseTransfer.warningModal.text": "There are groups in the Data Room which are currently not visible to this participant.", "businessCase.changeState.readOnly": "Read-only access", "businessCase.changeState.tooltipReadOnly": "No changes can be made because the case is closed", "businessCase.chat.navigate": "To the case", "businessCase.collaboration.modal.uiSelect.option.acceptAndApply": "Invite organization to apply for the financing case", "businessCase.collaboration.modal.uiSelect.option.acceptAndJoin": "Invite organization to participate in the financing case directly", "businessCase.dashboard.button.label.assume": "Accept", "businessCase.dashboard.button.label.next": "Next", "businessCase.dashboard.button.label.reject": "Reject", "businessCase.dashboard.changeStatus.error": "Status update unsuccessful", "businessCase.dashboard.changeStatus.success": "Case status changed", "businessCase.dashboard.modals.changeBusinessCaseStateModal.label.reason": "<PERSON><PERSON>", "businessCase.dashboard.modals.completeBusinessCaseModal.buttons.confirm": "Close", "businessCase.dashboard.modals.completeBusinessCaseModal.message": "Are you sure you want to close your financing case? Please select a reason:", "businessCase.dashboard.modals.reactivateBusinessCaseModal.buttons.reactivate": "Reactivate", "businessCase.dashboard.modals.reactivateBusinessCaseModal.message": "Are you sure you want to reactivate your financing case? Please select a reason:", "businessCase.dataExport.allowLabel": "Allow", "businessCase.dataExport.allowedLabel": "Allowed", "businessCase.dataExport.confirmModalHeader": "Required fields for data export ", "businessCase.dataExport.confirmModalTitle": "Are you sure you want to allow data export to the core banking system for this case?", "businessCase.dataExport.requiredFields.companyLabel": "Company data", "businessCase.dataExport.requiredFields.creditData": "Credit data", "businessCase.dataExport.requiredFields.viewPageLabel": "View page", "businessCase.dataExport.sectionDescription": "Export your case data to the core banking system. The connection is permanent for this case. Please ensure to complete all data before proceeding. ", "businessCase.dataExport.sectionLabel": "Allow data export", "businessCase.dataExport.viewRequiredFieldsLabel": "View required data", "businessCase.dataRoom.fieldInformationActions.subCaseLinkedTooltip": "{$START_TAG_UI_ICON}{$CLOSE_TAG_UI_ICON}", "businessCase.dataRoom.filters.documentsAndFolders": "Documents and folders", "businessCase.dataRoom.group.delete.message": "Are you sure you want to delete this group?", "businessCase.dataRoom.noFieldsInGeneralGroupText": "Add data via drag and drop from the left panel", "businessCase.dataRoom.noResult": "No result found. Try different keywords or check the spelling", "businessCase.dataRoom.noResultAfterFilter": "No matching results. Try adjusting your filters.", "businessCase.dataRoom.noResultAfterSearchAndFilter": "No matching results. Try adjusting your filters or search keywords.", "businessCase.dataRoom.participantRequest.pleaseFill": "Please fill.", "businessCase.dataRoom.participantRequest.reRequestLabel": "Request data again", "businessCase.dataRoom.participantRequest.requestButtonTitle": "Request", "businessCase.dataRoom.participantRequest.requestLabel": "Request information", "businessCase.dataRoom.participantRequest.withdrawLabel": "Withdraw data request", "businessCase.dataRoom.participantRequest.withdrawLabelButtonTitle": "Withdraw", "businessCase.dataRoom.prefix": "Information about", "businessCase.dataRoom.suffix": "Add", "businessCase.dataRoom.text.addField": "Add fields by drag and drop ", "businessCase.dataRoom.text.nextfolderInfo": "Supported file formats: PNG, JPG, PDF up to 100MB. For an ideal workflow, process PDF data in A4 format.", "businessCase.dataRoom.text.textSub": "To do this, drag the fields from the right column to this area. ", "businessCase.kpi.buttons.active": "Active", "businessCase.kpi.buttons.disabled": "Inactive", "businessCase.kpi.emptyState": "There are no inactive KPI's for this case", "businessCase.kpi.evaluationRisk": "Evaluation risk", "businessCase.kpi.evaluationRisk.checkExclusionCriteria": "Check exclusion criteria", "businessCase.kpi.evaluationRisk.checkPermissibleDeviation": "Check permissible deviation", "businessCase.kpi.evaluationRisk.compliantWithSpecifications": "Compliant with specifications", "businessCase.kpi.evaluationRisk.notDefined": "Not defined", "businessCase.kpi.expanded.kpiField": "KPI field", "businessCase.kpi.expanded.value": "Value", "businessCase.kpi.group.costs": "Costs", "businessCase.kpi.group.debtService": "Debt service", "businessCase.kpi.group.multipliers": "Multipliers", "businessCase.kpi.group.others": "Others", "businessCase.kpi.group.relations": "Relations", "businessCase.kpi.group.returns": "Returns", "businessCase.kpi.kpiResult": "KPI Result", "businessCase.linkedFieldsMessage": "All fields with this icon are linked to values from the organization that provided the case. Click the icon to unlink the field and edit its value.", "businessCase.optionCard.choose": "<PERSON><PERSON>", "businessCase.optionCard.duplicateCase.cardContent": "You can copy the complete case or just selected parts of it.", "businessCase.optionCard.financingCase.cardContent": "You can create new as well as existing financing cases.", "businessCase.optionCard.passingCase.cardContent": "Select this case type to place the forwarding of a financing case on neoshare. You then have the option to submit the case directly to other banks.", "businessCase.optionCard.selected": "Selected", "businessCase.overview.breakDownCard.proportion": "From financing volume", "businessCase.overview.breakDownCard.real-estate-proportion": "From financing volume", "businessCase.overview.managementSummary.comments": "Comments", "businessCase.overview.managementSummary.description": "Please write down KPI related inputs (e.g. enabling/disabling a KPI, a certain KPI result lies outside of the optimal KPI range etc.)", "businessCase.overview.managementSummary.header": "Management summary", "businessCase.overview.managementSummary.noCommentsMessage": "Comments from you or other users from your organization will be displayed here.", "businessCase.overview.tabs.general": "General", "businessCase.overview.tabs.kpi": "KPI", "businessCase.overviewBreakDownCard.series": "Total Amount", "businessCase.participantAccess.copyAs.caseParticipant": "Participant", "businessCase.participantAccess.copyAs.invitee": "", "businessCase.participantAccess.copyAs.inviteeWithNDA": "Invitee (With confidentiality agreement)", "businessCase.participantAccess.copyAs.inviteeWithoutNDA": "Ivitee (Without confidentiality agreemen)", "businessCase.participantAccess.rights.editAccess.text": "Manage editing rights", "businessCase.participantAccess.rights.editAccess.text.groupVisibility": "Groups", "businessCase.participantAccess.rights.editAccess.text.groupVisibility2": "Visibility", "businessCase.participantAccessRights.modal.editRights.editor": "Editor", "businessCase.participantAccessRights.modal.editRights.observer": "Observer", "businessCase.participantRoles.cancelLabel": "Cancel", "businessCase.participantRoles.cancelLabel.removeParticipant": "No", "businessCase.participantRoles.confirmLabel": "Confirm", "businessCase.participantRoles.confirmLabel.removeParticipant": "Yes, I am sure", "businessCase.participantRoles.confirmationMessage": "Do you really want to hand over the funding case to the following organisation?", "businessCase.participantRoles.confirmationMessage.removeParticipant": "Are you sure you want to delete this participant?", "businessCase.participantRoles.toast.error": "Error by updating Participants settings.", "businessCase.participantRoles.toast.success": "Participants settings successfully updated.", "businessCase.participationWithdraw.modal.title": "The participation amount has already been set.", "businessCase.participationWithdraw.modal.title2": "Are you sure you want to withdraw it and deduct it from the collected amount?", "businessCase.publishedState.toast.error": "Market visibility can be set to \"Public\" only when you reactivate your funding case.", "businessCase.roles.tooltip.text": "Participant can see other participants and their data related to the financing case.", "businessCase.type.financing": "Funding", "businessCase.type.passingOn": "Forwarding", "businessCaseCadr.text": "The enterprise data room has been added to the funding case, however there are no fields in the enterprise data room yet. When fields are added to it, they will be displayed here. ", "businessCaseCard.caseInvitationMessage": "You have an invitation to participate", "businessCaseCard.companyName": "Company name", "businessCaseDashboard.addMeToCase.confirmationText": "Are you sure you want to join as a participant case {$PH}?", "businessCaseDashboard.button.label.platformManagerJoin": "Add me to case", "businessCaseDashboard.conformationMessageFirstPart": "Would you like financing case", "businessCaseDashboard.conformationMessageSecondPart": "to be handed over?", "businessCaseDashboard.transferCase.confirm": "Confirm", "businessCaseDashboard.transferCase.confirmationText": "Do you want to confirm this?", "businessCaseDataRoom.filters.fieldType": "Field type", "businessCaseDataRoom.filters.selectPeriod": "Select period", "businessCaseManagement.templateEditor.templateList.label.new": "New template", "businessCaseManagement.templateEditor.templateList.placeholder.search": "Search...", "businessCaseManagement.templateEditor.templateList.selectedTemplateStatusFilter": " There are currently no templates with this status. ", "businessCaseManagement.templateEditor.templateList.selectedTemplateStatusFilterAll": " There are currently no templates available. ", "businessCaseParticipant.card.avatarText": "Contact person", "businessCaseParticipant.modal.title": "Manage Data room access rights", "businessCaseParticipantRoles.label.button.remove": "Remove participant", "businessCaseParticipantRoles.label.button.request": "Submit funding project", "businessCaseSelectorForDuplication.button.label.cancel": "View case", "businessCaseSelectorForDuplication.button.label.confirm": "Join case and proceed", "businessCaseSelectorForDuplication.empty": "Cases are not available for your organisation yet.", "businessCaseSelectorForDuplication.header": "Select financing case to copy from", "businessCaseSelectorForDuplication.myCases": "My cases only", "businessCaseSelectorForDuplication.platformManager.confirmation": "You are not a member of case {$PH}. Please choose from the following options:", "businessCaseTemplateManagement.templateEditor.emptyMessage": " Please select a template. ", "businessCaseTemplateManagement.templateEditor.label.createTemplate": "Create template", "businessCaseTemplateManagement.templateEditor.lastUpdate.test": " Last modification: ", "businessCaseTemplateManagement.templateEditor.message": " JSON format is incorrect. ", "businessCaseTemplateManagement.templateEditor.thisRevisionCanNotBeUpdated": " This revision is obsolete and cannot be edited. ", "button.label.actions": "Actions", "button.label.assign": "Assign", "button.label.assignAndDeactivate": "Assign and deactivate", "button.label.assignAndDelete": "Assign and delete", "button.label.cancel": "Cancel", "button.label.cancel.no": "No", "button.label.cancel:Abbrechen": "Cancel", "button.label.clearFilters": "Clear filters", "button.label.close": "Close", "button.label.closeCase": "Close case", "button.label.complete": "Complete", "button.label.confirm": "Yes,  I am sure", "button.label.continue": "Continue", "button.label.copy": "Copy", "button.label.delete": "Delete", "button.label.delete.small": "Delete", "button.label.enterEditMode": "Enter edit mode", "button.label.exportAsExcel": "Export as Excel", "button.label.i-understand": "I understand", "button.label.moveHere": "Move here", "button.label.next": "Next", "button.label.reactivateCase": "Reactivate case", "button.label.review": "Review", "button.label.save": "Save", "button.label.start": "Get started", "button.label.understood": "Understood", "calendar.clear": "Clear", "calendar.dateAfter30Days": "30 Days", "calendar.dateAfter7Days": "7 Days", "calendar.today": "Today", "calendar.tomorrow": "Tomorrow", "calendar.yesterday": "Yesterday", "caseReassignment.required.error": "No platform manager selected", "caseReassignment.title": "User {$PH} is the sole user for ongoing cases within your organisation. Reassign the case to a platform manager from the list below.", "caseStatusChangeModal.description.cancel": "Your funding case will be cancelled and deactivated.", "caseStatusChangeModal.description.complete": "Your funding case will be closed and deactivated.", "caseStatusChangeModal.description.publish": "Your case remains unchanged.", "caseStatusChangeModal.label.cancel": "Cancel", "caseStatusChangeModal.label.complete": "Close", "caseStatusChangeModal.label.publish": "Obtain", "caseStatusChangeModal.text": "Please select what should happen to the original funding case.", "caseTable.columns.fundingCase": "Funding case", "caseTable.columns.inquiry": "Inquiry", "cases.applicationsTable.columns.caseType": "Case type", "cases.applicationsTable.emptyState.text": "No information to display", "cases.bank.fullEmptyState": "Your organization is currently not involved in any financing case. Once it joins a case, it will appear here.", "cases.bewerbungenTab.header1": "Case", "cases.bewerbungenTab.header2": "Product", "cases.bewerbungenTab.header3": "Volume", "cases.bewerbungenTab.header4": "Case owner", "cases.bewerbungenTab.header5": "Date", "cases.bewerbungenTab.header6": "Status", "cases.businessCase.Collaboration.tab.invitationApplications": "Invitations and applications", "cases.casesList.emptyState.text": "No information to display", "cases.casesTable.columns.caseStatus": "Case status", "cases.casesTable.columns.caseType": "Case type", "cases.casesTable.columns.financingType": "Financing type", "cases.emptyState": "You are currently not involved in any financing case. Once you join a case, it will appear here.", "cases.filter.button.right": "Apply", "cases.filter.modal": "Status", "cases.filter.modal.case": "Case", "cases.filter.modal.options.active": "Active", "cases.filter.modal.options.canceled": "Canceled", "cases.filter.modal.options.completed": "Completed", "cases.filters.caseStatus.title": "Case status", "cases.filters.caseStatusTag.placeholder": "Select status", "cases.filters.caseType.financing.label": "Funding", "cases.filters.caseType.passingOn.label": "Forwarding", "cases.filters.caseType.title": "Case type", "cases.filters.financingType.corporate.label": "Corporate financing", "cases.filters.financingType.corporate.label.new": "Corporate", "cases.filters.financingType.miscellaneous.label": "Miscellaneous", "cases.filters.financingType.realEstate.label": "Real estate financing", "cases.filters.financingType.realEstate.label.new": "Real Estate", "cases.filters.financingType.title": "Type of financing", "cases.filters.financingType.title.new": "Financing type", "cases.filters.invitationApplication.caseType.label": "Case type", "cases.filters.invitationApplication.invitationStatus.label": "Status", "cases.filters.invitationApplication.invitations.label": "Invitations", "cases.filters.invitationApplication.status.label": "Status", "cases.filters.modal.status.applicationsTab": "Status", "cases.filters.modal.status.invitationsTab": "Status", "cases.filters.noResults": "No information to display", "cases.filters.title": "Filters", "cases.filters.totalResults": "selected cases", "cases.filters.totalResultsOne": "selected case", "cases.invitationTaB.header.case": "Case", "cases.invitationTaB.header.caseType": "Case type", "cases.invitationTaB.header.customerName": "Case owner", "cases.invitationTaB.header.date": "Date", "cases.invitationTaB.header.financingVolume": "Volume", "cases.invitationTaB.header.product": "Product", "cases.invitationTaB.header.status": "Status", "cases.invitations-list.toaster-message": "You no longer have access to the financing case as it is no longer active. Participation is not possible.", "cases.invitationsApplications.fullEmptyState": "You have not sent an application or received any invitation yet.", "cases.invitationsTable.emptyState.text": "No information to display", "cases.my.fullEmptyState": "You are currently not involved in any financing case. Once you join a case, it will appear here.", "cases.search.placeholder": "Search...", "cases.sidebar.bank": "Bank", "cases.sidebar.bankDefault": "Bank", "cases.sidebar.firm": "Company", "cases.sidebar.marketplace.bank": "Bank", "cases.sidebar.marketplace.bankDefault": "Bank", "cases.sidebar.marketplace.firm": "Company", "cases.statistics.completedCases": "Completed cases", "cases.statistics.ongoingCases": "Current cases", "cases.statistics.totalParticipation": "Total investment", "cases.tabNav.tab.applications": "Applications", "cases.tabNav.tab.invitations": "Invitations", "cases.tabNav.tab.mine": "My", "cases.table.header.1": "Case", "cases.table.header.3": "Volume", "cases.table.header.4": "Case owner", "cases.table.header.5": "Date edited", "cases.table.header.product": "Product", "characteristics.commissionFee": " Brokerage commission", "characteristics.disagio": " Debt discount", "characteristics.interestRate": " Interest rate", "characteristics.interestRateComission": " Interest earnings", "characteristics.rating": " Rating", "chat.exportChat.modal.downloadPdf": "Download PDF", "chat.exportChat.modal.message.success": "Chat downloaded ", "chat.exportChat.modal.options.all": "From the beginning", "chat.exportChat.modal.options.custom": "Custom", "chat.exportChat.modal.options.lastMonth": "Last 30 days", "chat.exportChat.modal.options.lastYear": "Last 365 days", "chat.exportChat.modal.to": "To", "chat.exportChat.modal.warning": "There are no messages for the selected period.", "chat.placeholder": "Write something....", "chat.send.message.businessCaseCanceled.cantTypeMessage": "The chat is currently not available to you because your financing case has been cancelled.", "chat.send.message.businessCaseCompleted.cantTypeMessage": "The chat is not available to you because your financing case has been closed.", "chat.send.message.isTypeInBusinessCaseDisabled.cantTypeMessage": "The chat is currently not available.", "chat.window.customerKey.partner": "Partner", "chatDashboard.dialog.topicChat.create": "Create topic-related chat", "chatDashboard.header": " Internal Chats", "chatDashboard.section": "Intra-organizational", "chatDashboard.sectionHeader.bilateraleCHat": "Bilateral chats", "chatDashboard.sectionHeader.category": " Customer chat ", "chatDashboard.sectionHeader.topicChat": " Topic related chats", "chatWindow.actionMenuItem.mute": " Mute", "chatWindow.actionMenuItem.showParticipant": "Show participant", "chatWindow.archived": "[ARCHIVED]", "chatWindow.chatActions.title": "Chat user", "chatWindow.chatMessage.primary.text": "Shift + Enter", "chatWindow.chatMessage.secondary.text": "to insert a new line", "checkbox.all": "All", "choose.option.error.message": "Choose an option to continue", "clear.all.filters.label": "Clear filter", "collaboration.customerOption.notRegistered": "Not registered", "collaboration.customerOption.registeredOrg": "Registered organisation", "collaboration.customerOption.registeredOrgGas": "Registered guest organisation", "collaboration.customerUserInfo.email": "Contact person's email address*", "collaboration.customerUserInfo.partner.cred": " Enter the user data of your partner:", "collaboration.customerUserInfo.partner.label": "Organization ", "collaboration.customerUserInfo.partner.userName": "Contact Person*", "collaboration.filters.selected": "selected", "collaboration.invitations.modal.header.preview": "Invitation overview", "collaboration.invitations.modal.no.signers.added": "No signatories added", "collaboration.invitations.modal.options.with.nda": "With confidentiality agreement", "collaboration.invitations.modal.options.without.nda": "Without confidentiality agreement", "collaboration.ndaStep.organization": "Organization*", "collaboration.ndaStep.participationType": "Participation type", "collaboration.participant.emptyState.message": "Select another participant on the left panel to edit their collaboration options.", "collaboration.stateCHange.label": "Yes, I am sure.", "collaborationCustomer.organization": "Organization user", "collaborationInvitations.result.modal.failed": "Invitation failed", "collaborationInvitations.result.modal.header": "You have invited the following organisations.", "collaborationInvitations.result.modal.header2": "You have invited the following organisations.", "collaborationManagement.alertConfig": "Please note that no changes should be made to documents in Dracoon. Dracoon mirrors the Data Room and thus there is no guarantee that the changes will be preserved in Dracoon and not overwritten by a synchronization from the Data Room. Synchronization of documents can take up to 10-15 minutes.", "collaborationManagement.caseSettings": "Case Settings", "collaborationManagement.integrations": "Integrations", "collaborationManagement.toast.success": "Your financing case has been successfully synchronized with NextFolder.", "collaborationStateChange.cancel.modal": "Are you sure that you want to cancel the business case?", "collaborationStateChange.close.modal": "Are you sure that you want to close the business case?", "collaborationStateChange.modal.prefix": "Are you sure the financing case", "collaborationStateChange.modal.suffix": "is wanted?", "collaborations-page.addToBlacklist": "Add to the Blocked organisations", "collaborations-page.block": "Block", "collaborations-page.block.confirm.message.prefix": "Are you sure you want to disable collaboration with", "collaborations-page.block.confirm.message.suffix": "?", "collaborations-page.block.empty.message": "Any organisations that you block will appear in this list.", "collaborations-page.placeholder": "Search organizations", "collaborations-page.unblock": "Unblock", "collaborations-page.unblock.confirm.message.prefix": "Are you sure you want to enable collaboration with this", "collaborations-page.unblock.confirm.message.suffix": "?", "comercialRegisterlabel": "Select multiple", "commercialRegister.documentInformationMessage": "This section is regularly updated. As soon as there is a new document available or a document is updated it will become available for download.", "commercialRegister.noDataFound": "There are currently no relevant documents relating to this company.", "commercialRegister.noDataFound.contactPerson": "Currently there is no contact person.", "commercialRegisterDocuments.chronological.label": "Chronological commercial register excerpt", "commercialRegisterDocuments.documentType.currentSummary": "Current export from the commercial register", "commercialRegisterDocuments.documentType.historicalSummary": "History export from the commercial register", "commercialRegisterDocuments.download.label": "Current export from the commercial register", "commercialRegisterDocuments.info": " Available documents ", "commercialRegisterDocuments.info2": " Available documents ", "commercialRegisterDocuments.label": " Historical commercial register extract ", "commercialRegisterDocuments.toast.error": "The file could not be downloaded. Please try again later.", "commercialRegisterDocuments.toast.info": "Document export started. Your download should be available in a few seconds.", "commercialRegisterDocuments.tooltip.text": "Document overview", "commercialRegisterDocuments.tooltip.text2": "Document overview", "company.industries.1": "Beverages retail", "company.industries.10": "Retail trade", "company.industries.100": "<PERSON><PERSON><PERSON>", "company.industries.101": "Civil engineering and special constructions", "company.industries.102": "Central heating and ventilation engineer", "company.industries.103": "<PERSON>", "company.industries.104": "Optician", "company.industries.105": "Clothing industry", "company.industries.106": "Bakers and confectioners", "company.industries.107": "Chemical and pharmaceutical industry", "company.industries.108": "Printing companies", "company.industries.109": "<PERSON>", "company.industries.11": "Clothing retail", "company.industries.110": "Extraction and processing of stones and earths", "company.industries.111": "Metal products production", "company.industries.112": "Electrical goods manufacture", "company.industries.113": "Electrotechnical capital goods manufacture", "company.industries.114": "Motor vehicle parts manufacture", "company.industries.115": "Plastic goods manufacture", "company.industries.116": "Tools manufacture", "company.industries.117": "Hearing Aid acoustician", "company.industries.118": "Woodworking", "company.industries.119": "Automotive trade", "company.industries.12": "Flowers and plants retail", "company.industries.120": "Mechanical Engineering", "company.industries.121": "Metal worker", "company.industries.122": "Metal processing machines", "company.industries.123": "Furniture manufacturing", "company.industries.124": "Steel deformation", "company.industries.125": "Textile industry", "company.industries.126": "Not required", "company.industries.13": "Office machines, furniture and organizational equipment retail", "company.industries.14": "Household and home textiles retail", "company.industries.15": "Household goods and DIY retail", "company.industries.16": "Paper, office supplies and stationery retail", "company.industries.17": "Watches and jewellery retail", "company.industries.18": "Electronics and photographic supplies retail", "company.industries.19": "Hardware and household retail", "company.industries.2": "Wholesale and trade procurement", "company.industries.20": "Electrical retail", "company.industries.21": "Bicycle retail", "company.industries.22": "Motorcycles trade", "company.industries.23": "Photography retail", "company.industries.24": "Furniture retail", "company.industries.25": "Food and beverage specialty retail", "company.industries.26": "Medical supply retail", "company.industries.27": "Footwear retail", "company.industries.28": "Food and beverage assortment retail", "company.industries.29": "Toy retail", "company.industries.3": "Wholesale of food and beverages", "company.industries.30": "Sports retail", "company.industries.31": "Gas stations", "company.industries.4": "Automotive parts, accessories and tires retail", "company.industries.5": "Timber retail", "company.industries.57": "Permanent crop cultivation", "company.industries.58": "Cultivation of market fruits", "company.industries.59": "Agricultural services", "company.industries.6": "Agricultural machinery retail", "company.industries.60": "Renewable energy", "company.industries.61": "Gardening and landscaping", "company.industries.62": "Gardening", "company.industries.63": "Pigs and poultry farming", "company.industries.64": "Farming alternatives", "company.industries.65": "Organic farming", "company.industries.66": "Cattle farming", "company.industries.67": "Doctors", "company.industries.68": "Elderly and nursing care services", "company.industries.69": "Architecture and engineering offices", "company.industries.7": "Pharmacies and medical supply retail", "company.industries.70": "Property developer", "company.industries.71": "Accommodation industry", "company.industries.72": "Insurance industry services", "company.industries.73": "Waste Disposal Industry", "company.industries.74": "Facility Management", "company.industries.75": "Driving schools", "company.industries.76": "Hairdressers and beauty salons", "company.industries.77": "Hospitality industry", "company.industries.78": "Facility Services", "company.industries.79": "Land and housing manager", "company.industries.8": "Bookstores", "company.industries.80": "Cargo transport", "company.industries.81": "Real estate agent", "company.industries.82": "Transportation of people", "company.industries.83": "Physical therapists", "company.industries.84": "Travel agencies", "company.industries.85": "ICT services", "company.industries.86": "Spedition and logistics", "company.industries.87": "Sports and leisure facilities", "company.industries.88": "Tax, legal and business consultants", "company.industries.89": "Renting movable property", "company.industries.9": "Drugstores and perfumeries", "company.industries.90": "Laundries and dry cleaners", "company.industries.91": "Advertising and market research", "company.industries.92": "Dentists and dental technicians", "company.industries.93": "Cabinet and furniture carpenter", "company.industries.94": "Roof technician", "company.industries.95": "Electrician", "company.industries.96": "Floor, tile and panel installer", "company.industries.97": "Structural engineering", "company.industries.98": "Plumbers, gas and water technicians", "company.industries.99": "Painters and lacquerers", "company.management": "Company management page", "company.sectors.1": "Others", "company.sectors.2": "Industry / Handcraft", "company.sectors.3": "Construction and finishing industry", "company.sectors.4": "Services", "company.sectors.5": "Agriculture", "company.sectors.6": "Retail", "company.sectors.7": "Wholesale", "companyANalysis.registerDocumentsTree.tooltipText": "Document overview", "companyAnalysis.companyBranchFilters.placeholder.cityFilter": "All cities", "companyAnalysis.companyBranchFilters.placeholder.countryFilter": "All countries", "companyAnalysis.companyBranchFilters.placeholder.onAppFilters": "Apply", "companyAnalysis.companyBranchFilters.placeholder.typeFilter": "Subsidiary type", "companyAnalysis.companyBranchFilters.title": " Subsidiary filter ", "companyAnalysis.companyBranches": "Company", "companyAnalysis.companyBranches.deleteBranchButton": "Delete subsidiary", "companyAnalysis.companyBranches.emptyInfo": " No company subsidiaries added yet. You may now add your first subsidiary. ", "companyAnalysis.companyBranches.heading": " Company subsidiaries ", "companyAnalysis.companyBranches.info": " with this address ", "companyAnalysis.companyBranches.label.rework": "Edit", "companyAnalysis.companyBranches.openManageBranch": "Add subsidiary", "companyAnalysis.companyBranches.openManageButton": "Add subsidiary", "companyAnalysis.companyBranches.placeholder": "Search...", "companyAnalysis.companyDocumentPreview.downloadText": " Download ", "companyAnalysis.companyDocumentPreview.noPreview": " No preview available for this type of data. ", "companyAnalysis.companyInformation.navLink": "KYC", "companyAnalysis.companyInformation.navLink.action": " Actions ", "companyAnalysis.companyInformation.navLink.cases": "Financing cases", "companyAnalysis.companyInformation.navLink.dataRoom": "Data room", "companyAnalysis.companyInformation.navLink.documents": "Documents", "companyAnalysis.companyInformation.navLink.information": "Additional information", "companyAnalysis.companyInformationSections.companyInformationSections.title": " Contact ", "companyAnalysis.companyInformationSections.informationSection.label": " Status ", "companyAnalysis.companyInformationSections.informationSection.label.additionalInfo.employees": " Employees ", "companyAnalysis.companyInformationSections.informationSection.label.additionalInfo.sales": " Sales ", "companyAnalysis.companyInformationSections.informationSection.label.additionalInfoCapital": " Capital ", "companyAnalysis.companyInformationSections.informationSection.label.capitalCurrency": " Capital currency ", "companyAnalysis.companyInformationSections.informationSection.label.corporatePurpose": " Company purpose ", "companyAnalysis.companyInformationSections.informationSection.label.deletionDate": " Deletion date ", "companyAnalysis.companyInformationSections.informationSection.label.foundingDate": " Date of incorporation ", "companyAnalysis.companyInformationSections.informationSection.label.identifiedLegalEntity": " Identified legal entity ", "companyAnalysis.companyInformationSections.informationSection.label.inputInfo": " Additional register number ", "companyAnalysis.companyInformationSections.informationSection.label.liquidStartDate": " Start of liquidation? ", "companyAnalysis.companyInformationSections.informationSection.label.liquidStatus": " Status of liquidation? ", "companyAnalysis.companyInformationSections.informationSection.label.liquidationEndDate": " End of liquidation? ", "companyAnalysis.companyInformationSections.informationSection.label.liquidationInformationRiskDate": " Date of the risk information ", "companyAnalysis.companyInformationSections.informationSection.label.liquidationInformationRiskHistory": " Risk information (history) ", "companyAnalysis.companyInformationSections.informationSection.label.liquidationInsolvent": " Insolvent ", "companyAnalysis.companyInformationSections.informationSection.label.taxRegistryNumber": " Tax ID ", "companyAnalysis.companyInformationSections.informationSection.title": " Login details ", "companyAnalysis.companyInformationSections.informationSection.title.informationSection": " Statistical data ", "companyAnalysis.companyInformationSections.informationSection.title.liquidation": " Liquidation ", "companyAnalysis.companyPortal.text": "To the financing case", "companyAnalysis.sameAddressCompanies.label.registerCity": " Registry City: ", "companyAnalysis.sameAddressCompanies.label.registerNumber": " Registration number: ", "companyAnalysis.sameAddressCompanies.title": " Companies at the same address ", "companyBranch.filial": "Branch type", "companyBranch.filter.city": "City", "companyBranch.filter.clearAll": "Clear all", "companyBranch.filter.filialType": "Filial type", "companyBranch.filter.land": "Country", "companyBranch.filter.placeholder": "All", "companyBranch.filter.save": "Save", "companyBranch.filter.title": "Filters", "companyBranchEditor.label.submit": "Add", "companyBranchEditor.title.friendsList": " Name of subsidiary ", "companyBranchEditor.title.header": " Add company subsidiary ", "companyBranchEditor.title.placeholder": "Enter name", "companyBranchEditor.title.placeholder.companyBranchTypeDropdown": "Select type", "companyBranchEditor.title.placeholder.location": "Enter address", "companyComponent.hoverTooltip.text": "Additional company information", "companyDocument.toast.info": "Document export started. Your download should be available in a few seconds.", "companyDocumentPreview.loadingText": "Loading", "companyDocuments.download.toast.error": "The file could not be downloaded. Please try again later.", "companyDocuments.download.toast.error2": "The file could not be downloaded. Please try again later.", "companyDocuments.toast.info": "Document export started. Your download should be available in a few seconds.", "companyEdit.table.address": "Address", "companyEdit.table.company": "Company", "companyEdit.table.legalForm": "Legal form", "companyEdit.table.nameOfCompany": "Name of company", "companyEdit.table.registryCourt": "Registry Court", "companyEdit.table.registryNumber": "Registry Number", "companyEdit.toast.error": "Error by creating company.", "companyEdit.toast.error.deleteCompany": "The company cannot be deactivated because it is used in an active financing case.", "companyEdit.toast.error.deleteCompany.show": "Error by company deactivating.", "companyEdit.toast.error.editCompany": "Error by company update.", "companyEdit.toast.error.toggleCompanyStatus": "Error by company reactivating.", "companyEdit.toast.success": "Company successfully created.", "companyEdit.toast.success.deleteCompany": "Company successfully deactivated.", "companyEdit.toast.success.editCompany": "Company successfully updated.", "companyEdit.toast.success.editCompany2": "Company successfully deactivated.", "companyEdit.toast.success.toggleCompanyStatus": "Company successfully reactivated.", "companyField.addCustomerTooltip": "Add as warranty provider", "companyField.prefix": "Add company", "companyField.revisions.button.label": "Restore revision", "companyField.revisions.changesBy": "Changes made by", "companyField.revisions.newVersion": "Latest version", "companyField.revisions.showRevision": "Show revision", "companyField.revisions.uiTable.noDataMessage": "No revisions available", "companyGraph.UBOList.buttonTooltip": "View UBO list", "companyGraph.UBOList.empty": "No UBOs found in this network", "companyGraph.UBOList.notAvailablePercentage": "N/A", "companyGraph.UBOList.title": "UBO list", "companyGraph.addConectionModal.header": "Add connection", "companyGraph.addConectionModal.selectAutoCompany": "Select a company from the list for automatic linking.", "companyGraph.addConectionModal.selectManualCompany": "Create the connection manually in the graph.", "companyGraph.collapse.btn.tooltip": "Close expanded network view", "companyGraph.collapse.node.message": "Are you sure you want to close the expanded network view for this company?", "companyGraph.collapse.node.no": "No", "companyGraph.collapse.node.proceed": "Proceed", "companyGraph.connectEdge.companyNode": "Company node", "companyGraph.connectEdge.connectTo": "Connect to", "companyGraph.connectEdge.connectionNode": "Connection Node", "companyGraph.connectEdge.editHeader": "Edit connection", "companyGraph.connectEdge.header": "Create connection", "companyGraph.connectEdge.infoMessage": "If this node appears more than once, any new connection will apply to every instance.", "companyGraph.connectEdge.ownership": "Ownership %", "companyGraph.connectEdge.ownership.validationError": "You are about to exceed 100% of the ownership.", "companyGraph.connectEdge.ownership.validationError2": "You are about to exceed 100% of the total ownership.", "companyGraph.connectEdge.ownership.validationError3": "Total ownership exceeds 100%.", "companyGraph.createCompany.existingCompany": "This company already exists in the graph. Create connections to it directly in the graph view.", "companyGraph.createCompany.modal.address": "Address*", "companyGraph.createCompany.modal.companyName": "Company name", "companyGraph.createCompany.modal.header": "Add a company node", "companyGraph.createCompany.modal.legalForm": "Legal form*", "companyGraph.createCompany.modal.registerCity": "Register city", "companyGraph.createCompany.modal.registryNumber": "Register number*", "companyGraph.details.comment": "Comment", "companyGraph.documents.tabDisabledTooltip": "Tab contains no documents", "companyGraph.documents.tableHeader": "HR number", "companyGraph.documents.tableHeader.addressLocation": "Location", "companyGraph.documents.tableHeader.company": "Company", "companyGraph.documents.tableHeader.lastUpdate": "Last Update", "companyGraph.documents.tableHeader.level": "Level", "companyGraph.documents.tableHeader.registerCourt": "Register court", "companyGraph.documentsTab": "Network Docs", "companyGraph.edge.deleteFromGraph": "Do you want to delete this connection?", "companyGraph.expandNetworkButtonTooltip.collapse": "Close expanded network view", "companyGraph.expandNetworkButtonTooltip.disabled": "Cannot expand network while a filter is active", "companyGraph.expandNetworkButtonTooltip.expand": "Expand this network", "companyGraph.expandNode.button": "Expand this network", "companyGraph.expandNode.button.disabled": "Cannot expand network while a filter is active", "companyGraph.extend.node.additionalMessage": "Expanding this network can take some time", "companyGraph.extend.node.message": "Do you want to expand this network view?", "companyGraph.extend.node.no": "No", "companyGraph.extend.node.proceed": "Proceed", "companyGraph.filter.infoNote": "Filters apply only to the currently loaded levels of the graph. Expand the graph further to filter deeper levels.", "companyGraph.filterNode.button": "Filter", "companyGraph.fullScreen.button": "Fullscreen", "companyGraph.gallery.limitReached.toastMessage": "You have reached the limit of nodes you can create in the gallery", "companyGraph.graphFilters.button.label.clearAll": "Clear all", "companyGraph.graphFilters.button.label.save": "Apply", "companyGraph.graphFilters.entities.corporateEntity": "Corporate entity", "companyGraph.graphFilters.entities.individualOwner": "Private individual", "companyGraph.graphFilters.entities.ubo": "UBO", "companyGraph.graphFilters.label.entityType": "Entity type", "companyGraph.graphFilters.label.role": "Role", "companyGraph.graphFilters.label.sharesPercentage": "Ownership (%)", "companyGraph.graphFilters.noResult": "There are no results that match your search criteria. Clear the applied filters or choose other criteria.", "companyGraph.graphFilters.title": "Filter", "companyGraph.graphToolbar.uboListButton": "UBOs", "companyGraph.groupLabel.tooltip.aboveNineteen": " over 19%", "companyGraph.groupLabel.tooltip.betweenFiveAndNineteen": " between 5% and 19%", "companyGraph.groupLabel.tooltip.holding": " Shareholder of these companies", "companyGraph.groupLabel.tooltip.owner": "{$PH} owner holding shares", "companyGraph.groupLabel.tooltip.owners": "{$PH} owners holding shares", "companyGraph.groupLabel.tooltip.underFivePercent": " under 5%", "companyGraph.hasChanges.btn.review": "Review", "companyGraph.hasChanges.message": "There are new updates in the company graph", "companyGraph.hideMinimap.tooltip": "Hide minimap", "companyGraph.holding": "Holding", "companyGraph.loadingMessage": "The data for this graphic is loading. You can close the page and continue using neoshare for other tasks without interrupting the process. Please note that this may take extended time.", "companyGraph.modal.proceed": "Proceed", "companyGraph.modifyGraph.commentTitle": "Comment (optional)", "companyGraph.modifyGraph.commentTitle.placeholder": "You can add additional information here", "companyGraph.noCompany.info.bottomLink": "Create main node", "companyGraph.noCompany.info.bottomPrefix": "Start creating this company's network manually by adding the first main node.", "companyGraph.noCompany.info.bottomSuffix": "or click “Create node” in the sidebar.", "companyGraph.noCompany.info.top": "No company network information available.", "companyGraph.node.deleteAllFromGallery": "Are you sure you want to delete all nodes in the gallery?", "companyGraph.node.deleteFromGallery": "Do you want to delete this node from the gallery?", "companyGraph.node.deleteFromGraph": "Do you want to delete this node from the graph?", "companyGraph.other": "Others", "companyGraph.paginator.compactRangeLabel": "{$PH} of {$PH_1} updates", "companyGraph.person.modal.existingPerson": "This person already exists in the graph. Create connections to the person directly in the graph view. ", "companyGraph.person.modal.header": "Add natural person", "companyGraph.resetFilterConfirmModal.content": "Expanding the level will reset your current filter. {$LINE_BREAK}Do you want to proceed?", "companyGraph.resetGraph.buttonTooltip": "Reset all loaded graphs", "companyGraph.resetGraphVisualization.toastMessage": "Graphs reset successfully", "companyGraph.review.btn.updated": "Accepted", "companyGraph.review.header": "Review updates", "companyGraph.review.new": "New", "companyGraph.review.newEdge": "New component", "companyGraph.review.newNode": "New node", "companyGraph.review.old": "Old", "companyGraph.review.paginator.prefix": "of", "companyGraph.review.paginator.suffix": "updates", "companyGraph.review.removeNode": "Deleted", "companyGraph.review.toastMessage": "Updates completed successfully", "companyGraph.reviewChanges.accept": "Accept", "companyGraph.reviewChanges.acceptAll": "Accept all", "companyGraph.reviewChanges.endDate": "End date:", "companyGraph.reviewChanges.startDate": "Start date:", "companyGraph.showMinimap.tooltip": "Show minimap", "companyGraph.sideMenu.createNode": "Create node", "companyGraph.sideMenu.createNode.person": "Person", "companyGraph.sideMenu.createNodeInfo": "Create a new node and it will appear here.", "companyGraph.sideMenu.detail.noSelectedNode.message": "Select a node and it's details will appear here.", "companyGraph.sideMenu.detail.noSelectedNode.message2": "Select a node or connection and it's details will appear here", "companyGraph.sideMenu.detail.noSelectedNode.title": "No node selected", "companyGraph.sideMenu.detail.noSelectedNode.title2": "No item selected", "companyGraph.sideMenu.detailEmpty": "No information available", "companyGraph.sideMenu.detailsTab.company.labels.address": "Address", "companyGraph.sideMenu.detailsTab.company.labels.registerCity": "Register city", "companyGraph.sideMenu.detailsTab.company.labels.registrationNumber": "Register number", "companyGraph.sideMenu.detailsTab.naturalPerson.labels.address": "Address", "companyGraph.sideMenu.detailsTab.naturalPerson.labels.birthDate": "Birth date", "companyGraph.sideMenu.detailsTab.naturalPerson.labels.location": "Location", "companyGraph.sideMenu.detailsTab.naturalPerson.labels.wealthIndex": "Wealth index", "companyGraph.sideMenu.galleryEmpty": "Gallery empty", "companyGraph.sideMenu.galleryEmpty.message": "Create a new node, and it will appear here. You can then add the nodes via drag and drop to the graph area on the left.", "companyGraph.sideMenu.galleryEmpty.subheading": "There are no newly created or unallocated nodes", "companyGraph.sideMenu.header.gallery": "Gallery", "companyGraph.sideMenu.legend.headerTitle.kind": "KIND", "companyGraph.sideMenu.legend.headerTitle.symbol": "SYMBOL", "companyGraph.sideMenu.legend.headerTitle.type": "TYPE", "companyGraph.sideMenu.legendTab.address": "Address", "companyGraph.sideMenu.legendTab.birthDate": "Birth date", "companyGraph.sideMenu.legendTab.business": "Business", "companyGraph.sideMenu.legendTab.edited": "Edited", "companyGraph.sideMenu.legendTab.nonUbo": "NON-UBO", "companyGraph.sideMenu.legendTab.shareholder": "Shareholder", "companyGraph.sideMenu.legendTab.ubo": "UBO", "companyGraph.sideMenu.legendTab.uboShareholder": "Shareholder (UBO)", "companyGraph.sideMenu.toggles.compactView": "Compact view", "companyGraph.sideMenu.toggles.expandSecondLevel": "Expand 2nd level", "companyGraph.sideMenu.toggles.expandThirdLevel": "Expand 3rd level", "companyGraph.sideMenu.toggles.highlightEdits": "Highlight edits", "companyGraph.sideMenu.toggles.showUBOPath": "Expand UBO path", "companyGraph.tabs.detail.label": "Details", "companyGraph.toolbar.title": "Graph", "companyGraph.viewDocument.button": "View Doc", "companyGraph.viewDocument.button.tooltip": "Trade register excerpt", "companyGraph.zoomSlider.tooltip": "Slide to zoom.", "companyInformation.navLInk.branches": "Branches", "companyInformation.switch.dataRoom": "Edit mode", "companyInformationFields.informationText": "Select the Data Room fields you want to copy to your corporate Data Room", "companyPortal.companyPortalDashboard.heading": " No financing case has been assigned to you yet. As soon as you are assigned a financing case, you will be notified about it by email. ", "companyPortal.companyPortalDashboard.title": " Dashboard ", "components.alertPanel.title": " Upload successful", "composite.field.numeric": "Numeric", "composite.field.percent": "Percentage", "confirm.accept.application.message": "Are you sure you want to accept this application? ", "confirm.accept.invitation.message": "Are you sure you want to accept this invitation?", "confirm.reject.application.message": "Are you sure you want to reject this application?", "confirm.reject.invitation.message": "Are you sure you want to reject this invitation? ", "confirmEmptyCadr.closeReject": "Do not share", "confirmEmptyCadr.closeSuccess": "Share anyway", "confirmEmptyCadr.infoText": "There must be at least one group with a field that is not private so that the company-related Data Room does not appear empty to your partners. Do you still want to share the enterprise Data Room? ", "confirmEmptyCadr.title": "There is no information that can be shared.", "confirmationDialog.initialCaseOwner.checkbox.label.new": "New case owner", "confirmationDialog.initialCaseOwner.checkbox.label.other": "Other case participants", "confirmationDialog.initialCaseOwner.title": "Share the corporate data room with:", "confrim.cancel.invitation.message": " Are you sure you want to cancel this invitation? ", "congrats.label": "Congratulations!", "contactDownloadDialogProp.cancelLabel": "Download older version", "contactDownloadDialogProp.confirmLabel": "Close", "contactDownloadDialogProp.message": "Please try again later or download an older version of this file.", "contactDownloadDialogProp.title": "The file is still being edited.", "contactPeople.errorMessage.emailOrganisationTaken.prefix": "This email address is already used for a user account in", "contactPeople.errorMessage.emailTaken.company.suffix": ". Please use another email address or remove the Company Contact Person.", "contactPeople.errorMessage.emailTaken.prefix": "This user already represents", "contactPeople.errorMessage.emailTaken.suffix": ". Please use another email address.", "contactPeople.roles.CEO": "CEO", "contactPeople.roles.COO": "COO", "contactPeople.roles.boardMember": "Member of the Board of Directors", "contactPeople.roles.clerk": "Administrator", "contactPeople.roles.commercialManager": "Commercial Director", "contactPeople.roles.consultant": "Management Consultant", "contactPeople.roles.corporateFinanceAdvisor": "Corporate Finance Consultant", "contactPeople.roles.customerRepresentative": "Bank Representative", "contactPeople.roles.generalManager": "Authorized representative", "contactPeople.roles.managingDirector": "Managing Director", "contactPeople.roles.other": "Other", "contactPeople.roles.shareholder": "Shareholder", "contactPeople.roles.supervisorBoardMember": "Board of Directors", "contactPeople.roles.treasurer": "Treasurer", "contactPeople.roles.vicePresident": "Vice President", "contactPeople.rolesCFO": "CFO", "contactPerson.actionMenu.label.addUser": "Add as a contact person", "contactPerson.actionMenu.label.removeUser": "Remove as a contact person", "contactPerson.card.label.removeUser": "Remove user", "contract-create.modal.search.placeholder": "Search", "contract.contractDetailsModal.annulmentButton": "Void", "contract.create.modal.backButton": "Back", "contract.create.modal.closeButton": "Close", "contract.create.modal.firstStep": "Create contract with or without reference to a financing project", "contract.create.modal.firstStep.button.forward": "Next", "contract.create.modal.firstStep.withoutFininacingVolume": "Without reference to a case", "contract.create.modal.fourthStep": "Review and submit", "contract.create.modal.fourthStep.button.forward": "Submit", "contract.create.modal.header.ID": "ID", "contract.create.modal.header.finiancingVolume": "Financing volume", "contract.create.modal.header.signers": "Company", "contract.create.modal.label.enterUsernameOrEmail": "Enter username or email", "contract.create.modal.label.search": "Search", "contract.create.modal.noSearchResultsFound": " No search results found ", "contract.create.modal.notRegistered.nonNeoshare.user.email.message": "Email not registered. Use this form to invite users.", "contract.create.modal.openDocumentEditor": "Signature Placement", "contract.create.modal.participant": " Participant", "contract.create.modal.secondStep": "Assign signer*", "contract.create.modal.secondStep.button.forward": "Next", "contract.create.modal.secondStep.contractTitle": "Contract title", "contract.create.modal.secondStep.fileInput.errorMessage.tooBigSize": "The file is too large. The maximum file size is 15 MB.", "contract.create.modal.secondStep.signingDay": "Date of signature", "contract.create.modal.secondStep.signingDay.errorMessage.invalidDate": "Invalid date", "contract.create.modal.secondStep.unsignedExample": "Unsigned copy", "contract.create.modal.signer": " Signer ", "contract.create.modal.stepThird.search.placeholder": "Name or email address", "contract.create.modal.stepThird.title": " Assign signer\r\n", "contract.create.modal.thirdStep": "Enter contract details", "contract.create.modal.thirdStep.button.forward": "Review", "contract.create.modal.title": " Create contract ", "contract.filter.header.label1": " Status\r\n", "contract.filter.header.label2": " Company\r\n", "contract.filter.label.without": "Without reference to a case", "contract.heading.noContractsOption": " There are currently no contracts available. ", "contract.status.AuthoritativeCopy": "Pending", "contract.status.autoResponded": "Pending", "contract.status.completed": "Signed", "contract.status.correct": "Pending", "contract.status.declined": "Pending", "contract.status.deleted": "Pending", "contract.status.delivered": "Pending", "contract.status.draft": "Pending", "contract.status.faxPending": "Pending", "contract.status.filter.all": "All documents", "contract.status.filter.completed": "Signed", "contract.status.filter.extendedPending": "Pending", "contract.status.filter.pending": "Pending", "contract.status.filter.voided": "Voided", "contract.status.sent": "Pending", "contract.status.signed": "Signed", "contract.status.successfullySigned": "Validating", "contract.status.template": "Pending", "contract.status.test": "Pending", "contract.status.timedOut": "Pending", "contract.status.transferCompleted": "Pending", "contract.status.voided": "Voided", "contract.table.list.header.createdBy": "Created by", "contract.table.list.header.createdOn": "Created on", "contract.table.list.header.financingVolume": "Financing volume", "contract.table.list.header.signingDay": "Date of signature", "contract.table.list.header.status": "Status", "contract.table.list.header.title": "Contract title", "contractCard.reject.description": "The names or email addresses of the signing parties do not match the current user. Therefore, you are not eligible to sign this document. To ensure the document's security and authenticity, please reject the document now.", "contractCard.reject.title": "Invalid Signer Access", "contractList.placeholder.search": "Search...", "contractList.search.button.label": "Create", "contractList.title": "Contracts", "contractManagement.contract.businessCaseSelectableItem.id": " ID ", "contractManagement.contract.businessCaseSelectableItem.title": " Financing volume ", "contractManagement.contract.showUserWithCustomerInModal.label.dateSigned": " Signed on ", "contractManagement.contract.showUserWithCustomerInModal.label.pending": "Pending", "contractManagement.contract.showUserWithCustomerInModal.label.voided": "Voided", "contractManagement.contract.showUserWithCustomerInModal.tooltip": " Delivered on ", "contractManagement.exportChat": "Export chat", "contractManagement.template.templateCard.createdBy": "Created by", "contractManagement.template.templateCard.dropDownItem1": "<PERSON><PERSON>", "contractManagement.template.templateCard.dropDownItem2": "Archive", "contractManagement.template.templateCard.dropDownItem3": "Reactivate", "contractManagement.template.templateCard.dropDownItem4": "Publish", "contractManagement.template.templateCard.updatedBy": "Changed by", "contractManagement.template.templateModal.create": "Create new template", "contractManagement.template.templateModal.description.label": " Description ", "contractManagement.template.templateModal.header": "Edit template information", "contractManagement.template.templateModal.title.label": " Tile of template* ", "contractManagement.template.templateSideFilter.label.all": "All templates", "contractManagement.template.templateSideFilter.label.archived": "Archived", "contractManagement.template.templateSideFilter.label.draft": "Draft", "contractManagement.template.templateSideFilter.label.published": "Published", "contractManagement.visualizeContractPreview.dt1": " Company ", "contractManagement.visualizeContractPreview.dt2": " Financing volume ", "contractManagement.visualizeContractPreview.dt3": "ID", "contractManagement.visualizeContractPreview.label.button": "Download", "contractManagement.visualizeContractPreview.title": " Financing case details ", "contractManagement.visualizeContractPreview.title.dt1": " Contract title ", "contractManagement.visualizeContractPreview.title.dt2": " Date of signature ", "contractManagement.visualizeContractPreview.title.dt3": " Unsigned copy ", "contractManagement.visualizeContractPreview.title2": " Contract details ", "contractManagement.visualizeContractPreview.title3": " Signer ", "contractSignersList.label.tooltipContent": " Signed on ", "contractSignersList.label.tooltipContent.delivered": " Delivered on ", "contractStatus.envelopeExpired": "Expired", "contractStatus.filter.label.notSignedByMe": "To be signed by me", "contractStatus.filter.label.pending": "All", "contractStatus.filter.label.signedByMe": "Signed from me", "contractStatus.manuallyVoided": "Voided by <PERSON><PERSON><PERSON>", "contractStatus.voidedBySigner": "Voided by <PERSON><PERSON>", "contracts.contractDetailsModal.contractDataHeader": " Contract details ", "contracts.contractDetailsModal.contractTitle": "Contract title", "contracts.contractDetailsModal.signedExample": "Signed copy", "contracts.contractDetailsModal.signerAndStatus": " Signer and Status ", "contracts.contractDetailsModal.signingDate": "Date of signature", "contracts.contractDetailsModal.title": " Contract details ", "contracts.contractDetailsModal.title.financingVolume": "Financing volume", "contracts.contractDetailsModal.title.finiancingDetails": " Financing case details ", "contracts.contractDetailsModal.title.signers": "Company", "contracts.contractDetailsModal.unsignedExample": "Unsigned copy*", "contracts.contractsDetailsModal.downloadButton": "Download", "copyInformation.fields.modal.copy": "Copy", "copyInformation.fields.modal.text": "Select a group to which you want to copy the Data Room fields ", "copyInformationModal.groupSelectOptions.copyDataRoomFieldsText": "Copy Data Room fields", "copyInformationModal.groupSelectOptions.existingGroupText": "Existing group", "copyInformationModal.groupSelectOptions.newGroupText": "New group", "coreBanking.expirationDate": "Expiration date", "coreBanking.generateToken.buttonLabels.generate": "Generate", "coreBanking.generateToken.daysUntilExpiryText": "Days until expiry", "coreBanking.generateToken.description": "The token has a 30-day expiration period", "coreBanking.generateToken.header": "Generate integration token", "coreBanking.generateToken.information": "The token will be used for authentification in order to access the neoshare data. Please provide an expiration date to proceed.", "coreBanking.generateToken.maximumDaysToExpirationText": "The activation period must be between 1 and {$PH} days", "coreBanking.generateToken.modal.information": "Generating a new token will automatically replace the current one.", "coreBanking.generateToken.modal.question": "Are you sure you want to proceed?", "coreBanking.generateToken.tokenExpiredMessage": "Your token is no longer valid. Please generate a new one to continue the transfer to the core banking system.", "coreBanking.viewToken.activeTokenLabel": "Active token", "coreBanking.viewToken.buttonLabels.generateNewToken": "Generate new token", "coreBanking.viewToken.information": "You can download the token only once. Generating а new token would automatically deactivate the previous one.", "coreBanking.viewToken.tokenExpirationDaysLeftMessage": "This token will expire in {$PH} days. Please renew your token to continue the authorization process to the core banking system.", "coreBanking.viewToken.tokenExpirationIsTodayMessage": "This token will expire today. Please renew your token to continue the authorization process to the core banking system.", "coreBanking.viewToken.tokenExpirationOnlyOneDayLeftMessage": "This token will expire in 1 day. Please renew your token to continue the authorization process to the core banking system. ", "coreBankingLabel": "Core banking system", "coreBankingMessage": "Allow the information transfer from neoshare to the core banking system by generating a token. The token will be used for authentification in order to access the neoshare data.", "create.invitation.addUnregisteredUser": "Add unregistered user", "create.invitation.label": "Create invitation", "create.nonNeoshare.user.additional.information": "Add additional information", "createBusinessCase.cancelBusinessCase": " Are you sure you want to cancel the creation of this case? ", "createBusinessCase.cancelBusinessCase.button.label.cancel": "No", "createBusinessCase.cancelBusinessCase.button.label.confirm": "Yes, I am sure", "createBusinessCase.checkbox.label.isCADRLinked": "Link enterprise data room.", "createBusinessCase.createBusinessCaseFormFooter.button.label.back": "Back", "createBusinessCase.createBusinessCaseFormFooter.button.label.back2": "Back", "createBusinessCase.createBusinessCaseFormFooter.button.label.continue": "Next", "createBusinessCase.createBusinessCaseFormFooter.button.label.submit2": "Create", "createBusinessCase.errorMessage.selectOneCheckbox": "At least one component must be selected", "createBusinessCase.formField": "Company*", "createBusinessCase.formField.caseType": "Case type", "createBusinessCase.formField.label": " Financing volume* ", "createBusinessCase.formField.label.signer": " User ", "createBusinessCase.formField.own-capital": "Own capital", "createBusinessCase.formField.real-estate-total-financing-volume": "Total investment amount", "createBusinessCase.formField.select": "Add users from your organization", "createBusinessCase.formField.total-financing-volume": "Financing volume", "createBusinessCase.header": " Create financing case ", "createBusinessCase.info.message": "You can copy the complete case or just selected parts of it. The default settings for creating a new case will be applied over any deselected components.", "createBusinessCase.label": " Template*", "createBusinessCase.label.buttonLink": "Create company", "createBusinessCase.ownCapacityField.projectNameFieldLabel": "Project name", "createBusinessCase.ownCapacityField.valueBiggerThanTotalError": "The own capital cannot be bigger than the total investment amount", "createBusinessCase.placeholder.select": "Search company", "createBusinessCase.subHeader": " Please select one of the following options: ", "createBusinessCase.text": "All saved templates from you and your colleagues can be found here.", "createBusinessCase.toast.error": "Something went wrong.", "createBusinessCase.toolTip.text": "You decide which company from your list of corporate clients to finance.", "createCompany.select.addTagText": "Add company", "createCompany.select.placeholder": "Search company", "createCustomer.modal.errorMessage.bicAlreadyExists": "This BIC is  already assigned.", "createInvoice.button": "Make payment", "createInvoice.form.info": "After you make the payment, we will create an automatic invoice. ", "createInvoice.form.invoiceLast48H": "Attention, your last voluntary payment was less than 48 hours ago. \r\n{$LINE_BREAK}", "createInvoice.form.title": "Enter amount", "createInvoice.span": "On", "createInvoice.span2": "a payment of", "createInvoice.span3": "was submitted", "createInvoice.subTitle": "With a voluntary payment you support the work of our startup.", "createInvoice.title": "Would you like to support our work? ", "createUsageContract.missingUsageSignersAlert": "Usage contract signer is not available for this customer.", "createUsageContractModal.title": "Usage Contract data", "createUserModal.title": "User data", "createUserModal.validationError.noRoleGiven": "Please select at least one role.", "currentPasswordIncorrect": "Wrong password!", "customer.list.snapshot.description": "List of customers you have selected and the organizations they are partnering with", "customer.list.snapshot.tab.addedByMe": "Added by you", "customer.list.snapshot.tab.autoAdded": "Auto added", "customer.list.snapshot.tab.autoAddedCustomers": "Auto added customers", "customer.type.bank": "Bank", "customer.type.corporate": "Corporate", "customer.type.fsp": "Financial service provider ", "customer.type.immo": "Real estate", "customerCollaboration.tooltip.notAllowToBlock": "Currently, you cannot block the collaboration with this organization, as you are jointly working on active cases", "customerDetails.addressField.address": "Address", "customerDetails.deleteLogoPhoto.tooltip": "Delete logo", "customerDetails.deleteLogoPhoto.tooltip:Logo löschen": "Delete logo", "customerDetails.mfa.description": "For security reasons we advice you to keep it enabled.", "customerDetails.mfa.disabled.tooltip": "Feature currently unavailable.", "customerDetails.mfa.header": "Multi-factor Authentication", "customerDetails.mfa.options.description": "Select which options you want to enable:", "customerDetails.numberField.money": "Balance sheet total", "customerDetails.numberField.numberOfEmployees": "Number of employees", "customerDetails.textField.name": "Organisation name", "customerDetails.uploadLogoPhoto.tooltip": "Upload logo", "customerDetailsForm.title": "Organisation logo", "customerDetailsForm.toast.error": "Customer data could not be saved. Please check your entry.", "customerDetailsForm.toast.success": "Action carried out successfully.", "customerDetailsForm.website": "Website", "customerKpi.tab.description": "When you enable or disable a KPI at the organizational level, it will automatically apply to all business cases within the organization.", "customerKpi.tab.label": "Manage KPI", "customerList.EditDetails": "Edit details", "customerList.disableUserLabel": "Deactivate User", "customerList.disableUserTooltip": "This user cannot be deactivated as this organisation currently lacks a platform manager.", "customerList.enableUserLabel": "Activate User", "customerList.userUpdate.toast.failure": "An error occurred, please try again.", "customerList.userUpdate.toast.success": "User updated successfully", "customerMasterData.kpiSettings.list.actualAnnualNetColdRentPerSquareMeterOfRentalSpace": "Actual annual net cold rent per square metre of rental space", "customerMasterData.kpiSettings.list.annualNetColdRentPerSquareMeterOfRentalSpaceTarget": "Target annual net cold rent per square metre of rental space", "customerMasterData.kpiSettings.list.capitalServiceSurplusShortfallWithActualAnnualNetColdRent": "Capital service surplus/shortfall with actual annual net cold rent", "customerMasterData.kpiSettings.list.capitalServiceSurplusShortfallWithTargetAnnualNetColdRent": "Capital service surplus/shortfall with target annual net cold rent", "customerMasterData.kpiSettings.list.costsPerQmGrossFloorArea": "Costs per QM gross floor area (based on total investment amount)", "customerMasterData.kpiSettings.list.costsPerQmRentalSpace": "Costs per QM rental space (based on the total investment amount)", "customerMasterData.kpiSettings.list.debtRatio": "Debt ratio", "customerMasterData.kpiSettings.list.dscr": "DSCR Actual annual net cold rent ", "customerMasterData.kpiSettings.list.equityMultiplierForContractedSalesProceeds": "Equity multiplier for contracted sales proceeds", "customerMasterData.kpiSettings.list.equityMultiplierForPlannedSalesProceeds": "Equity multiplier for planned sales proceeds", "customerMasterData.kpiSettings.list.equityRatio": "Equity ratio", "customerMasterData.kpiSettings.list.ltcAtTotalInvestmentCosts": "LTC", "customerMasterData.kpiSettings.list.ltvAtContractedSalesProceeds": "LTV at contracted sales proceeds", "customerMasterData.kpiSettings.list.ltvAtEvaluationMortgageLendingValue": "LTV at mortgage lending value", "customerMasterData.kpiSettings.list.ltvAtMarketValue": "LTV at market value", "customerMasterData.kpiSettings.list.ltvAtPlannedSalesProceeds": "LTV at planned sales proceeds", "customerMasterData.kpiSettings.list.ltvAtRealisticSalesProceeds": "LTV at realistic sales proceeds", "customerMasterData.kpiSettings.list.multiplierOfActualAnnualNetColdRentToEvaluationMortgageLendingValue": "Multiplier of actual annual net cold rent to mortgage lending value", "customerMasterData.kpiSettings.list.multiplierOfActualAnnualNetColdRentToMarketValue": "Multiplier of actual annual net cold rent to market value", "customerMasterData.kpiSettings.list.multiplierOfActualAnnualNetColdRentToTotalInvestmentAmount": "Multiplier of actual annual net cold rent to the total investment amount", "customerMasterData.kpiSettings.list.multiplierOfTargetAnnualNetColdRentToContractedSalesProceeds": "Multiplier of target annual net cold rent to the contracted sales proceeds", "customerMasterData.kpiSettings.list.multiplierOfTargetAnnualNetColdRentToEvaluationMortgageLendingValue": "Multiplier of target annual net cold rent to mortgage lending value", "customerMasterData.kpiSettings.list.multiplierOfTargetAnnualNetColdRentToMarketValue": "Multiplier of target annual net cold rent to market value", "customerMasterData.kpiSettings.list.multiplierOfTargetAnnualNetColdRentToPlannedSalesProceeds": "Multiplier of target annual net cold rent to planned sales proceeds", "customerMasterData.kpiSettings.list.multiplierOfTargetAnnualNetColdRentToRealisticSalesProceeds": "Multiplier of target annual net cold rent to the realistic sales proceeds", "customerMasterData.kpiSettings.list.multiplierOfTargetAnnualNetColdRentToTotalInvestmentAmount": "Multiplier of target annual net cold rent to the total investment amount", "customerMasterData.kpiSettings.list.planProjectProfitAmount": "Plan project profit €", "customerMasterData.kpiSettings.list.planProjectProfitPercentage": "Plan project profit %", "customerMasterData.kpiSettings.list.pricePerQmLand": "Price per QM land (based on the total investment amount)", "customerMasterData.kpiSettings.list.projectProfitContractedSalesProceedsAmount": "Project profit with contracted sales proceeds €", "customerMasterData.kpiSettings.list.projectProfitContractedSalesProceedsPercentage": "Project profit with contracted sales proceeds %", "customerMasterData.kpiSettings.list.projectProfitWithContractedSalesProceedsAmount": "Project profit with contracted sales proceeds €", "customerMasterData.kpiSettings.list.projectProfitWithContractedSalesProceedsPercentage": "Project profit with contracted sales proceeds %", "customerMasterData.kpiSettings.list.projectProfitWithRealisticSalesProceedsAmount": "Project profit with realistic sales proceeds €", "customerMasterData.kpiSettings.list.projectProfitWithRealisticSalesProceedsPercentage": "Project profit with realistic sales proceeds %", "customerMasterData.kpiSettings.list.propertyRelatedDebtServiceCapacityActual": "Property-related debt service capacity for actual annual net rental income total", "customerMasterData.kpiSettings.list.propertyRelatedDebtServiceCapacityTarget": "Property-related debt service capacity for target annual net rental income total", "customerMasterData.kpiSettings.list.propertyRelatedUnsecuredPortionAt60EvaluationMortgageLendingValue": "Property-related unsecured portion at 60% mortgage lending value", "customerMasterData.kpiSettings.list.propertyRelatedUnsecuredPortionAt60MortgageLendingValue": "Property-related unsecured portion at 60% mortgage lending value", "customerMasterData.kpiSettings.list.propertyRelatedUnsecuredPortionAt70EvaluationMortgageLendingValue": "Property-related unsecured portion at 70% mortgage lending value", "customerMasterData.kpiSettings.list.propertyRelatedUnsecuredPortionAt70MortgageLendingValue": "Property-related unsecured portion at 70% mortgage lending value", "customerMasterData.kpiSettings.list.propertyRelatedUnsecuredPortionAt80EvaluationMortgageLendingValue": "Property-related unsecured portion at 80% mortgage lending value", "customerMasterData.kpiSettings.list.propertyRelatedUnsecuredPortionAt80MortgageLendingValue": "Property-related unsecured portion at 80% mortgage lending value", "customerMasterData.kpiSettings.list.realEastate.ltvAtMarketValue": "LTV at market value ", "customerMasterData.kpiSettings.list.realEastate.multiplierOfActualAnnualNetColdRentToMarketValue": "Multiplier of actual annual net cold rent to market value", "customerMasterData.kpiSettings.list.realEastate.multiplierOfTargetAnnualNetColdRentToMarketValue": "Multiplier of target annual net cold rent to market value ", "customerMasterData.kpiSettings.list.realEastate.returnAtMarketValueBasedOnActualAnnualNetRentalIncomeTotal": "Return at market value based on actual annual net rental income total", "customerMasterData.kpiSettings.list.realEastate.returnAtMarketValueBasedOnTargetAnnualNetRentalIncomeTotal": "Return at market value based on target annual net rental income total", "customerMasterData.kpiSettings.list.realEastate.roeAtPlannedProjectProfit": "ROЕ at planned project profit", "customerMasterData.kpiSettings.list.rentalSpaceFactorSpaceEfficiency": "Rental space factor / space efficiency", "customerMasterData.kpiSettings.list.returnAtMarketValueBasedOnActualAnnualNetRentalIncomeTotal": "Return at market value based on actual annual net rental income total", "customerMasterData.kpiSettings.list.returnAtMarketValueBasedOnTargetAnnualNetRentalIncomeTotal": "Return at market value based on target annual net rental income total", "customerMasterData.kpiSettings.list.returnOfTotalInvestmentCostsBasedOnActualAnnualNetRentalIncomeTotal": "Return of total investment costs based on actual annual net rental income total", "customerMasterData.kpiSettings.list.returnOfTotalInvestmentCostsBasedOnTargetAnnualNetRentalIncomeTotal": "Return of total investment costs based on target annual net rental income total", "customerMasterData.kpiSettings.list.returnOnContractedSalesProceedsToTargetAnnualNetColdRent": "Return on contracted sales proceeds to target annual net cold rent", "customerMasterData.kpiSettings.list.returnOnPlanSalesProceedsToTargetAnnualNetColdRent": "Return on plan sales proceeds to target annual net cold rent", "customerMasterData.kpiSettings.list.returnOnRealisticSalesProceedsToTargetAnnualNetColdRent": "Return on realistic sales proceeds to target annual net cold rent", "customerMasterData.kpiSettings.list.roeAtContractedSalesProceeds": "ROE at contracted sales proceeds", "customerMasterData.kpiSettings.list.roeAtPlannedProjectProfit": "ROЕ at planned project profit", "customerMasterData.kpiSettings.list.roeAtPlannedSalesProceeds": "ROE at planned sales proceeds", "customerMasterData.kpiSettings.list.standardLandValueForComparison": "Standard land value for comparison", "customerMasterData.kpiSettings.list.targetAnnualNetColdRentPerSquareMeterOfRentalSpace": "Target annual net cold rent per square metre of rental space", "customerMasterData.kpiSettings.list.utilizationOfDebtServiceLimitForAnnualNetRentalIncomeTotalActual": "Utilization of debt service limit for actual annual net rental income total", "customerMasterData.kpiSettings.list.utilizationOfDebtServiceLimitForAnnualNetRentalIncomeTotalTarget": "Utilization of debt service limit for target annual net rental income total", "customerMasterData.pageTitle": "Master data", "customerMasterData.tab.blocked": "Blocked organisations", "customerMasterData.tab.collaboration": "Collaborations", "customerMasterData.tab.collaboration.placeholder.search": "Filter banks...", "customerMasterData.tab.collaboration.table.column.employees": "Employees", "customerMasterData.tab.collaboration.table.column.name": "Name", "customerMasterData.tab.collaboration.table.column.status": "Marketplace collaboration", "customerMasterData.tab.collaboration.table.column.totalAssets": "Balance sheet total", "customerMasterData.tab.customerDetails": "Organisation details", "customerMasterData.tab.customerDetails.label.button.rework": "Edit data", "customerMasterData.tab.customerDetails.label.button.save": "Save data", "customerMasterData.tab.customerDetails.lastUpdated": "Last change on", "customerMasterData.tab.kpiSettings": "KPI settings", "customerOrganisationData.kpiSettings.enabled": "Enable", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMaxHighest": "The MAX value of the first range cannot be higher than {$PH}.", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMaxHighestDesc": "The MAX value of the first range must be {$PH} or lower.", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMaxLowest": "The MAX value of the first range cannot be lower than {$PH}.", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMaxLowestDesc": "The MAX value of the first range must be {$PH} or higher.", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMinHighest": "The MIN value of the first range cannot be higher than {$PH}.", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMinHighestDesc": "The MIN value of the first range must be {$PH} or lower.", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMinLowest": "The MIN value of the first range cannot be lower than {$PH}.", "customerOrganisationData.kpiSettings.kpiRanges.error.firstMinLowestDesc": "The MIN value of the first range must be {$PH} or higher.", "customerOrganisationData.kpiSettings.kpiRanges.error.notAscending": "The range field values (of all three ranges) must be in ascending order", "customerOrganisationData.kpiSettings.kpiRanges.error.notDescending": "\t\r\nThe range field values (of all three ranges) must be in descending order", "customerOrganisationData.kpiSettings.kpiRanges.error.required": "All range fields must be filled out", "customerOrganisationData.kpiSettings.kpiRanges.error.secondMaxHighest": "The MAX value of the second range cannot be higher than {$PH}.", "customerOrganisationData.kpiSettings.kpiRanges.error.secondMaxHighestDesc": "The MAX value of the second range must be {$PH} or lower.", "customerOrganisationData.kpiSettings.kpiRanges.error.secondMaxLowest": "The MAX value of the second range cannot be lower than {$PH}.", "customerOrganisationData.kpiSettings.kpiRanges.error.secondMaxLowestDesc": "The MAX value of the second range must be {$PH} or higher.", "customerOrganisationData.kpiSettings.kpiRanges.error.thirdMaxHighest": "The MAX value of the third range cannot be higher than {$PH}.", "customerOrganisationData.kpiSettings.kpiRanges.error.thirdMaxHighestDesc": "The MAX value of the third range must be {$PH} or lower.", "customerOrganisationData.kpiSettings.kpiRanges.error.thirdMaxLowest": "The MAX value of the third range cannot be lower than {$PH}.", "customerOrganisationData.kpiSettings.kpiRanges.error.thirdMaxLowestDesc": "The MAX value of the third range must be {$PH} or higher.", "customerOrganisationData.kpiSettings.kpiRanges.header": "Set ranges", "customerOrganisationData.kpiSettings.kpiRanges.rangeDirection.ascending": "Ranges must be set in ascending order", "customerOrganisationData.kpiSettings.kpiRanges.rangeDirection.descending": "Ranges must be set in descending order", "customerOrganisationData.kpiSettings.kpiRanges.rangeLabel.attention": "Check exclusion criteria", "customerOrganisationData.kpiSettings.kpiRanges.rangeLabel.moderate": "Check permissible deviation", "customerOrganisationData.kpiSettings.kpiRanges.rangeLabel.optimal": "Compliant with specifications", "customerOrganisationData.kpiSettings.kpiRanges.saveChanges.failure": "An error occurred, please try again later.", "customerOrganisationData.kpiSettings.kpiRanges.saveChanges.success": "Changes saved successfully.", "customerOrganisationData.pageTitle": "Organisation data", "customerOrganisationData.security.mfa.authOptions": "Authentication options", "customerOrganisationData.security.mfa.description": "For security reasons we advice you to keep it enabled.", "customerOrganisationData.security.mfa.enable": "Enable", "customerOrganisationData.security.mfa.header": "Multi-factor authentication (MFA)", "customerOrganisationData.tab.blocked": "Blocked organisations", "customerOrganisationData.tab.masterData": "Master data", "customerOrganisationData.tab.security": "Security", "customerRole.editor": "Editor", "customerRoles.observer": "Observer", "customerStatus.Guest": "Guest", "customerStatus.Regular": "Regular", "customerStatus.model.contractSent": "Initial NV - sent", "customerStatus.model.contractSignedByAll": "Initial NV- signed by all", "customerStatus.model.contractSignedByCustomer": "Initial NV- signed by Bank", "customerStatus.model.initialized": "Initialized", "customerStatus.model.initializing": "Initialize", "customerTable.columns.createdOn": "Created on", "customerTable.columns.customerKey": "Customer ID", "customerTable.columns.customerType": "Customer type", "customerTable.columns.name": "Customer name", "customerTable.columns.source": "Source", "customerTable.columns.sourceType": "Source type", "customerTemplates.tabs.companyAndCaseTemplates": "Company & Case templates", "customerTemplates.tabs.documentTemplates": "Document templates", "customers.sourceType.mainSystem": "Main system", "dasboard.businessCase.leaderContactInfo.mobilePhone": "Mobile", "dasboard.businessCase.leaderContactInfo.phone": "Phone", "dashbaord.bank.partnersClientsWidget.clients": "Clients", "dashbaord.bank.partnersClientsWidget.financingPartner": "Financing partners", "dashboard.BusinessCase.administration.fundingCase.button.conclude": "Close", "dashboard.BusinessCase.administration.fundingCase.button.reactivate": "Reactivate", "dashboard.BusinessCase.administration.fundingCase.description": " You have the option to close your financing case or to deactivate it by choosing \"Cancel\". However, closing a case is only possible if the market visibility of your financing case has been set to \"Private\". ", "dashboard.BusinessCase.administration.fundingCase.isReactivatable.description": " You have the option to reactivate your already closed or canceled financing case. However, reactivating a project is only possible if the market visibility of your financing case has been set to \"Private. ", "dashboard.BusinessCase.administration.sectionText.duplicateCase": "You can copy the complete case or just selected parts of it.", "dashboard.activityLogs.header.recentChanges": " Newest changes ", "dashboard.activityLogs.noParticipationYet": " As soon as you participate in a financing case, all relevant activities will be displayed here. ", "dashboard.activityLogs.weekdays.friday": "Friday", "dashboard.activityLogs.weekdays.monday": "Monday", "dashboard.activityLogs.weekdays.saturday": "Saturday", "dashboard.activityLogs.weekdays.sunday": "Sunday", "dashboard.activityLogs.weekdays.thursday": "Thursday", "dashboard.activityLogs.weekdays.tuesday": "Tuesday", "dashboard.activityLogs.weekdays.wednesday": "Wednesday", "dashboard.analytic.noClassified": "not classified", "dashboard.bank.analytics.assetClasses.title": "Asset class", "dashboard.bank.analytics.assetClasses.tooltipPrefix": "Amount", "dashboard.bank.analytics.assetClasses.tooltipSuffix": "of the total own investment amount of all asset classes in real estate financing cases", "dashboard.bank.analytics.cases": "Cases", "dashboard.bank.analytics.certified.title": "Certified", "dashboard.bank.analytics.energy.title": "Energy efficiency class", "dashboard.bank.analytics.historicalInvestment.title": "Historical development of the total own investment amount ", "dashboard.bank.analytics.nonCertified.title": "Not certified", "dashboard.bank.analytics.ownInvestmentAmount.title": "Own investment amount by object type", "dashboard.bank.analytics.title": "Analytics", "dashboard.bank.analytics.totalInvestmentAmount.ownInvestmentAmount.tooltip": "Amount", "dashboard.bank.analytics.totalInvestmentAmount.totalOwnInvesmentAmount.tooltip": "of the total own investment amount of all property types in real estate financing cases", "dashboard.bank.casesTable.general.columns.assetClass": "Asset class", "dashboard.bank.casesTable.general.columns.caseStatus": "Case status", "dashboard.bank.casesTable.general.columns.caseType": "Case type", "dashboard.bank.casesTable.general.columns.company": "Company", "dashboard.bank.casesTable.general.columns.creationDate": "Creation date", "dashboard.bank.casesTable.general.columns.ecoreScoring": "ECORE-SCORING", "dashboard.bank.casesTable.general.columns.energyEfficiencyClass": "Energy efficiency class", "dashboard.bank.casesTable.general.columns.esgCertified": "ESG-Certified", "dashboard.bank.casesTable.general.columns.financingType": "Financing type", "dashboard.bank.casesTable.general.columns.objectAddress": "Object address", "dashboard.bank.casesTable.general.columns.objectName": "Project name", "dashboard.bank.casesTable.general.columns.objectType": "Object type", "dashboard.bank.casesTable.general.columns.ownInvestmentAmount": "Own investment amount", "dashboard.bank.casesTable.kpi.columns.caseStatus": "Case status", "dashboard.bank.casesTable.kpi.columns.caseType": "Case type", "dashboard.bank.casesTable.kpi.columns.creationDate": "Creation date", "dashboard.bank.casesTable.kpi.columns.financingType": "Financing type", "dashboard.bank.casesTable.kpi.columns.ltcAtMarketValue": "LTV at market value", "dashboard.bank.casesTable.kpi.columns.ltcAtTotalInvestmentAmount": "LTC", "dashboard.bank.casesTable.kpi.columns.multiplierMarketValueActual": "Multiplier of actual annual net cold rent to market value", "dashboard.bank.casesTable.kpi.columns.multiplierMarketValueTarget": "Multiplier of target annual net cold rent to market value", "dashboard.bank.casesTable.kpi.columns.multiplierTotalInvestmentAmountActual": " Multiplier of actual annual net cold rent to the total investment amount", "dashboard.bank.casesTable.kpi.columns.multiplierTotalInvestmentAmountTarget": "Multiplier of target annual net cold rent to the total investment amount", "dashboard.bank.casesTable.kpi.columns.objectAddress": "Object address", "dashboard.bank.casesTable.kpi.columns.projectName": "Project name", "dashboard.bank.casesTable.kpi.columns.projectProfitPercentage": "Project profit with realistic sales proceeds %", "dashboard.bank.casesTable.kpi.columns.propertyDebtForAnnualIncomeActual": "Property-related debt service capacity for Annual net rental income total (actual)", "dashboard.bank.casesTable.kpi.columns.propertyDebtForAnnualIncomeTarget": "Property-related debt service capacity for Annual net rental income total (target)", "dashboard.bank.casesTable.kpi.columns.propertyUnsecuredPortion60Percent": "Property-related unsecured portion at 60% mortgage lending value", "dashboard.bank.casesTable.kpi.columns.propertyUnsecuredPortion70Percent": "Property-related unsecured portion at 70% mortgage lending value", "dashboard.bank.casesTable.kpi.columns.propertyUnsecuredPortion80Percent": "Property-related unsecured portion at 80% mortgage lending value", "dashboard.bank.casesTable.kpi.columns.rentalSpaceFactor": "Rental space factor / space efficiency", "dashboard.bank.casesTable.kpi.columns.utilizationOfDebtActual": "Utilization of debt service limit for Annual net rental income total (actual)", "dashboard.bank.casesTable.kpi.columns.utilizationOfDebtTarget": "Utilization of debt service limit for Annual net rental income total (target)", "dashboard.bank.financingPartnerClientWidget.client.cases": "Cases", "dashboard.bank.financingPartnerClientWidget.client.name": "Name", "dashboard.bank.financingPartnerClientWidget.client.totalLoanAmount": "Own investment amount", "dashboard.bank.historicalDevelopmentInvestmentAmountWidget.title": "Historical development of the total own investment amount", "dashboard.bank.portfolio.realEstateKpiLocationOverviewWidget.allConsortialCases": "Аll consortium cases", "dashboard.bank.portfolio.realEstateKpiLocationOverviewWidget.cases": "Cases", "dashboard.bank.portfolio.realEstateKpiLocationOverviewWidget.totalLtv": "Total LTV", "dashboard.bank.portfolio.realEstateKpiLocationOverviewWidget.totalOwnInvestmentAmount": "Total own investment amount", "dashboard.bewerbungenTab.header1": "Case", "dashboard.bewerbungenTab.header5": "Application date", "dashboard.businessCase.Collaboration.tab.applications": " Applications ", "dashboard.businessCase.Collaboration.tab.invitationApplications": "Invitations and applications", "dashboard.businessCase.Collaboration.tab.invitations": " Invitations ", "dashboard.businessCase.Collaboration.tab.myParticipation": " My participation ", "dashboard.businessCase.actions": " Actions ", "dashboard.businessCase.administration.button.label": "Add user", "dashboard.businessCase.administration.button.label.viewDataRoom": "View Enterprise Data Room", "dashboard.businessCase.administration.companyContactUsers.addUserLabel": " Add user ", "dashboard.businessCase.administration.companyContactUsers.doesNotHaveUsers": " No user has been given access to the customer portal yet. Please authorize them to do so in", "dashboard.businessCase.administration.companyContactUsers.doesNotHaveUsers.realEstate": "No user has been given access to the partner portal yet. Please authorise them to do so in", "dashboard.businessCase.administration.companyContactUsers.hasCompanyUsersAndNoUsersAssigned.firstPart": " Share information with customer portal users. ", "dashboard.businessCase.administration.companyContactUsers.hasCompanyUsersAndNoUsersAssigned.firstPart.realEstate": "Exchange information with the users of the partner portal.", "dashboard.businessCase.administration.companyContactUsers.hasCompanyUsersAndNoUsersAssigned.secondPart": " Select them here... ", "dashboard.businessCase.administration.companyContactUsers.hasUsers": " Customer portal user ", "dashboard.businessCase.administration.companyContactUsers.hasUsers.realEstate": "Partner portal users", "dashboard.businessCase.administration.contactPersonCard.label": "Contact", "dashboard.businessCase.administration.contactPersonTitle": " Contact ", "dashboard.businessCase.administration.customerUsersTitle": " User ", "dashboard.businessCase.administration.deleteFaq": "Do you want to delete this question?", "dashboard.businessCase.administration.dracoonStatusBox": "Connected", "dashboard.businessCase.administration.dracoonText": " Synchronise your applications with your business case", "dashboard.businessCase.administration.faq.label": "Add question", "dashboard.businessCase.administration.faq.label.addQuestion": "Add question", "dashboard.businessCase.administration.faq.label.editQuestion": "Edit question", "dashboard.businessCase.administration.faq.noQuestions": "You don’t have any questions yet", "dashboard.businessCase.administration.label.notSynched": "Connect", "dashboard.businessCase.administration.platformManagerRemovalDisabled": "This user is a manager and cannot be removed", "dashboard.businessCase.administration.sectionTitle": " Participant ", "dashboard.businessCase.administration.sectionTitle.FAQ": " FAQ ", "dashboard.businessCase.administration.sectionTitle.companyContactPeople": " Customer portal release ", "dashboard.businessCase.administration.sectionTitle.companyContactPeople.realEstate": "Partner portal release", "dashboard.businessCase.administration.sectionTitle.duplicateCase": "Copy case", "dashboard.businessCase.administration.sectionTitle.fundingCase": " Financing case ", "dashboard.businessCase.button.caseTransfer": "Case transfer", "dashboard.businessCase.card.financingVolume": "Financing volume", "dashboard.businessCase.card.toolTipText": "Other company information", "dashboard.businessCase.card.totalInvestmentCosts": "Total investment amount", "dashboard.businessCase.charts.notStructuredText": "Not structured", "dashboard.businessCase.charts.valueZeroOrEmptyErrorText": "The own investment amount cannot be zero", "dashboard.businessCase.collaboration": "Collaboration", "dashboard.businessCase.collaboration.applications.header.tab": " Applications ", "dashboard.businessCase.collaboration.applications.numbersOf.accepted": " Accepted ", "dashboard.businessCase.collaboration.applications.numbersOf.preparation": " In preparation ", "dashboard.businessCase.collaboration.applications.numbersOf.rejected": " Denied ", "dashboard.businessCase.collaboration.applications.numbersOf.submitted": " In process ", "dashboard.businessCase.collaboration.applications.search.placeholder": "Search applications...", "dashboard.businessCase.collaboration.applications.table.columns.customerName": "Organisation", "dashboard.businessCase.collaboration.applications.table.columns.startedOn": "Date", "dashboard.businessCase.collaboration.applications.table.columns.state": "Status", "dashboard.businessCase.collaboration.applications.table.columns.totalParticipationAmount": "Amount", "dashboard.businessCase.collaboration.applications.table.columns.user": "User", "dashboard.businessCase.collaboration.chartData.financing": "Financing", "dashboard.businessCase.collaboration.chartData.myAmount": "My amount", "dashboard.businessCase.collaboration.invitations.caseStatusTag.placeholder": "Select status", "dashboard.businessCase.collaboration.invitations.filter": "Filters", "dashboard.businessCase.collaboration.invitations.filters.title": "Filters", "dashboard.businessCase.collaboration.invitations.label": " Invitations ", "dashboard.businessCase.collaboration.invitations.label.numberOf": " Denied ", "dashboard.businessCase.collaboration.invitations.label.numberOf.assumed": " Accepted ", "dashboard.businessCase.collaboration.invitations.label.numberOf.cancel": " Canceled ", "dashboard.businessCase.collaboration.invitations.label.numberOf.workInProgress": " In process ", "dashboard.businessCase.collaboration.invitations.noCustomers": " You have not selected any organizations ", "dashboard.businessCase.collaboration.invitations.sendInvite": "Invite", "dashboard.businessCase.collaboration.invitations.status.label": "Status", "dashboard.businessCase.collaboration.invitations.table.columns.creationDate": "Date of invitation", "dashboard.businessCase.collaboration.invitations.table.columns.customerName": "Organization", "dashboard.businessCase.collaboration.invitations.table.columns.status": "Status", "dashboard.businessCase.collaboration.invitations.table.columns.users": "Users", "dashboard.businessCase.collaboration.invitations.text2": " There are no results at this time.\r\n", "dashboard.businessCase.collaboration.invitations.typeOfInvitation.header": "Type of invitation", "dashboard.businessCase.collaboration.invitations.uiSelect.label": "Select type", "dashboard.businessCase.collaboration.myPartners.disabledVisibilityMessage": " The marketplace visibility button for your case is inactive because you have already accepted an application or your invitation has been accepted. ", "dashboard.businessCase.collaboration.myPartners.visibilityButton.label.closed": "Private", "dashboard.businessCase.collaboration.myPartners.visibilityButton.label.open": "Public", "dashboard.businessCase.collaboration.myPartners.visibilityChange": "Marketplace visibility", "dashboard.businessCase.collaboration.myPartners.visibilityChange.tooltipTip": "The status of a newly created financing case is by default “private”. Publish the case on the marketplace by enabling the toggle.", "dashboard.businessCase.collaboration.participationAmount": " Edit investment amount ", "dashboard.businessCase.collaboration.participationChart.header": " Amount to be collected ", "dashboard.businessCase.collaboration.participationChart.header2": " Amount collected ", "dashboard.businessCase.collaboration.participationChart.myAmount": " My amount ", "dashboard.businessCase.collaboration.participationChart.participants": " Participant ", "dashboard.businessCase.collaboration.participationChartHeader": " My participation ", "dashboard.businessCase.collaboration.tab.myPartners": "Participants", "dashboard.businessCase.collaboration.text.noApplications": " There are no results at this time.\r\n", "dashboard.businessCase.collaboration.totalParticipation": " Total investment ", "dashboard.businessCase.collected": "Collected", "dashboard.businessCase.dataRoom": "The enterprise Data Room has been added to the funding case, but there are no fields in the enterprise Data Room yet. As soon as fields are added to it, they will be displayed here. ", "dashboard.businessCase.dataRoom.groupPortalActions": " Customer portal actions ", "dashboard.businessCase.dataRoom.groupPortalActions.notVisible": " Invisible ", "dashboard.businessCase.dataRoom.groupPortalActions.realEstate": "Partner Portal Actions", "dashboard.businessCase.dataRoom.groupPortalActions.visibility": " Visible ", "dashboard.businessCase.dataRoom.groupPortalActions.visibility.notVisible": "not visible", "dashboard.businessCase.dataRoom.groupPortalActions.visibilityInCustomerPortal": " Visibility in the customer portal ", "dashboard.businessCase.dataRoom.groupPortalActions.visibilityInCustomerPortal.realEstate": "Visibility in the partner portal", "dashboard.businessCase.dataRoom.placeholder": "Information search...", "dashboard.businessCase.dataRoom.tabs.case": "Case", "dashboard.businessCase.dataRoom.tabs.company": "Company", "dashboard.businessCase.dataRoomTab": "Data room", "dashboard.businessCase.dataRoomTabAndFinancingDetails": "Data room & Financing details", "dashboard.businessCase.downloadDocuments": "Download documents", "dashboard.businessCase.financing.details.nav.financing.structure": "Financing structure", "dashboard.businessCase.financing.details.nav.my.participation": "My participation", "dashboard.businessCase.header.caseHolder": " Case owner ", "dashboard.businessCase.header.onlyReadRights": " Read-only ", "dashboard.businessCase.header.onlyReadRights.tooltip": "No changes are possible in the Data Room because the case has been canceled or closed.", "dashboard.businessCase.header.participant": " Participant ", "dashboard.businessCase.informationLabel.caseStateChanged": "Case status changed", "dashboard.businessCase.invitationToParticipate": " Invitation for participation ", "dashboard.businessCase.lender": "<PERSON><PERSON>", "dashboard.businessCase.loanAmount": "Loan amount ", "dashboard.businessCase.necessary": "Required", "dashboard.businessCase.pdfExport": "Export to PDF", "dashboard.businessCase.percentFinancing": "% of financing volume", "dashboard.businessCase.tabs.administration": "Administration", "dashboard.businessCase.tabs.financingDetails": "Financing details", "dashboard.businessCase.tabs.overview": "Overview", "dashboard.businessCase.teaserExport": "Teaser export", "dashboard.businessCase.templateFields.timeInMonths": "Time in months", "dashboard.businessCase.templateFields.timeinMonths": "Term in months", "dashboard.casesTable.caseType.financing": "Funding", "dashboard.casesTable.caseType.passingOn": "Forwarding", "dashboard.casesTable.columnsFilter.buttonLabel": "Columns", "dashboard.casesTable.columnsFilter.columnsList.title": "Columns", "dashboard.casesTable.financingType.corporate": "Corporate financing", "dashboard.casesTable.financingType.miscellaneous": "Miscellaneous", "dashboard.casesTable.financingType.realEstate": "Real estate financing", "dashboard.casesTable.header.tableType.general": "General", "dashboard.casesTable.header.tableType.kpi": "KPI", "dashboard.casesTable.objectStatus.buildingPermitIssued": "Building permission granted", "dashboard.casesTable.objectStatus.completionDateOfConstructionPhase1": "Completion of construction", "dashboard.casesTable.objectStatus.liquidityEffectiveFullLettingDate": "Full letting date", "dashboard.casesTable.objectStatus.partialBuildingPermitIssued": "Partial building permission granted", "dashboard.casesTable.objectStatus.startOfConstructionPhase1": "Start of construction Phase", "dashboard.casesTable.objectStatus.startOfPlanning": "Start of planning", "dashboard.casesTable.type.general": "General", "dashboard.casesTable.type.kpi": "KPI", "dashboard.collaboration.invitations.placeholder": "Invite organizations...", "dashboard.collaboration.invitations.text": " from the organizations you wish to invite: ", "dashboard.collaboration.myParteners.section.hedaer.newparticipants": " Standard settings for new participants ", "dashboard.collaboration.myPartners.criteria.header": " Applicant criteria ", "dashboard.collaboration.myPartners.criteria.placeholder": "Add condition", "dashboard.collaboration.myPartners.header": " My partners ", "dashboard.collaboration.myPartners.own-participation-amount": "Participation amount", "dashboard.collaboration.myPartners.participantRequirements": "Participant requirements", "dashboard.collaboration.myPartners.participationAmountModal.notDefined": "Not defined", "dashboard.collaboration.myPartners.roles": " Financing share ", "dashboard.collaboration.myPartners.roles.FAQ": " FAQ ", "dashboard.collaboration.myPartners.roles.button.create": "Create", "dashboard.collaboration.myPartners.roles.changeAmountModal.changeParticpantAmountText": "Change Participation Amount", "dashboard.collaboration.myPartners.roles.changeAmountModal.infoText": "The updated investment amount will be displayed on all platform pages where applicable.", "dashboard.collaboration.myPartners.roles.changeAmountModal.participantAmountText": "Amount", "dashboard.collaboration.myPartners.roles.chatPermissions": "Bilateral chats", "dashboard.collaboration.myPartners.roles.dataRoom": " Data room ", "dashboard.collaboration.myPartners.roles.dataRoom.create": " Edit ", "dashboard.collaboration.myPartners.roles.dataRoom2": "Data room", "dashboard.collaboration.myPartners.roles.invitationManagement": "Application / invitation management", "dashboard.collaboration.myPartners.roles.invitationManagement.text.permission": " View ", "dashboard.collaboration.myPartners.roles.invitationManagement.text.permission.administer": "Manage", "dashboard.collaboration.myPartners.roles.invitationManagement.text.permission.preview": "Preview", "dashboard.collaboration.myPartners.roles.invitationManagement.toolTipText": "Participant can see and/or manage the applications/invitations\r\n        of other participants.", "dashboard.collaboration.myPartners.roles.label.rework": " Edit ", "dashboard.collaboration.myPartners.roles.name": " Financing share ", "dashboard.collaboration.myPartners.roles.name.writing": " Writes messages ", "dashboard.collaboration.myPartners.roles.permission": " Edit ", "dashboard.collaboration.myPartners.roles.permissions": "Can see other participants", "dashboard.collaboration.myPartners.roles.permissions.chat": " Cha<PERSON> ", "dashboard.collaboration.myPartners.roles.tooltip.text": "With these settings you allow your partners to conduct bilateral chats with each other.", "dashboard.collaboration.myPartners.section.errorMessage": "Min value cannot exceed max value", "dashboard.collaboration.myPartners.section.field": "Minimum investment amount", "dashboard.collaboration.myPartners.section.field.errorMessage2": "Max value cannot be less than min value", "dashboard.collaboration.myPartners.section.field.tooltip": "The maximum amount your partners can contribute to the financing case.", "dashboard.collaboration.myPartners.section.field2": "Maximum investment amount", "dashboard.collaboration.myPartners.section.maxWarningMessage": "max investment", "dashboard.collaboration.myPartners.section.minWarningMessage": "min investment", "dashboard.collaboration.myPartners.section.name": " Participant requirements ", "dashboard.collaboration.myPartners.section.placeholder": "The smallest possible amount that your partners can contribute to the financing case.", "dashboard.collaboration.myPartners.sectionHeader.details": " Financing details ", "dashboard.contractTemplates.version": " Version ", "dashboard.dataRoom.button.label": "To company", "dashboard.dataRoom.information": " All input fields with this icon are linked to a field in the Data Room. The link will be broken by clicking on the icon. This allows the field to be edited and a new value entered. By clicking anywhere on the page, the new value will  be saved and the input value of that field will be forever separated from the value in the Data Room.", "dashboard.dataRoom.information2": "You can edit the company-related Data Room from the company's page. Click the button if you want to make changes to the company-related Data Room. ", "dashboard.emptyState.corporate": "Create your first real estate financing case to view the full dashboard.", "dashboard.emptyState.information": "Create your first case to view the full dashboard.", "dashboard.emptyState.title": "No information available.", "dashboard.feedback": " Your message could not be sent.", "dashboard.feedback.checkBoxLabel": "Create support request", "dashboard.feedback.errorMessage": "<PERSON><PERSON>", "dashboard.feedback.placeholder": "Your feedback...", "dashboard.feedback.submit": "Submit", "dashboard.feedback.successMessage": "Send new message", "dashboard.feedback.thankYouMessage": " Thank you for your feedback! ", "dashboard.feedback2": " Please try again.", "dashboard.filter.modal": "Status", "dashboard.filter.modal.options.active": "Active", "dashboard.filter.modal.options.inactive": "Inactive", "dashboard.filters.caseStatus.active.title": "Active", "dashboard.filters.caseStatus.closed.title": "Closed", "dashboard.filters.caseStatus.title": "Case status", "dashboard.filters.caseStatusTag.placeholder": "Select status", "dashboard.filters.caseStatusTags.active.applicationSubmitted": "Financing application has been submitted", "dashboard.filters.caseStatusTags.active.caseCreated": "Financing case created", "dashboard.filters.caseStatusTags.active.financingApproved": "Financing approved", "dashboard.filters.caseStatusTags.active.financingDecisionPending": "Financing decision pending", "dashboard.filters.caseStatusTags.active.financingSuccessfullyCompleted": "Financing successfully completed", "dashboard.filters.caseStatusTags.active.structuringOrderIssued": "Structuring order issued", "dashboard.filters.caseStatusTags.active.termSheetAccepted": "Term sheet accepted", "dashboard.filters.caseStatusTags.closed.applicationSubmitted": "Incorrect entry", "dashboard.filters.caseStatusTags.closed.caseCreated": "Object was sold", "dashboard.filters.caseStatusTags.closed.financingApproved": "Financing was repaid", "dashboard.filters.caseStatusTags.closed.financingDecisionPending": "Other", "dashboard.filters.caseStatusTags.closed.structuringOrderIssued": "Financing application was withdrawn", "dashboard.filters.caseStatusTags.closed.termSheetAccepted": "Financing was rejected", "dashboard.filters.caseType.financing.label": "Funding", "dashboard.filters.caseType.passingOn.label": "Forwarding", "dashboard.filters.caseType.title": "Case type", "dashboard.filters.financingType.corporate.label": "Corporate financing", "dashboard.filters.financingType.corporate.label.new": "Corporate", "dashboard.filters.financingType.miscellaneous.label": "Miscellaneous", "dashboard.filters.financingType.realEstate.label": "Real estate financing", "dashboard.filters.financingType.realEstate.label.new": "Real Estate", "dashboard.filters.financingType.title": "Type of financing", "dashboard.filters.financingType.title.new": "Financing type", "dashboard.filters.title": "Filters", "dashboard.filters.totalResults": "selected cases", "dashboard.filters.totalResultsOne": "selected case", "dashboard.financing.coveredCriteria": "Fulfilled", "dashboard.financingDetails.header.creditAmount": " Credit amount ", "dashboard.invitationTaB.header.creationDate": "Date of invitation", "dashboard.map.tooltip.assetClass": "Asset class", "dashboard.map.tooltip.caseId": "Case ID", "dashboard.map.tooltip.objectAddress": "Address", "dashboard.map.tooltip.totalInvestment": "Total investment amount", "dashboard.marketplace.overviewTab.contactPerson": " Contact ", "dashboard.marketplace.overviewTab.contactPerson.noContactPerson": " No contact set up ", "dashboard.mine.revisions.showLess": "Show less", "dashboard.mine.revisions.showMore": "Show more", "dashboard.overview.finiancingDetails.header.editMode": "Edit mode", "dashboard.overview.header.revisions.recentChanges": " Newest changes ", "dashboard.overviewTab.contactPerson": "Contact", "dashboard.propertyLocation.search.placeholder": "Search by location", "dashboard.realEstate.analytics.assetClasses.tooltipPrefix": "Amount", "dashboard.realEstate.analytics.assetClasses.tooltipSuffix": "of the total investment amount of all asset classes in real estate financing cases", "dashboard.realEstate.analytics.totalInvestmentAmount.ownInvestmentAmount.tooltip": "Amount", "dashboard.realEstate.analytics.totalInvestmentAmount.totalOwnInvesmentAmount.tooltip": "of the total investment amount of all property types in real estate financing cases", "dashboard.realEstate.casesTable.finance.columns.caseStatus": "Case status", "dashboard.realEstate.casesTable.finance.columns.company": "Company", "dashboard.realEstate.casesTable.finance.columns.creationDate": "Creation date", "dashboard.realEstate.casesTable.finance.columns.debtRatio": "Debt ratio", "dashboard.realEstate.casesTable.finance.columns.equityRatio": "Equity ratio", "dashboard.realEstate.casesTable.finance.columns.financingType": "Financing type", "dashboard.realEstate.casesTable.finance.columns.foreignCapital": "Foreign capital", "dashboard.realEstate.casesTable.finance.columns.ltcAtMarketValue": "LTV at market value", "dashboard.realEstate.casesTable.finance.columns.ltcAtPlannedSalesProceeds": "LTV at planned sales proceeds", "dashboard.realEstate.casesTable.finance.columns.ltcAtTotalInvestmentAmount": "LTC", "dashboard.realEstate.casesTable.finance.columns.marketValue": "Total market value", "dashboard.realEstate.casesTable.finance.columns.objectAddress": "Object address", "dashboard.realEstate.casesTable.finance.columns.ownCapital": "Own capital", "dashboard.realEstate.casesTable.finance.columns.projectName": "Project name", "dashboard.realEstate.casesTable.finance.columns.propertyDebtForAnnualIncomeActual": "Property-related debt service capacity for Annual net rental income total (actual)", "dashboard.realEstate.casesTable.finance.columns.propertyDebtForAnnualIncomeTarget": "Property-related debt service capacity for Annual net rental income total (target)", "dashboard.realEstate.casesTable.general.columns.actualAnnualNetRentalIncomeTotal": "Actual annual net rental income total", "dashboard.realEstate.casesTable.general.columns.assetClass": "Asset class", "dashboard.realEstate.casesTable.general.columns.caseStatus": "Case status", "dashboard.realEstate.casesTable.general.columns.company": "Company", "dashboard.realEstate.casesTable.general.columns.creationDate": "Creation date", "dashboard.realEstate.casesTable.general.columns.ecoreScoring": "ECORE-SCORING", "dashboard.realEstate.casesTable.general.columns.energyEfficiencyClass": "Energy efficiency class", "dashboard.realEstate.casesTable.general.columns.esgCertified": "ESG-Certified", "dashboard.realEstate.casesTable.general.columns.financingType": "Financing type", "dashboard.realEstate.casesTable.general.columns.marketValue": "Market value", "dashboard.realEstate.casesTable.general.columns.objectAddress": "Object address", "dashboard.realEstate.casesTable.general.columns.objectName": "Project name", "dashboard.realEstate.casesTable.general.columns.objectStatus": "Object status", "dashboard.realEstate.casesTable.general.columns.objectType": "Object type", "dashboard.realEstate.casesTable.general.columns.rentalUnits": "Rental units", "dashboard.realEstate.casesTable.general.columns.targetAnnualNetRentalIncomeTotal": "Target annual net rental income total", "dashboard.realEstate.casesTable.general.columns.totalGrossFloorArea": "Total gross floor area (m²)", "dashboard.realEstate.casesTable.general.columns.totalInvestmentAmount": "Total investment amount", "dashboard.realEstate.casesTable.general.columns.totalRentalSpace": "Total rental space (m²)", "dashboard.realEstate.casesTable.kpi.columns.caseStatus": "Case status", "dashboard.realEstate.casesTable.kpi.columns.costsGrossFloor": "Costs per QM gross floor area (based on total investment amount)", "dashboard.realEstate.casesTable.kpi.columns.costsRentalSpace": "Costs per QM rental space (based on the total investment amount)", "dashboard.realEstate.casesTable.kpi.columns.creationDate": "Creation date", "dashboard.realEstate.casesTable.kpi.columns.financingType": "Financing type", "dashboard.realEstate.casesTable.kpi.columns.multiplierMarketValueActual": "Multiplier of actual annual net cold rent to market value", "dashboard.realEstate.casesTable.kpi.columns.multiplierMarketValueTarget": "Multiplier of target annual net cold rent to market value", "dashboard.realEstate.casesTable.kpi.columns.multiplierPlanDisposalProceedsTarget": "Multiplier of target annual net cold rent to planned sales proceeds", "dashboard.realEstate.casesTable.kpi.columns.multiplierTotalInvestmentAmountActual": "Multiplier of actual annual net cold rent to the total investment amount", "dashboard.realEstate.casesTable.kpi.columns.multiplierTotalInvestmentAmountTarget": "Multiplier of target annual net cold rent to the total investment amount", "dashboard.realEstate.casesTable.kpi.columns.objectAddress": "Object address", "dashboard.realEstate.casesTable.kpi.columns.planProfit": "Plan project profit €", "dashboard.realEstate.casesTable.kpi.columns.planProfitPercentage": "Plan project profit %", "dashboard.realEstate.casesTable.kpi.columns.pricePerQMLand": "Price per QM Land (based on the total investment amount)", "dashboard.realEstate.casesTable.kpi.columns.projectName": "Project name", "dashboard.realEstate.casesTable.kpi.columns.rentalSpaceActual": "Actual annual net cold rent per square metre of rental space", "dashboard.realEstate.casesTable.kpi.columns.rentalSpaceFactor": "Rental space factor / space efficiency", "dashboard.realEstate.casesTable.kpi.columns.rentalSpaceTarget": "Target annual net cold rent per square metre of rental space", "dashboard.realEstate.casesTable.kpi.columns.returnAtMarketValueActual": "Return at market value based on actual annual net rental income total", "dashboard.realEstate.casesTable.kpi.columns.returnAtMarketValueTarget": "Return at market value based on target annual net rental income total", "dashboard.realEstate.casesTable.kpi.columns.returnOfInvestmentCostsActual": "Return of total investment costs based on actual annual net rental income total", "dashboard.realEstate.casesTable.kpi.columns.returnOfInvestmentCostsTarget": "Return of total investment costs based on target annual net rental income total", "dashboard.realEstate.casesTable.kpi.columns.returnOnPlanSalesProceeds": "Return on plan sales proceeds to target annual net cold rent", "dashboard.realEstate.controls.tab.finance": "Finance", "dashboard.realEstate.controls.tab.portfolio": "Portfolio", "dashboard.realEstate.finance.financingPartner.title": "Financing partners", "dashboard.realEstate.finance.financingPartnerWidgetTable.cases.header": "Cases", "dashboard.realEstate.finance.financingPartnerWidgetTable.financingPartner.header": "Name", "dashboard.realEstate.finance.financingPartnerWidgetTable.participationAmount.header": "Total investment", "dashboard.realEstate.finance.financingStructure.title": "Financing structure", "dashboard.realEstate.finance.financingStructure.tooltipPrefix": "Total Encumbance Of The Property Without Prior Charges", "dashboard.realEstate.finance.financingStructure.tooltipSuffix": "of the total investment amount in real estate financing cases", "dashboard.realEstate.finance.financingStructure.totalLoanAmount": "Foreign capital incl. prior charges", "dashboard.realEstate.finance.financingStructure.totalOwnCapital": "Total own capital", "dashboard.realEstate.finance.kpi.cases": "Cases", "dashboard.realEstate.finance.kpi.totalLTC": "Total LTC", "dashboard.realEstate.finance.kpi.totalLTV": "Total LTV", "dashboard.realEstate.finance.kpi.totalMarketValue": "Total market value", "dashboard.realEstate.portfolio.analytics.assetClasses.title": "Asset classes", "dashboard.realEstate.portfolio.analytics.energy.title": "Energy efficiency class", "dashboard.realEstate.portfolio.analytics.objectStatus.buildingPermissionGranted": "Building permission granted", "dashboard.realEstate.portfolio.analytics.objectStatus.completion": "Completion", "dashboard.realEstate.portfolio.analytics.objectStatus.fullyLetOut": "<PERSON>y let out", "dashboard.realEstate.portfolio.analytics.objectStatus.partialBuildingPermissionGranted": "Partial building permission granted", "dashboard.realEstate.portfolio.analytics.objectStatus.startOfConstruction": "Start of construction", "dashboard.realEstate.portfolio.analytics.objectStatus.startOfPlanning": "Start of planning", "dashboard.realEstate.portfolio.analytics.objectStatus.title": "Object status", "dashboard.realEstate.portfolio.analytics.subleaseClasses.commercialRentalSpace": "Commercial rental space", "dashboard.realEstate.portfolio.analytics.subleaseClasses.gastronomyRentalSpace": "Gastronomy rental space", "dashboard.realEstate.portfolio.analytics.subleaseClasses.hotelRentalSpace": "Hotel rental space", "dashboard.realEstate.portfolio.analytics.subleaseClasses.officeRentalSpace": "Office rental space", "dashboard.realEstate.portfolio.analytics.subleaseClasses.otherOperatorPropertyRentalSpace": "Other operator property rental space", "dashboard.realEstate.portfolio.analytics.subleaseClasses.otherRentalSpace": "Other rental space", "dashboard.realEstate.portfolio.analytics.subleaseClasses.residentialRentalSpace": "Residential rental space", "dashboard.realEstate.portfolio.analytics.subleaseClasses.retailRentalSpace": "Retail rental space", "dashboard.realEstate.portfolio.analytics.subleaseClasses.targetJNKMTooltip": "SOLL-JNKM", "dashboard.realEstate.portfolio.analytics.subleaseClasses.title": "Sublease classes", "dashboard.realEstate.portfolio.analytics.subleaseClasses.totalSpaceTooltip": "Total area", "dashboard.realEstate.portfolio.analytics.title": "Analytics", "dashboard.realEstate.portfolio.analytics.totalInvestmentAmount.title": "Total investment amount by object type", "dashboard.realEstate.portfolio.kpi.allRentalUnits": "All rental units", "dashboard.realEstate.portfolio.kpi.cases": "Cases", "dashboard.realEstate.portfolio.kpi.totalGrossFloorArea": "Total gross floor area (m²)", "dashboard.realEstate.portfolio.kpi.totalInvestmentAmount": "Total investment amount ", "dashboard.realEstate.portfolio.kpi.totalMarketValue": "Total market value", "dashboard.realEstate.portfolio.kpi.totalRentalSpace": "Total rental space (m²)", "dashboard.realEstate.portfolio.marketValueWidget.title": "Historical development of the total market values", "dashboard.revisions.informationLabel.applicationAccepted": "Application accepted", "dashboard.revisions.informationLabel.applicationCanceled": "Application canceled", "dashboard.revisions.informationLabel.applicationCreated": "Application form viewed", "dashboard.revisions.informationLabel.applicationDeclined": "Application declined", "dashboard.revisions.informationLabel.applicationInvitationCreated": "Invitation sent", "dashboard.revisions.informationLabel.applicationInvitationDeclined": "Invitation declined", "dashboard.revisions.informationLabel.applicationSubmitted": "Application submitted", "dashboard.revisions.informationLabel.businessCaseDuplicated": "Case copied from {$PH}", "dashboard.revisions.informationLabel.consortiumCreated": "Case created", "dashboard.revisions.informationLabel.consortiumStateChanged": "Case status changed", "dashboard.revisions.informationLabel.informationAdded": "Information added", "dashboard.revisions.informationLabel.informationCreated": "Information saved", "dashboard.revisions.informationLabel.informationDeleted": "Information deleted", "dashboard.revisions.informationLabel.informationUpdated": "Information updated", "dashboard.revisions.informationLabel.invitationAccepted": "Invitation accepted", "dashboard.revisions.informationLabel.invitationCanceled": "Invitation canceled", "dashboard.revisions.informationLabel.invitationCreated": "Participant invited", "dashboard.revisions.informationLabel.leadChange": "Case owner changed", "dashboard.revisions.informationLabel.leadChanged": "Case owner changed", "dashboard.revisions.informationLabel.ndaSigned": "NDA signed", "dashboard.revisions.informationLabel.newDocument": "Document added", "dashboard.revisions.informationLabel.participantAdded": "Participant added", "dashboard.revisions.informationLabel.participantChanged": "Conditions of participation adjusted", "dashboard.revisions.informationLabel.participantChangedUserRemoved": "Participant left case", "dashboard.revisions.informationLabel.participantRemoved": "Participant removed", "dashboard.revisions.informationLabel.userAdded": "User added", "dashboard.revisions.informationLabel.userRemoved": "User removed", "dashboard.revisions.informationLabel.userUpdated": "User updated", "dashboard.sidebar.marketplace.bankDefault": "Bank", "dashboard.table.header.3": "Volume", "dashboard.widgets.emptyState.title": "No information to display", "data-roomGroup.visibility.subTitle.inviteeApplication": "Available for invited organizations, applicants, participants and case owners.", "data-roomGroup.visibility.template": "No restriction", "data-roomGroup.visibility.title": "Private", "data-roomGroup.visibility.title.inviteeApplication": "Case interested", "dataExportModal.title": "Data export", "dataGrid.addNewRow": "Add a new row", "dataGrid.contextMenu.delete": "Delete", "dataGrid.contextMenu.delete.shortcut": "Del", "dataGrid.contextMenu.paste": "Paste", "dataGrid.contextMenu.paste.shortcut": "Ctrl + V", "dataGrid.contextMenu.paste.tooltip": "Please use the shortcut (Ctrl + V) to paste values", "dataGrid.contextMenu.pinOnBottom": "Pin on bottom", "dataGrid.contextMenu.pinOnTop": "Pin on top", "dataGrid.contextMenu.pinRow": "Pin row", "dataGrid.dataType.checkbox": "Checkbox", "dataGrid.dataType.currency": "<PERSON><PERSON><PERSON><PERSON>", "dataGrid.dataType.date": "Date", "dataGrid.dataType.decimal": "Decimal", "dataGrid.dataType.number": "Numeric", "dataGrid.dataType.numeric": "Numeric", "dataGrid.dataType.percentage": "Percent", "dataGrid.deleteColumn": "Delete a column", "dataGrid.deleteRow": "Delete a row", "dataGrid.export": "Export", "dataGrid.export.csv": "CSV", "dataGrid.export.excel": "Excel", "dataGrid.formula.errors.div": "You can not divide by 0.", "dataGrid.formula.errors.error": "Error. There's a problem with a formula.", "dataGrid.formula.errors.na": "Missing or wrong data", "dataGrid.formula.errors.name": "No such existing function.", "dataGrid.formula.errors.null": "Error in selected cells. Check path or formula.", "dataGrid.formula.errors.num": "Error by number format or calculation.", "dataGrid.formula.errors.ref": "Some of the cells you are referring to no longer exist.", "dataGrid.formula.errors.value": "Wrong argument type.Please check the formula.", "dataGrid.pinLeft": "<PERSON><PERSON> left", "dataGrid.pinRight": "Pin right", "dataGrid.pushPin": "Pin column", "dataGrid.statusBar.metrics.average": "Average", "dataGrid.statusBar.metrics.count": "Count", "dataGrid.statusBar.metrics.max": "Max", "dataGrid.statusBar.metrics.min": "Min", "dataGrid.statusBar.metrics.sum": "Sum", "dataGrid.symbolOutlined": "Adjust column size", "dataGrid.toolbar.functions.average": "AVERAGE", "dataGrid.toolbar.functions.category.commonlyUsed": "Frequently used", "dataGrid.toolbar.functions.category.date": "Date", "dataGrid.toolbar.functions.category.engineering": "Engineering", "dataGrid.toolbar.functions.category.financial": "Financial", "dataGrid.toolbar.functions.category.information": "Information", "dataGrid.toolbar.functions.category.logical": "Logical", "dataGrid.toolbar.functions.category.lookupAndReference": "Lookup Reference", "dataGrid.toolbar.functions.category.math": "Math", "dataGrid.toolbar.functions.category.statistics": "Statistics", "dataGrid.toolbar.functions.category.text": "Text", "dataGrid.toolbar.functions.ceiling": "CEILING", "dataGrid.toolbar.functions.commonlyUsed": "Commonly used", "dataGrid.toolbar.functions.concat": "CONCATENATE", "dataGrid.toolbar.functions.count": "COUNT", "dataGrid.toolbar.functions.counta": "COUNTA", "dataGrid.toolbar.functions.countblank": "COUNTBLANK", "dataGrid.toolbar.functions.floor": "FLOOR", "dataGrid.toolbar.functions.if": "IF", "dataGrid.toolbar.functions.len": "LEN", "dataGrid.toolbar.functions.lower": "LOWER", "dataGrid.toolbar.functions.now": "NOW", "dataGrid.toolbar.functions.proper": "PROPER", "dataGrid.toolbar.functions.replace": "REPLACE", "dataGrid.toolbar.functions.substitute": "SUBSTITUTE", "dataGrid.toolbar.functions.sum": "SUM", "dataGrid.toolbar.functions.today": "TODAY", "dataGrid.toolbar.functions.trim": "TRIM", "dataGrid.toolbar.functions.upper": "UPPER", "dataGrid.toolbar.horizontalAlignment.center": "center", "dataGrid.toolbar.horizontalAlignment.justify": "justify", "dataGrid.toolbar.horizontalAlignment.left": "left", "dataGrid.toolbar.horizontalAlignment.right": "right", "dataGrid.toolbar.tooltips.backgroundColor": "Background Color", "dataGrid.toolbar.tooltips.checkbox": "Checkbox", "dataGrid.toolbar.tooltips.currency": "<PERSON><PERSON><PERSON><PERSON>", "dataGrid.toolbar.tooltips.export": "Export", "dataGrid.toolbar.tooltips.functions": "Functions", "dataGrid.toolbar.tooltips.horizontalAlignment": "Horizontal Alignment", "dataGrid.toolbar.tooltips.more": "More", "dataGrid.toolbar.tooltips.percentage": "Percentage", "dataGrid.toolbar.tooltips.textColor": "Text Color", "dataGrid.toolbar.tooltips.verticalAlignment": "Vertical Alignment", "dataGrid.toolbar.tooltips.wrapText": "Text Wrap", "dataGrid.withoutPin": "Without pin", "dataRoom.accessModal.column": "Organisation ", "dataRoom.accessModal.columnR": "Role", "dataRoom.accessModal.label": "Directly Shared", "dataRoom.accessModal.label2": "Invitees, applicants, chat contacts ", "dataRoom.accessModal.label3": "Visible by default to new", "dataRoom.accessModal.text": "Control data visibility, request information for areas in your data room, and check their status. ", "dataRoom.accessModal.title": "Manage access", "dataRoom.addDataText": "Add data", "dataRoom.crawlError.label": "The Common Register Portal is not accessible right now. Downloading will continue later.", "dataRoom.customHandler.toast.success": "Document successfully updated", "dataRoom.default.defaultControlTitle.explicitlyShared": "Settings for new Direct Shared", "dataRoom.default.defaultControlTitle.interestedCustomer": "Settings for new case interested customers", "dataRoom.default.defaultControlTitle.participant": "Settings for new participants", "dataRoom.delete.input.warning": "Are you sure you want to delete this input?", "dataRoom.delete.synced.input.warning": "Deleting this will stop syncing with the Financing details, and you won't be able to restore the sync.", "dataRoom.documentCategory.label": "Select document category", "dataRoom.draggableFields.boolean": "Yes/No Field", "dataRoom.draggableFields.currency": "Money amount", "dataRoom.draggableFields.date": "Date", "dataRoom.draggableFields.document": "Document", "dataRoom.draggableFields.folder": "Folder", "dataRoom.draggableFields.integer": "Integer", "dataRoom.draggableFields.location": "Location", "dataRoom.draggableFields.month": "Month", "dataRoom.draggableFields.percent": "Percentage value", "dataRoom.draggableFields.select": "Drop-Down", "dataRoom.draggableFields.table": "Table", "dataRoom.draggableFields.tag": "Decimal", "dataRoom.draggableFields.title": "Short text", "dataRoom.draggableFields.titlePlus": "Long text", "dataRoom.onboardingTip.text.documentDragAndDrop": "You can add files that have been uploaded to the Inbox to the financing case by dragging and dropping them.", "dataRoom.onboardingTip.text.documentUpload": "You can upload files to the Inbox by dragging and dropping them.", "dataRoom.onboardingTip.text.documentsIntoGroups": "You can select files from the list and add them to groups by drag & drop.", "dataRoom.onboardingTip.title.documentDragAndDrop": "Adding files to the funding case", "dataRoom.onboardingTip.title.documentUpload": "Upload files to the Inbox", "dataRoom.onboardingTip.title.documentsIntoGroups": "Sort files", "dataRoom.participantFieldRequest.infoMessage": "By clicking Request, all selected users of this participant will be notified immediately.\r\nAt least one user must be selected.", "dataRoom.participantFieldRequest.title": "Select user", "dataRoom.selectAll": "Select All", "dataRoom.singleFile.upload": "File name  {$INTERPOLATION}", "dataRoom.tabs.mine": "Mine", "dataRoom.tabs.shared": "Shared", "dataRoom.visibilityState.cadrParticipantDescription": "Available to data room owners, participants, and Directly Shared.", "dataRoom.visibilityState.cadrParticipantDescription2": "Available for case owner", "dataRoom.visibilityState.cadrPrivateDescription": "Available for data room owner", "dataRoom.visibilityState.cadrPublicDescription": "All organizations with access to this data room can see this group.", "dataRoom.visibilityState.cardParticipantTitle": "Participants and Directly Shared", "dataRoom.visibilityState.caseDrParticipantDescription": "Available for participants and case owners", "dataRoom.visibilityState.caseDrPublicDescription": "All organizations with access to this case can see this group.", "dataRoomTemplate.information": "Add Information", "dataType.audio": "Audio", "dataType.configuration": "Configuration", "dataType.diagram": "Diagram", "dataType.document": "Document", "dataType.image": "Image", "dataType.presentation": "Presentation", "dataType.spreadsheet": "Spreadsheet", "dataType.video": "Video", "datagrid.addNewColumn": "Add a new column", "dateRangeField.label.from": "From", "dateRangeField.label.till": "<PERSON><PERSON>", "dateRangeField.label.to": "To", "dateService.timePastBefore": "ago", "dateService.timePastSince": "since", "deleteButtonLabel.areYouSure": "Are you sure you want to delete?", "detachMasterCase.dialog.label.no": "No", "detachMasterCase.dialog.label.yes": "Yes, I want.", "detachMasterCase.dialog.message": "Making this field independent will unlink it from the value provided by the organization that shared the case with you.", "detachMasterCase.dialog.text": "Do you really want to switch to edit mode? If you make a field an independent input field, separate its input value from the value linked to it in the Data Room. ", "detachMasterCase.dialog.title": "Are you sure you want to enter edit mode?", "details.label": "Details", "deviceAuthentication.authenticate": "Authenticate", "deviceAuthentication.deviceName": "Device name", "deviceAuthentication.message": "Still haven’t received the email? Try to sent a new one after {$START_TAG_STRONG}{$INTERPOLATION}{$CLOSE_TAG_STRONG}.", "deviceAuthentication.resendButtonText": "Try to send a new one.", "deviceAuthentication.resendMessage": "Didn’t receive the authentication mail? Try to send new one in {$START_TAG_SPAN}{$INTERPOLATION}{$CLOSE_TAG_SPAN}.", "deviceAuthentication.securityCode": "Security code", "deviceAuthentication.text": "Name and authenticate your device in order to continue.", "deviceAuthentication.timeOutMessage": "Didn’t receive the authentication mail?", "deviceAuthentication.timeOutMessage2": "Send a new one.", "deviceAuthentication.validationError": "Enter the name of your device", "deviceAuthentication.verification": "Send security code", "digitalSignature.dashboard.header": " Signature overview ", "digitalSignature.dashboard.heading": " No documents to sign ", "digitalSignature.dashboard.heading.signer": " Signer ", "digitalSignature.notificationModal.button.label": "Next", "digitalSignature.notificationModal.mainText": " We have sent a signature authentication code to", "digitalSignature.notificationModal.mainText2": " .", "digitalSignature.notificationModal.smallPrint": " Please use the code to verify and sign the document in the next step. ", "digitalSignature.signature.signatureAuthCodeFailed.smallPrint": " Please use the code to verify and sign the document in the next step. ", "digitalSignature.signature.signatureAuthCodeFailed.text": "We have sent a signature authentication code to", "digitalSignature.signature.signatureAuthCodeFailed.text2": "sent", "digitalSignature.signatureSessionError.info": " We are sorry! A problem occurred while signing the document. ", "digitalSignature.signatureSessionError.label.redirectToDashboard": "Back to signature overview", "digitalSignature.signatureSessionError.label.tryAgain": "Retry", "digitalSignature.signatureSessionExpired.button.label.redirectToDashboard": "Back to signature overview", "digitalSignature.signatureSessionExpired.button.label.tryAgain": "Retry", "digitalSignature.signatureSessionExpired.info": " Oops... Your session expired. ", "digitalSignature.signatureSessionSuccess.additionalInfo": "Please note that it may take a few minutes to provide the signed copy.", "digitalSignature.signatureSessionSuccess.button.label.redirectToDashboard": "Back to signature overview", "digitalSignature.signatureSessionSuccess.info": " Great! You have successfully signed the document. ", "digitalSignature.sortFields.title": "File name", "document.display.errorMessage.isDocumentMIssingError": "File is no longer available", "document.display.errorMessage.isNotSentToInboxSuccessful": "Upload again", "document.display.successMessage": "Forwarding successful", "document.invalid-type.error": "The file you uploaded was classified as potentially dangerous and removed again.", "document.pdf.type.error": "Not supported data type. Supported data types are: PDF", "documentDisplay.tooltip.text": "Forward to Inbox", "documentField.label.at": "at", "documentField.manageHelper.toast.error": "Upload problem", "documentField.message.missingDescription": "No description added", "documentField.upload.description": "or drag & drop here", "documentField.upload.download": "Download", "documentField.upload.failure": "File could not be uploaded", "documentField.upload.inProgress": "Uploading file, please wait", "documentField.upload.modalSubTitle": "Upload a new file.", "documentField.upload.modalTitle": "Upload document", "documentField.validationError.documentNameExist": "A document field with the same name already exists. Please change the name", "documentFiller.button.label": "Auto Generate", "documentFiller.button.label.inProgress": "Generating response...", "documentFiller.button.label.retry": "Generate Again", "documentFiller.error.gibberishOrLatin": "Error in generating AI suggestions. Please try with a different file.", "documentFiller.error.minimalText": "An error occurred while processing the PDF. Please try with a file with more text content.", "documentFiller.error.moralError": "Error in generating AI suggestions. Please try with a different file.", "documentFiller.error.noText": "An error occurred while processing the PDF. File needs to contain text on the first pages.", "documentFiller.instructions": "Did you know? neoshare AI can now provide you Title and Description suggestions for the text-based PDF files.", "documentFiller.label": "Generate Title and Description using neoshare AI", "documentFiller.labelMaxAttemptsReached": "neoshare AI suggestions are limited to 3.", "documentFiller.revert.button": "<PERSON><PERSON>", "documentINbox.title": "Inbox", "documentInbox.applyToDataRoom": "Apply to data room", "documentInbox.classificationDocument.originalFileName": "Original file name: {$INTERPOLATION}", "documentInbox.classificationWarning": "Duplicate document placement", "documentInbox.copyMail.message": "Email address copied to your clipboard", "documentInbox.countSelected": "{$INTERPOLATION} Selected", "documentInbox.dateOfUpload": "Date of upload", "documentInbox.delete": "Delete", "documentInbox.deleteModalTitle": "Are you sure you want to delete the selected files?", "documentInbox.download": "Download", "documentInbox.dragAndDrop": "Add files and folders via drag and drop", "documentInbox.duplicationFileConflict": "There’s a conflict with one or more selected documents. Please resolve it to proceed", "documentInbox.edit": "<PERSON><PERSON>", "documentInbox.emptyStateDescription": "or click on \"Upload\"", "documentInbox.failedZipUpload.error.prefix": "out of", "documentInbox.failedZipUpload.error.suffix": "files were not uploaded.", "documentInbox.fileName": "Name", "documentInbox.filePresentPlaceholder": "File uploaded", "documentInbox.fileWithVirus.prefix": "The file you uploaded", "documentInbox.fileWithVirus.suffix": "was classified as potentially dangerous and removed again", "documentInbox.folder": "Folder", "documentInbox.groupTitle": "Sort files by groups ", "documentInbox.inboxAddress": "Inbox address", "documentInbox.label.deleteAll": "Delete all", "documentInbox.label.uploadFiles": "Upload", "documentInbox.mailTooltip.message": "To send files to the Inbox by e-mail, please use this e-mail address.", "documentInbox.message": "Your file has been uploaded successfully.", "documentInbox.more": "More", "documentInbox.noDocument": "There are currently no documents uploaded.", "documentInbox.noFilePlaceholder": "No file", "documentInbox.onRemove.message": "Delete selected files?", "documentInbox.organization": "Organization", "documentInbox.organize": "Organize", "documentInbox.originalFileName": "Original file name", "documentInbox.palaceholder.search": "Search...", "documentInbox.placeholder": "Search records...", "documentInbox.preview": "Preview", "documentInbox.previewDataRoomStructure": "Preview Data room structure for Case ID: {$INTERPOLATION}", "documentInbox.previewDataRoomStructureDescription": "Take a sneak peek at where your files will land! Simply select, preview, and hit \"Apply\" to finalize.", "documentInbox.regenerate": "Regenerate", "documentInbox.rejectUpload.message": "The file you uploaded was classified as potentially dangerous and removed again.", "documentInbox.search": "Search...", "documentInbox.selectedDocument": "Selected", "documentInbox.selectedDocuments.files": "Files", "documentInbox.source": "Source", "documentInbox.table.fileName": "File name", "documentInbox.table.uploadedBy": "Uploaded by", "documentInbox.table.uploadedOn": "Uploaded on", "documentInbox.timeoutMessage": "AI is classifying {filename} location and naming…", "documentInbox.title": "Inbox", "documentInbox.toggles.all": "Select all", "documentInbox.toggles.nameRework": "Edit name", "documentInbox.toggles.sortGroup": "Sort groups", "documentInbox.tour.stepOne.descriptionOneText": "Let neoshare AI select the ideal spot for your files in the Data Room.", "documentInbox.tour.stepOne.descriptionThreeText": "Upload and arrange multiple documents simultaneously.", "documentInbox.tour.stepOne.descriptionTwoText": "Organize and place documents with ease and accuracy.", "documentInbox.tour.stepOne.header": "Discover the all-new Inbox", "documentInbox.tour.stepOne.nextButtonText": "Explore", "documentInbox.tour.stepOne.subHeader": "Intelligent, fast, effortless", "documentInbox.tour.stepThree.header": "Preview your uploads before sending", "documentInbox.tour.stepThree.nextButtonText": "Еnter Inbox", "documentInbox.tour.stepThree.subHeader": "Easily check what's missing or already uploaded.", "documentInbox.tour.stepTwo.header": "Upload multiple files", "documentInbox.tour.stepTwo.subHeader": "neoshare AI suggests the best name and location to keep everything neatly organised.", "documentInbox.transferDocumentsSuccess": "Selected documents saved successfully", "documentInbox.transferError": "An error occurred. Please try again", "documentInbox.transferError.dataRoomStructure": "Document processing failed due to changes in the Data room folder structure. Please try again", "documentInbox.transferError.unassignedDocument": "Unassigned documents cannot be sent to the Data room", "documentInbox.upload.error": "Upload problem", "documentInbox.uploadDate": "Upload date", "documentInbox.uploadOptions.uploadFiles": "Upload files", "documentInbox.uploadOptions.uploadFolder": "Upload a folder", "documentInbox.uploadedBy": "Uploaded by", "documentPLaceHolder.confirmLabel": "Continue", "documentPLaceholder.model.missingLabelMapping": "Missing data", "documentPLaceholder.model.missingLabelMapping.position": "Missing position", "documentPLaceholder.model.missingLabelMapping.salutation": "Missing salutation", "documentPLaceholder.model.missingLabelMapping.title": "Missing title", "documentPlaceHolder.text.message.confirmation": "If you close the signature placement editor, all your changes will be lost. Are you sure you want to continue?", "documentPlaceHolder.text.message.missingProperties": "Each signer must be assigned either a signature placeholder or a placeholder for their initials.", "documentPlaceHolder.text.message.missingProperties.confirmLabel": "Close", "documentPlaceHolder.text.title.missingProperties": "The following signers have not been assigned a signature placeholder.", "documentPlaceholder.emptyState.editFields": "Edit fields", "documentPlaceholder.emptyState.emptyInfo": "Drag a placeholder from the left into the document or select a placeholder already in use. ", "documentPlaceholder.fullName": "Full name", "documentPlaceholder.groupHeader": "Signature field", "documentPlaceholder.groupHeader.alignment": "Alignment", "documentPlaceholder.groupHeader.autofill": "Autofill field", "documentPlaceholder.groupHeader.formatting": "Formatting", "documentPlaceholder.groupHeader.scaling": "Sc<PERSON>", "documentPlaceholder.groupHeader.signerList": "Signer list", "documentPlaceholder.label.email": "E-Mail-Adresse*", "documentPlaceholder.label.initials": "Initials", "documentPlaceholder.label.name": " Name", "documentPlaceholder.label.organization": "Organisation", "documentPlaceholder.label.position": "Position", "documentPlaceholder.label.salutation": "Salutation", "documentPlaceholder.label.signature": "Signature", "documentPlaceholder.label.signed": "Signed on", "documentPlaceholder.label.title": "Title", "documentPlaceholder.modal.title": "Signature placement", "documentPlaceholder.model.company": "Organisation", "documentPlaceholder.model.signature": "Signature", "documentPlaceholder.model.title": "Title", "documentPreview.email": "Email address", "documentPreview.noPreview": "No preview available for this data type.", "documentPreview.noPreview2": "No preview available", "documentPreview.notApplicable": "N/A", "documentPreview.notRegisteredCompany": "Not registered company", "documentPreview.organisation": "Organisation:", "documentPreview.toast.info": "Document export started. Your download should be available in a few seconds.", "documentPreview.type": "Source:", "documentPreview.uploadedBy": "Uploaded by:", "downloadFilesAsZip": "Document export", "downloadFilesAsZip.fileName.case": "Archive-case-{$PH}.zip", "downloadFilesAsZip.fileName.company": "Archive-company-{$PH}.zip", "downloadFilesAsZip.toast.info": "Document export started. Your download should be available in a few seconds.", "dracoonIntegration.toast.error": "The data is not correct.", "dracoonMessage": "Please enable DRACOON or agree21doksharing integration to allow your users to synchronize with neoshare data rooms.", "drag.drop.here": "Drag-and-Drop here", "dragAndDrop": " Drag & Drop ", "dragAndDrop.subtitle": "Upload data in this window by Drag-and-Drop.", "dragAndDrop.title": "Drag-and-Drop", "draggableInboxDocument.warning": "Document already exists.", "draggableInboxDocument.warning2": "Document already exists: ", "dropDownOptions.config.addNewOption": "Add option", "duplicate-case.header": "Copy case ", "duplicate-case.tabNav.tab.administration": "Management", "duplicate-case.tabNav.tab.basicInfo": "Basic information", "duplicate-case.tabNav.tab.dataRoom": "Data room", "duplicate-case.tabNav.tab.kollaboration": "Collaboration ", "duplicateBusinessCase.financingDetailsGroups": "If you select all groups, the data will be copied to the new financing case. Otherwise, the new case will be empty.", "duplicateBusinessCase.financingDetailsGroups.title": "Financing details groups", "duplicateCase.participant.label.amount": "Overall participation", "duplicateCase.participant.label.copy": "Co<PERSON> as", "duplicateCollaboration.applicantCriteria.header": "Applicant Criteria & Participant Requirements ", "duplicateCollaboration.myPartners.header": "My Partner", "duplicateDataRoom.contentHeader.date": "Uploaded on", "duplicateDataRoom.contentHeader.fileName": "File Name", "duplicateDataRoom.contentHeader.name": "Uploaded by", "duplicateDataRoom.contentInformation.description": "Settings for new participants:", "duplicateDataRoom.contentInformation.description2": "Settings for new Case Interested", "duplicateDataRoom.contentInformation.visibility.notVisible": "no visible", "duplicateDataRoom.group.title": "Data Room groups", "duplicateDataRoom.header.description": "If not selected, the default settings for new subscribers will be applied", "duplicateDataRoom.header.title": "Maintain current group visibility for each participant that is copied ", "duplicateDataRoom.inboxDocument.header.description": "Click to select the documents you want to copy", "duplicateDataRoom.inboxDocument.header.title": "Copy Case Data Room Inbox", "duplicateDataRoom.information": "Data Room settings", "duplicateDataRoom.mainInformationMessage": " All fields of the data room are always copied to the new financing case. For selected fields, the information and attachments are also copied. ", "duplicateManagement.emptyManagement": "No information available.", "duplicateManagement.sectionTitle.FAQ": "FAQ", "duplicateManagement.sectionTitle.customerPortalUser": "Customer portal user", "duplicateManagement.sectionTitle.customerPortalUser.realEstate": "Partner portal users", "edit.customer.user.snapshot.modal.title": "Edit customer user", "edit.snapshot.title": "Edit general information", "editField.existingChatWarning.text": "There is an ongoing discussion about this information. If you delete the field, the discussion will be archived. ", "editField.label": "Title", "editField.mirroredField": "Title editing is disabled because this field is synced with Financing details\t", "editFinStructFieldErrorMsg": "", "elementCount.item": "item", "elementCount.items": "items", "elementCount.tooltipMessage": "This folder contains {$INTERPOLATION} items. {$INTERPOLATION_1}   match your current filter or search", "email.adress.optional": "Email address (optional)", "error.notFound.description": "The page cannot be found", "error.notFound.text": "Error 404", "errorMessage.companyEmailTaken": "This user already represents {$PH} Please use another email address or remove the Company Contact Person.", "errorMessage.organisationEmailTaken": "This user already represents {$PH}. Please use another email address or remove the Company Contact Person.", "excelSpreadsheetDownload.toast.error": "An error occurred, please try again.", "excelSpreadsheetDownload.toast.info": "Excel export started. Your download should be available in a few seconds.", "existingChat.dialog.message": "If you want to be added to the chat, please contact the case owner.", "existingChat.dialog.title": "There is already an existing chat for this topic.", "expandedSelect.list.toggleAll": "Select all", "facilityAmounts.facilityTotal.label": "Facility total", "facilityAmounts.stats.totalAmount": "Adjust total amount", "facilityAmounts.targetTotal.label": "Fixed total amount ", "facilityField.actionsMenu.item.editField": "Edit field", "facilityField.actionsMenu.item.revisions": "Revisions", "facilityField.actionsMenu.item.revisions:Revisionen": "Revisions", "facilityField.basicInfo.guaranteeCompanySearch": "Warranty provider", "facilityField.basicInfo.loanPurpose": "Purpose", "facilityField.basicInfo.targetCompany": "Target company", "facilityField.edit.field": "Field", "facilityField.revisionsTable.columns.hours": "Time", "faq.checkbox.text": "Notify contact person of all current applications and invitations about changes.", "faq.control.answer": "Answer", "faq.control.question": "Question", "faq.deleteFaq": "Do you want to delete this question?", "faq.toast.error.create": "Error by creating the FAQ.", "faq.toast.error.edit": "Error by update of the FAQ.", "faq.toast.success.create": "FAQ successfully created.", "faq.toast.success.edit": "FAQ successfully updated.", "faq.toastMessage.deleteMessage.error": "Error deleting FAQs", "faq.toastMessage.deleteMessage.success": "FAQ successfully deleted", "fieldINformation.tooltipText": "This input field is linked to a value from the Data Room. Clicking on the icon will unlock editing of the field. When you enter a new value, the link to the Data Room of the main case is broken.", "fieldInformation.approveDocumentLabel": "Approve document", "fieldInformation.approvePlaceholderLabel": "Approve placeholder", "fieldInformation.caseOwnerPendingMessage": "Pending approval", "fieldInformation.companyPortalAdditionalTooltip.notFieldVisibleCompanyPortal": "Not visible to users of the customer portal", "fieldInformation.companyPortalAdditionalTooltip.notFieldVisibleCompanyPortal.realEstate": "Not visible for users of the partner portal", "fieldInformation.companyPortalAdditionalTooltip1": "Information required by the customer portal user.", "fieldInformation.companyPortalAdditionalTooltip1.realEstate": "Information required by the user of the partner portal", "fieldInformation.companyPortalAdditionalTooltip111.notVisible.realEstate": "Not visible for users of the partner portal", "fieldInformation.companyPortalAdditionalTooltip111.visible": "Visible for users of the customer portal", "fieldInformation.companyPortalAdditionalTooltip111.visible.realEstate": "Visible for users of the partner portal", "fieldInformation.companyPortalAdditionalTooltip112.notVisible": "Not visible for users of the customer portal", "fieldInformation.companyPortalAdditionalTooltip2": "Information provided by the user of the customer portal", "fieldInformation.companyPortalAdditionalTooltip2.realEstate": "&#x27;Information provided by the user of the partner portal", "fieldInformation.companyPortalAdditionalTooltip3": "Visible to users of the customer portal.", "fieldInformation.companyPortalAdditionalTooltip3.realEstate": "Visible for users of the partner portal", "fieldInformation.participantPendingMessage": "Pending approval from case owner", "fieldInformation.populatedFromFieldAdditionalInfo": "This data can be re-requested if needed", "fieldInformation.populatedFromFieldLabel": "Data provided by:", "fieldInformation.rejectDocumentLabel": "Reject document", "fieldInformation.rejectPlaceholderLabel": "Reject placeholder", "fieldInformation.requestFieldLabel": "Request information", "fieldInformation.requestedFromFieldAdditionalInfo": "This data request can be withdrawn", "fieldInformation.requestedFromFieldLabel": "Information requested by:", "fieldInformation.syncedWithDataRoom": "Synced with Data room", "fieldInformation.syncedWithDataRoom:Mit Datenraum synchronisiert": "Synced with Data room", "fieldInformation.syncedWithFinancingDetails": "Synced with Financing details", "fieldInformation.syncedWithFinancingDetails:Mit Finanzierungsdetails\n        synchronisiert": "Synced with Financing details", "fieldInformation.tooltip.main": "Customer portal settings ", "fieldInformation.tooltip.main.realEstate": "Partner Portal Settings", "fieldModal.addFieldLabel": "Add field", "fieldModal.fieldRework": "Edit field", "fieldModal.revision": "Revisions", "fileUpload.success": "has been uploaded successfully.", "fileUploader.toast.error.fileAlreadySelected": "File already selected.", "fileUploader.toast.error.maxFileCount.prefix": "Maximum number of files reached.  A maximum of", "fileUploader.toast.error.maxFileCount.suffix": "can be uploaded at the same time.", "fileUploader.toast.error.unsupportedType": "The file you uploaded {$PH} was classified as potentially dangerous and removed again.", "fileUploader.toast.error.unsupportedType.prefix": "Unsupported data type. Supported data types are:", "fin-structure.remove-block.warning": "If you continue, all data for the financing building block will be permanently deleted. Are you sure you want to delete the financing building block?", "financialStructure.fields.hint.isCalculatedFromHiddenFields": "All {$START_TAG_SPAN}Asset classes{$CLOSE_TAG_SPAN} are counted, including deselected ones.", "financialStructure.table.columns.availabilityPeriod": "Availability period", "financialStructure.table.columns.facility": "Fecility", "financialStructure.table.columns.interestRate": "Interest rate", "financialStructure.table.columns.loanDuration": "Loan duration", "financing.details.hint": "Kreditbetrag, Zahlungstermin, Disagio, Gesamtzinssatz p.a., Gesamtlaufzeit bis, 1. Zinsbindung bis and Valutierung are mandatory information for data export in the core banking system.", "financingPartners.addDynamicFieldSetToFinStructureFailure": "Failed to add the financing building block", "financingPartners.addDynamicFieldSetToFinStructureSuccess": "Financing building block has been successfully added", "financingPartners.addPartner": "Add financing partner", "financingPartners.alreadyAddedPartner": "The financing partner has already been added to the financing block.", "financingPartners.buildingBlockAmount.warning": "To calculate financing partner participation, the {$START_TAG_STRONG}'Betrag'{$CLOSE_TAG_STRONG} field in the financing building block must be greater than 0.", "financingPartners.emptyMessage": "No financing partner has been added yet.", "financingPartners.financingPartner": "Financing partner", "financingPartners.financingPartner.tooltip": "Financing partners are all lending persons, companies and institutions and the lender itself. As soon as a financing partner has been added, it is removed from the selection options.", "financingPartners.forExample": "e.g. 30 %", "financingPartners.participation": "Financial participation ", "financingPartners.participationBelowThreshold": "The total financial participation is currently {$START_TAG_STRONG}below 100%.{$CLOSE_TAG_STRONG}", "financingPartners.updateFailure": "Failed to update the financing partner data", "financingPartners.updateSuccess": " Financing partner data has been successfully updated", "financingStructure.controlLabel": "Loan borrower", "firmekunden.overview.editCompany.contactPeople.list": "Show contact", "firmenkunden.companyEdit.checkbox": "Access to customer portal", "firmenkunden.companyEdit.checkbox.realEstate": "Access to the partner portal", "firmenkunden.companyEdit.nonExistingIndrustry": "Add industry", "firmenkunden.companyEdit.nonExistingLegalForm": "Add legal form", "firmenkunden.companyEdit.partner.errorMessage.invalidDate": "Invalid date", "firmenkunden.companyEdit.partner.errorMessage.unacceptableSymbol": "Invalid symbol", "firmenkunden.companyEdit.partner.errorMessage.unacceptableSymbol1": "Invalid symbol", "firmenkunden.companyEdit.partner.info.button.addContactPerson": "Add new", "firmenkunden.companyEdit.partner.info.otherRoleSelected": "Enter role", "firmenkunden.companyEdit.partner.info.removeContactPerson": "Delete", "firmenkunden.companyEdit.partner.info1": "First name*", "firmenkunden.companyEdit.partner.info2": "Last name*", "firmenkunden.companyEdit.partner.info3": "Phone number", "firmenkunden.companyEdit.partner.info4": "Email address*", "firmenkunden.companyEdit.partner.info5.dropdown": "Role", "firmenkunden.companyEdit.table.field1": "Company", "firmenkunden.companyEdit.table.field10": "Tax ID", "firmenkunden.companyEdit.table.field11": "Tax number", "firmenkunden.companyEdit.table.field12": "VAT ID", "firmenkunden.companyEdit.table.field13": "Founding date", "firmenkunden.companyEdit.table.field2": "Name of company", "firmenkunden.companyEdit.table.field3": "Legal form", "firmenkunden.companyEdit.table.field4": "Industry", "firmenkunden.companyEdit.table.field5": "Registry Court", "firmenkunden.companyEdit.table.field6": "Registry Number", "firmenkunden.companyEdit.table.field7": "Address", "firmenkunden.companyEdit.table.field8": "Registered on", "firmenkunden.companyEdit.table.field9": "Extract from", "firmenkunden.companyEdit.table.info.exportFields": "Tax ID, Tax number and Founding date are mandatory information for data export in the core banking system.", "firmenkunden.contactPeople.title": " Contact person", "firmenkunden.editCompany.title1": "Edit company", "firmenkunden.editCompany.title2": "Create company", "firmenkunden.overview.button.label": "Create company", "firmenkunden.overview.noDataMessage": "No results found", "firmenkunden.overview.placeholder": "Search company...", "firmenkunden.table.header1": "Status", "firmenkunden.table.header2": "Name of company", "firmenkunden.table.header3": "Address", "fluidTableCheckbox.emptyMessage": "No files uploaded", "folderDeleteModal.description": "All included files and subfolders will be permanently removed and cannot be recovered.", "folderDeleteModal.title": "Are you sure you want to delete this folder?", "folderStructure.actions.showInEnclosing": "Show in enclosing folder", "folderStructure.deleteFolderModal.toast.success": "Folder deleted successfully", "folderStructure.downloadFolder.toast.error": "Error while downloading the folder. Please try again", "folderStructure.downloadFolder.toast.started": "Your download should be available in a few seconds", "folderStructure.dragDropPlaceholder": "Add documents and folders via drag and drop from the left panel or upload a file", "folderStructure.dragDropPlaceholder.filterActive": "Adding is disabled while filters are active. Clear the filters to add items.", "folderStructure.dragDropPlaceholder.searchActive": "Adding is disabled while search is active. Clear the search to add items.", "folderStructure.dragDropPlaceholder.searchAndFilterActive": "Adding is disabled while search and filters are active. Clear both to add items.", "folderStructure.emptyFolder": "This folder is empty", "folderStructure.folder.actions.delete": "Delete", "folderStructure.folder.actions.move": "Move", "folderStructure.folder.actions.rename": "<PERSON><PERSON>", "folderStructure.listView.columns.dateUpdated": "Date updated", "folderStructure.listView.columns.fileSize": "File size", "folderStructure.listView.columns.name": "Name", "folderStructure.manageFolderModal.folderName.errors.doesNotEndWith": "File or folder names can't end with: {$INTERPOLATION}", "folderStructure.manageFolderModal.folderName.errors.folderNameUnique": "A folder / document with the same name already exists. Please change the name", "folderStructure.manageFolderModal.header.add": "Add folder", "folderStructure.manageFolderModal.header.rename": "Rename folder", "folderStructure.manageFolderModal.toast.createSuccess": "Folder created successfully", "folderStructure.manageFolderModal.toast.renameSuccess": "Folder successfully renamed", "folderStructure.move.toast.failure.generalMessage": "Error while moving the document or folder", "folderStructure.move.toast.success": "Folder / Document moved successfully", "folderStructure.sectionTitle": "Documents", "folderStructure.toast.failure.folderCannotBeMoved": "You cannot move a folder into itself or one of its subfolders", "folderStructure.toast.failure.folderNotExist": "Folder has already been deleted", "folderStructure.toast.failure.missingPermission": "Insufficient permissions. Please contact the Case Owner for further details", "folderStructure.toast.failure.sameLocation": "The folder/document is already in this location", "folderStructure.viewModeToggle.gridView": "Grid view", "folderStructure.viewModeToggle.listView": "List view", "footer.text": "Contact person", "footer.text.copyright": "Copyright © 2025 neoshare AG. All rights reserved.", "footer.text.imprint": "Imprint", "footer.text.privacy-policy": "Privacy policy", "fs.selectParticipant": "Select participant", "fs.subGroups": "Subgroups", "fsnapshot.details.labels.name": "Name", "global.multiple.active": "active", "global.single.active": "active", "globalTemplateTranslation": "Template", "graph.amounts.1.5mrd": "1 - 1.5 billion euros", "graph.amounts.100mil": "50-100 million euros", "graph.amounts.10mil": "5-10 million euros", "graph.amounts.1mrd": "500 million - 1 billion euros", "graph.amounts.20mil": "10-20 million euros", "graph.amounts.2mrd": "1.5 - 2 billion euros", "graph.amounts.300mil": "100-300 million euros", "graph.amounts.500mil": "300-500 million euros", "graph.amounts.50mil": "20-50 million euros", "graph.amounts.5mil": "0 - 5 million euros", "graph.amounts.over2mrd": "> 2 billion euros", "graph.graphFilters.button.label.apply": "Apply", "graph.graphFilters.button.name": "close", "graph.graphFilters.label": "Relationship", "graph.graphFilters.label.role": "Role", "graph.graphFilters.label.showSharesFilter": " Share of stock ", "graph.graphFilters.title": "Network filter", "graph.graphFilters.uiSelect.allRoles": "All roles", "graph.graphFilters.uiSelect.placeholder": "All relationships", "graph.legend.header": "Legend", "graph.legend.label": " Major shareholder (> 50%) ", "graph.legend.label2": "Minority shareholder", "graph.legend.label3": "Inactive", "graph.legend.label4": "Person", "graph.legend.label5": "Company", "graph.legend.label6": "Inactive person", "graph.legend.label7": " Inactive company ", "graph.legend.label8": "% share of stock", "graph.legend.sectionTitle": " Type of ownership ", "graph.legend.sectionTitle2": " Type of owners ", "graph.tutorialOverlay.message": " You can zoom in on the company network using the mouse or the zoom function on the right. ", "graph.uboGraph.noDataMessage": "No beneficial owner exists.", "groupModal.navLink": "Edit group", "groupPortal.actions.modal.all": "All groups", "groupPortal.actions.modal.text": "Check data visibility, request information for areas in your data room, and check their status. ", "groupPortal.modal.title": " Customer portal release ", "groupPortal.modal.title.realEstate": "Partner portal release", "groupPortal.tooltip.text": "Customer portal actions", "groupPortal.tooltip.text.realEstate": "Partner Portal Actions", "groupVisibility.card.span": "Manage access", "groupVisibility.statusInfo.inviteeAndApplicant.tooltipDescription": "Available to invited organizations, applicants, participants, and case owners.", "groupVisibility.statusInfo.inviteeAndApplicant.tooltipTitle": "Case collaborators ", "groupVisibility.statusInfo.private.tooltipDescription": "Available for case owner.", "groupVisibility.statusInfo.private.tooltipDescription.isCadr": "Available for data room owner.", "groupVisibility.statusInfo.tooltipDescription.isCadr": "Available to data room owners, participants and direct shares", "groupVisibility.statusInfo.tooltipTitle.isCadr": "Participants and Direct shares", "groupVisibilityStatusInfo.tooltipDescription": "All organisations with access to this case can see this group.", "groupVisibilityStatusInfo.tooltipDescription.isCadr": "All organisations with access to this data room can see this group.", "groupVisibilityStatusInfo.tooltipDescription.participant": "Available for participants and case owners.", "groupVisibilityStatusInfo.tooltipTitle": "No restriction", "imageCropper.adjustProfilePhoto.message.tooltip": "Drag to move", "integrationForm.backButton.label": "To Apps", "integrationForm.clientId": "Customer ID", "integrationForm.clientSecret": "Customer Secret", "integrationForm.container": "Customer instance", "integrationForm.header": "Connect Dracoon to neoshare.", "integrationGuide.clickOnTheseButtons": "Click on these buttons in the following order:", "integrationGuide.clientId": "Customer ID", "integrationGuide.clientSecret": "Client Secret", "integrationGuide.exampleText": "ex: SsyLWUseK[...]saUCXZBiznGgZL", "integrationGuide.field": "Open Dracoon in a new tab.", "integrationGuide.field.copyDracoon": "Copy the following link and paste it into the redirect URL input field in Dracoon:", "integrationGuide.field.copyNeoshare": "Copy and paste the following values into the input fields on neoshare:", "integrationGuide.guideStep.label": "Settings", "integrationGuide.guideStep.label.apps": "Apps", "integrationGuide.guideStep.label.apps.custom": "Custom apps", "integrationGuide.header": "This way you connect your neoshare account to Dr<PERSON>on.", "integrationGuide.instance": "Customer instance", "integrationLoading.button.label": "Understood", "integrationLoading.text": "The application is currently added to your neoshare account.", "integrationLoading.text.basicFlow": "Please note that any change to the uploaded documents in Dracoon will corrupt the connection and you will probably lose it. Each new synchronisation will delete your previous uploads. Synchronisation of documents can take up to 10-15 minutes. ", "interactiveList.placeHolder": "Add condition", "internalBilateral.modal.placeholder": "Enter or select user", "internalChat.title": "Create internal chat", "invalid.email.error": "Invalid email address", "invalid.email.wrongFormat": "Invalid email address", "invalid.username.error": "Invalid email address", "invitation.accepted.status": "Invitation accepted", "invitation.canceled.status": "Invitation canceled", "invitation.declined.status": "Invitation rejected", "invitation.expired.status": "Invitation expired", "invitation.not.available.error.message": "The invitation is not available anymore", "invitation.pending.status": "Invitation sent", "invitation.receive.status": "Invitation received", "invitationOrApplication.submitted.status": "In progress", "invitations.sortFields.creationDate": "Date created", "invitations.sortFields.customerName": "Customer name", "invite.person.message": "If you want to redirect the acceptance or rejection of invitations to participate in a project, you can invite a person from your organization who can manage the project.", "invite.person.passing.on.case": "You can redirect the invitation to take over the project to another person from your organisation. This person can accept or reject the invitation and take over the project management", "invite.to.apply.hint.message": "You have been invited to participate in this funding case. If you would like to participate, click \"Submit application\".", "invite.to.apply.next.button.label": "Proceed", "inviteToNeoSHare": "Invite to neoshare", "invited.users.label": "INVITED USERS", "invited.users.without.permissions.error": "The invited users do not have permission to view and participate in cases", "invoiceTable.confirmLabel": "Cancel invoice", "invoiceTable.message": "Please note that the operation is not reversible.", "invoiceTable.noDataMessage": "No invoices available", "invoiceTable.title": "Cancel invoice", "key1": "EN word 1", "key4": "english en 4", "keyInformationExtraction.autofill.popover.description": "{$INTERPOLATION} fields ready to autofill", "keyInformationExtraction.autofill.popover.dismiss": "<PERSON><PERSON><PERSON>", "keyInformationExtraction.autofill.popover.title": "Autofill", "keyInformationExtraction.autofill.popover.use": "Use", "keyInformationExtraction.infoMessage.dismiss": "<PERSON><PERSON><PERSON>", "keyInformationExtraction.infoMessage.label": "Accept or reject suggestions for each field and view their sources", "keyInformationExtraction.source": "Source:", "keyInformationExtraction.source.notAvailable": "Not available", "kpis.button.label.disable": "Disable", "kpis.button.label.enable": "Enable", "kpis.confirmationDialog.disable.additionalInfo": "Inactive KPI's can be found in the \"Inactive\" tab for reactivation", "kpis.confirmationDialog.disable.successToast": "<PERSON><PERSON> was disabled successfully", "kpis.confirmationDialog.disable.title": "Are you sure you want to disable this KPI?", "kpis.confirmationDialog.enable.additionalInfo": "Enabled KPI’s can be found in the \"Active\" tab", "kpis.confirmationDialog.enable.successToast": "KPI was enabled successfully", "kpis.confirmationDialog.enable.title": "Are you sure you want to enable this KPI?", "kpis.confirmationDialog.message": "A notification email will be sent to all platform users of your organization.", "label.data": "Data", "label.group": "Group", "label.inProgress": "In progress", "label.item": "{$INTERPOLATION} item", "label.items": "{$INTERPOLATION} items", "label.placeholder": "Placeholder", "label.selectGroup": "Select group", "label.userType": "User type", "labels.appraiser": "Appraiser", "labels.collaborator": "Collaborator", "labels.comment": "Comment", "labels.description": "Description", "labels.invitation-details": "Invitation Details", "labels.leader": "Leader", "labels.participant": "Participant", "labels.participation-type": "Participation type", "labels.structurer": "Structurer", "labels.theme": "Theme", "labels.user": "User", "layout.mainLayout.createBusinessCaseTxt": "Create case", "layout.mainLayout.icon.add": "add", "layout.mainLayout.tooltip.text": "Create business case", "linkedCases.table.config.linked": "Linked", "locationField.placeholder": "Add address", "locationPreview.noAddress": "No address provided", "locationPreview.showOnMap": "Show on map", "login-title": "Log in", "login.currentPasswordIncorrect": "Wrong password!", "login.currentPasswordIsIncorrect": "The password cannot be changed as the old one is incorrect.", "login.duplicateDeviceName": "Change your device name", "login.entityCard.change": "Change", "login.entityCard.device": "<PERSON><PERSON>", "login.entityCard.hello": "Hello", "login.errorMessage.expired.confirm.device.link": "Your authentication link has expired. Please request a new one.", "login.errorMessage.resetPasswordFromDifferentDevice": "You are trying to reset password from another device. Return to the original device or start again.", "login.errorMessage.wrongAuthenticationDevice": "You are trying to authenticate another device. Return to the original device or start again.", "login.errorMessage.wrongDevice": "You are trying to authenticate another device. Return to the original device or start again.", "login.footer.text": "Powerful encryption protection", "login.footer.text.migrated.users": "To continue providing top-level protection, we have updated our security measures. For detailed information on these updates, please visit our", "login.generalError": "An error occurred, please try again.", "login.invalid.email.address": "Invalid email address", "login.invalidDeviceAuth": "Your browser/device is not authenticated. Please copy and paste the link into a browser/device where you are authenticated.", "login.mfaCodeExpired": "Security code expired.  You have {$INTERPOLATION} attempts left.", "login.mfaCodeExpiredSingular": "Security code expired. You have 1 attempt left.", "login.mfaCodeRequested": "Your security code was sent! Please check your email.", "login.passwordCannotBeSameAsOld": "The new password must differ from the previous one.", "login.passwordConfirmBtn": "Confirm", "login.registerNewDeviceSuccess": "If an account with this email address exists, you will receive an authentication email for your device.", "login.requestResetPasswordSuccess": "You should receive an email with further instructions shortly.", "login.reset-password.current-password": "Current password", "login.reset-password.new-password": "New password", "login.reset-password.one-lower-case": "A lowercase character", "login.reset-password.one-number": "A number", "login.reset-password.one-special-symbol": "A special character", "login.reset-password.one-upper-case": "An uppercase character", "login.reset-password.repeat-password": "Confirm password", "login.reset-password.requirements-not-met": "Password does not meet security requirements", "login.reset-password.rules.title": "The password must contain the following:", "login.reset-password.title": "Reset password", "login.reset-password.twelve-letters": "Minimum of 12 characters", "login.reset-password.unauthorize-devices-modal": "After updating your password, please log out immediately to secure your account with the new credentials.", "login.reset.password.logout.all.devices": "Log out all devices", "login.reset.password.logout.device": "Log out device", "login.resetPassword.passwordsDoNotMatch": "Password doesn’t match.", "login.resetPasswordFailure": "The password is too weak or used regularly. Please use a stronger password.", "login.resetPasswordSuccess": "Password reset successfully", "login.selectOrganizationLabel": "Select organisation", "login.termOfUse.privacy-policy": "The legal basis for data processing is your consent in accordance with  Art. 6 para. 1 cl. 1 lit. a GDPR. You can find further information on data processing in the {$START_LINK}Privacy policy{$CLOSE_LINK}. ", "login.termOfUse.title": "Declaration of Consent", "login.unlockAccountSuccess": "Your account has been successfully unlocked!", "login.user.validation.forgot.password": "Forgot your password?", "login.user.validation.password": "Password", "manageGroup.addGroup": "Add group", "manageGroup.label": "Group name", "manageGroup.label.footer": "Group", "manageLinkedCases.description": "You cannot manage this funding case because you are not registered as a user in it.", "manageLinkedCases.title": "Linked cases", "manageLinkedCases.visibilityChange.tooltip.unmanaged": "Not managed", "markeplace.filter.switch1": "Financing volume", "markeplace.filter.switch2": "Term", "markeplace.filter.switch5": "Interest earnings", "markeplace.filter.switch7": "Financing volume", "marketplace.additionalFilter.label": "Exclude organisations", "marketplace.advanced.filters2": "Investment location", "marketplace.businessCase.card.button": "To the case", "marketplace.businessCase.card1": "Financing volume", "marketplace.businessCase.hover": "Other company information", "marketplace.filter.switch3": "Surrounding area search", "marketplace.filter.switch4": "Interest earnings", "marketplace.filter.switch6": "Term", "marketplace.filters.1": "Additional filters", "marketplace.filters.2": "Results", "marketplace.filters.4": "Distance", "marketplace.filters.advanced": "Adjust", "marketplace.filters.advanced.filters1": "Search filter", "marketplace.filters.sortFields.createdOn": "Created on", "marketplace.filters.sortFields.distanceCustomerToCustomer": "Distance (customer)", "marketplace.filters.sortFields.distanceCustomerToInvestLocation": "Distance (investment location)", "marketplace.filters.sortFields.financingVolume": "Financing volume", "marketplace.overview": "Results", "marketplace.overview.businessCase.breakdown1": "Case owner", "marketplace.overview.businessCase.breakdown2": "Partner", "marketplace.overview.businessCase.breakdown3": " still open ", "marketplace.overview.businessCase.breakdown4": " already placed ", "marketplace.statistics.completedCases": " Completed cases ", "marketplace.statistics.ongoingCases": "Current cases", "marketplace.statistics.totalParticipation": " Total investment ", "messageNotRed": "Not read", "mfaAuthentication.timeOutMessage": "Still haven't received the security code?", "mfaAuthentication.timeOutMessage2": "Send security code", "mfaAuthentication.timerMessage": "Didn’t receive the security code? Try to send a new one in {$START_TAG_STRONG}{$INTERPOLATION}{$CLOSE_TAG_STRONG}.", "months": "  Months", "multiFactorAuth.toast.error": "Multi-factor authentication could not be activated.", "multiFactorAuth.toast.error.inactive": "Multi-factor authentication could not be disabled.", "multiFactorAuth.toast.success": "Multi-factor authentication active", "multiFactorAuth.toast.success.inactive": "Multi-factor authentication not active.", "multiselectDropdown.filter.placeholder": "Filter by status", "multiselectDropdown.switchLabel": "Applicant filter", "navigationPanel.collapse.btn.tooltip": "Collapse", "navigationPanel.expand.btn.tooltip": "Expand", "nda.label": "Confidentiality agreement", "neoGpt.onboarding.header.financing.step1": "Welcome to neoshare AI", "neoGpt.onboarding.header.step1": "Welcome to neoshare AI!", "neoGpt.onboarding.header.step2": "Understand each document", "neoGpt.onboarding.header.step3": "Receive answers to all of your questions", "neoGpt.onboarding.step1": "Our innovative AI Assistant is ready to give you seamless access to all the information you need - quickly and easily. ", "neoGpt.onboarding.step2": "neoshare AI simplifies searching through long documentation and allows you to capture important information in seconds.", "neoGpt.onboarding.step3": "neoshare AI provides you with relevant and accurate answers to help you successfully manage your documentation.", "neoGpt.thankYou.forRating.msg": "Thank you for your response! We will try to improve our service based on your feedback! ", "neoGptChat.negative.feedback.placeholder": "Please share your experience or thoughts with us.", "neogpt.button.label": "Ask neoshare AI", "neogpt.chat.errorMessage": "There seem to be issues with answering the question. We apologize for the inconvenience. Please try again later.", "neogpt.chat.input.placeholder": "Message neoshare AI", "neogpt.chat.message.check": "Source", "neogpt.chat.message.checkMultiple": "Sources", "neogpt.chat.message.document.unavailable": "Document unavailable", "neogpt.chat.message.documentLimitedReady": "Processing images. This may take a while.", "neogpt.chat.message.documentPendingMsg": "The document is being processed, please wait.", "neogpt.chat.message.documentReadyMsg": "Document successfully processed.", "neogpt.chat.message.documents.label": "Documents:", "neogpt.chat.message.field.unavailable": "Field unavailable", "neogpt.chat.message.fields.label": "Fields:", "neogpt.chat.message.fields.updated": "Field updated", "neogpt.chat.message.initial": "How can I help you today?", "neogpt.chat.message.noDataAnswer": "We couldn't find a suitable answer. You can try rephrasing your question, double-checking keywords, or looking for possible typos. If you still need assistance, feel free to ask another question, and we'll be here to help.", "neogpt.chat.message.page": "Page", "neogpt.chat.message.sources.showLess": "Show less", "neogpt.chat.message.sources.showMore": "Show more", "neogpt.chat.message.summary": "Brief summary", "neogpt.chat.message.switchContext": "We could not find an answer to your request in this document.", "neogpt.chat.message.switchContextToDataRoom": "We could not find an answer to your request in this document. Shall we expand the search?", "neogpt.chat.pending.message": "The document is being processed, please wait.", "neogpt.chat.selectQuestion": "Pick a sample question from the list below, or enter your own.\r\n", "neogpt.feature.autofill": "Autofill fields", "neogpt.feature.chat": "Chat assistant", "neoshare.AI.disable.chat.history": " Disabling it will delete all previous messages", "neoshare.AI.onboarding.financing.step1": "Our innovative AI Assistant provides seamless access to the specific information you need about your financing structure. Please note that the assistant exclusively assists you with your financing structure.", "neoshare.AI.onboarding.header.step2": "Ask about any document or field", "neoshare.AI.onboarding.settings": "Adjust your preferences", "neoshare.AI.onboarding.step1": "Our assistant provides quick access to key information. Note: the assistant may make mistakes. Please review important details.", "neoshare.AI.onboarding.step2": "Using the AI assistant you can search through all fields and supported documents from your data room. The currently supported file formats are PDF, TXT, DOCX, JPG/JPEG and many more.", "neoshare.AI.onboarding.step3": "Manage your chat history and see supported formats.", "neoshare.AI.onboarding.text.step2": "Search all fields and documents in your data room and financing structure.", "neoshareAI.currently.supported.files.message": "The currently supported file formats are:", "neoshareAI.deleteHistoryDialog.text": "Permanently delete chat history:<br />This action cannot be reversed. Are you sure?", "neoshareAI.limited.capabilities.message": "The assistant occasionally provides inaccurate results for documents or fields.", "neoshareAI.settings.deleteChatHistory": "Delete chat history", "neoshareAI.settings.saveChatHistory": "Save chat history", "neoshareAI.supportedTypes.showLess": "Show less", "neoshareAI.supportedTypes.showMore": "Show more", "network.button.label.Ubo": "UBO", "network.button.label.network": "Network", "networkGraph.noDataMessage": "No network information available.", "newDocumentUpload": "Upload new document here", "nextFolderSynchronization.modal.buttonLabel": "Go to Data room", "nextFolderSynchronization.modal.select.placeholder": "None selected", "nextFolderSynchronization.modal.subTitle": "You can monitor the state of each document in the the Data room", "nextFolderSynchronization.modal.title": "Synchronization in progress", "nextfolder.message.error": "Not synced", "nextfolder.message.pending": "Synchronization pending", "nextfolder.message.success": "Synced", "nextfolderIntegration.caseSync.integration.description": "You have successfully synchronized your financing case with NextFolder!", "nextfolderIntegration.caseSync.success.button": "Go to financing case", "nextfolderIntegration.description": "You have successfully integrated NextFolder with your neoshare account!", "nextfolderIntegration.success.button": "To Apps", "nextfolderIntegration.toast.credentials.error": "This user data is already used by another organization.", "nextfolderIntegration.toast.error": "Connection not established. Try again or start the process later.", "nextfolderIntegrationForm.backButton.label": "To Apps", "nextfolderIntegrationForm.businessCase.label": "Select process name (optional)", "nextfolderIntegrationForm.description": "Do you want to stop integrating NextFolder into your Data Room?", "nextfolderIntegrationForm.emailAddress": "Email address", "nextfolderIntegrationForm.header": "Connect NextFolder to neoshare", "nextfolderIntegrationForm.infoText": "By clicking \"Connect\", you allow neoshare to use NextFolder as a cloud provider for all your uploaded documents. ", "nextfolderIntegrationForm.nextfolder.label": "NextFolder template\r\n", "nextfolderIntegrationForm.password": "Password", "nextfolderIntegrationGuide.description": "Supported file formats: PNG, JPG, PDF up to 100MB. For an ideal workflow, process PDF data in A4 format.", "nextfolderIntegrationGuide.header": "Important things you need to know before you do:", "nextfolderIntegrationGuide.message": "To establish a successful connection between your Data Room and NextFolder, you need to wait about 15 minutes after linking.", "nextfolderIntegrationGuide.title": "Upload file", "nextfolderMessage": "Nextfolder is a browser-based application that helps you process incoming documents faster and paperless from receipt to the archive system.", "nextfolderSynchronizationForm.button.synchronization": "Synchronize", "nextfolderSynchronizationForm.card.button": "To the case", "nextfolderSynchronizationForm.description": "Are you sure you want to disable the synchronization between your funding case and NextFolder?", "nextfolderSynchronizationForm.footer.description": "By clicking \"Synchronize\", you enable the connection between your funding case and a transaction at NextFolder.", "nextfolderSynchronizationForm.title": "Synchronize your finance case with NextFolder.", "ngb.alert.close": "Close", "ngb.carousel.next": "Next", "ngb.carousel.previous": "Previous", "ngb.carousel.slide-number": " Slide {$INTERPOLATION} of {$INTERPOLATION_1} ", "ngb.datepicker.next-month": "Next month", "ngb.datepicker.previous-month": "Previous month", "ngb.datepicker.select-month": "Select month", "ngb.datepicker.select-year": "Select year", "ngb.pagination.first": "««", "ngb.pagination.first-aria": "First", "ngb.pagination.last": "»»", "ngb.pagination.last-aria": "Last", "ngb.pagination.next": "»", "ngb.pagination.next-aria": "Next", "ngb.pagination.previous": "«", "ngb.pagination.previous-aria": "Previous", "ngb.progressbar.value": "{$INTERPOLATION}", "ngb.timepicker.AM": "{$INTERPOLATION}", "ngb.timepicker.HH": "HH", "ngb.timepicker.MM": "MM", "ngb.timepicker.PM": "{$INTERPOLATION}", "ngb.timepicker.SS": "SS", "ngb.timepicker.decrement-hours": "Decrement hours", "ngb.timepicker.decrement-minutes": "Decrement minutes", "ngb.timepicker.decrement-seconds": "Decrement seconds", "ngb.timepicker.hours": "Hours", "ngb.timepicker.increment-hours": "Increment hours", "ngb.timepicker.increment-minutes": "Increment minutes", "ngb.timepicker.increment-seconds": "Increment seconds", "ngb.timepicker.minutes": "Minutes", "ngb.timepicker.seconds": "Seconds", "ngb.toast.close-aria": "Close", "no.added.signers.error": "You must add at least one signer per organization before sending the invitation", "no.existing.email.error": "There is no user with this email address.", "no.invited.organization.message": "You have not yet invited any organisation", "noMatched.results.message": "There are no results that match your search criteria", "noUserMessage": "You have not listed any specific users from this organization.", "noUserMessageOld": "You do not have any organization users", "none.label": "None", "notificationSystem.header.actions.markAllAsRead": "Mark all as read", "notificationSystem.header.actions.settings": "Settings", "notificationSystem.header.filters.all": "All", "notificationSystem.header.filters.unread": "Unread", "notificationSystem.header.title": "Notifications", "notificationSystem.notification.actions.hide": "Remove", "notificationSystem.notification.actions.markAsRead": "<PERSON> as read", "notificationSystem.notification.actions.markAsUnread": "<PERSON> as unread", "notificationSystem.notification.chatRelated.archivedAutomatically": "A chat was automatically archived in the financing case with the ID {$PH}.", "notificationSystem.notification.chatRelated.archivedManually": "{$PH} has archived a chat in the financing case with the ID {$PH_1}.", "notificationSystem.notification.chatRelated.createdAutomatically": "A chat was automatically created in the financing case with the ID {$PH}.", "notificationSystem.notification.chatRelated.messageSent": "{$PH} has sent a new message in the financing case with the ID {$PH_1}.", "notificationSystem.notification.chatRelated.reactivatedAutomatically": "A chat was automatically reactivated in the financing case with the ID {$PH}.", "notificationSystem.notification.chatRelated.reactivatedByUser": "{$PH} has reactivated a chat in the financing case with the ID {$PH_1}.", "notificationSystem.notification.chatRelated.userTagged": "{$PH} has tagged you in a chat in the financing case with the ID {$PH_1}.", "notificationSystem.notification.elapsedTime.days": " {$ICU} ", "notificationSystem.notification.elapsedTime.hours": " {$ICU} ", "notificationSystem.notification.elapsedTime.minutes": " {$ICU} ", "notificationSystem.notification.elapsedTime.weeks": " {$ICU} ", "notificationSystem.notification.manualTodo.todoCanceled": "A task for {$PH} has been canceled by {$PH_1}", "notificationSystem.notification.manualTodo.todoReassigned": "A task for  {$PH} has been reassigned by  {$PH_1}. No further action is needed for this task.", "notificationSystem.notification.noPermissionsToEnterCase": "You no longer have access to this case", "notificationSystem.notification.userAssignment.expiryAfterOne": "The To-Do item associated with {$PH} is due today. Please ensure it's addressed.", "notificationSystem.notification.userAssignment.expiryAfterThree": "The To-Do item associated with {$PH} is due by {$PH_1}.", "notificationSystem.notification.userAssignment.expiryBeforeFive": "The To-Do item associated with {$PH} was due on {$PH_1}.", "notificationSystem.notification.userAssignment.reassigned": "A task for {$PH} has been reassigned by {$PH_1}. No further action is needed for this task.", "notificationSystem.notificationList.allTab.empty": "There are currently no new notifications.", "notificationSystem.notificationList.unreadTab.empty": "There are currently no unread notifications.", "notificationSystem.notificationList.upToDate": "You are up to date.", "notificationSystem.onboardingTip.image.customizations": "./assets/images/notification-system/tips-notification-system-customizations-en.svg", "notificationSystem.onboardingTip.text.customizations": "You decide what is important to you and can customize your notification settings at any time.", "notificationSystem.onboardingTip.text.intro": "Check all activity since your last visit via our notification system.", "notificationSystem.onboardingTip.title.customizations": "Manage your notifications", "notificationSystem.onboardingTip.title.intro": "Do not miss any information", "notificationSystem.settings.chat": "Cha<PERSON>", "notificationSystem.settings.title": "Settings", "notificationSystem.settings.todos": "To do list", "number.abbreviation.bil": "B", "number.abbreviation.mil": "M", "number.abbreviation.tsd": "K", "officers.heading1": "Contact person", "organizationField.prefix": "Add organization", "otherBusinessCases.noDataAvailable.message": "There are currently no funding cases on this company that you can access.", "ownCompany.dataRoom.addGroup": "Insert group", "ownCompany.dataRoom.button.openLInkedCases": "Linked cases", "ownCompany.dataRoom.empty": "No fields have been added to the corporate Data Room yet. To create some, please activate the edit mode and add fields or create a template for the enterprise data room under \"Templates\". ", "ownCompany.dataRoom.emptyGroup.text": "Add fields by drag and drop ", "ownCompany.dataRoom.emptyGroup.textSub": "To do this, drag the fields from the right column into this area.", "ownCompany.dataRoom.prefix": "Information about", "ownCompany.dataRoom.suffix": "Insert", "paginator.compactRangeLabel": "{$PH} of {$PH_1} data entries", "paginator.goToPageLabel": "Go to page:", "paginator.rangeLabel": "Showing {$PH} - {$PH_1} of {$PH_2} entries", "paginator.showLabel": "Show:", "participation-page.errors.dfs-value-exceeding-total-expected-amount": "Please note that the sum of the financing building blocks is higher than the requested amount of funding.", "participation-page.labels.amount-distribution": "Amount distribution", "participation-page.labels.building-blocks-distribution-text": "Below you can see the distribution of your own investment amount across the financing building blocks included in the financing structure.", "participation-page.labels.my-financing-structure": "Own Financing structure", "participation-page.labels.own-investment-amount": "Own investment amount", "participation-page.labels.shared-financing-structures": "Shared Structures", "participation.warning.message.notCoveredCriteria": "is not met", "participation.warning.message.participant.whoAre": "participant's", "participation.warning.message.youHave": "You have", "participationAmount.toast.error": "Error while updating the participant requirements.", "participationAmount.toast.success": "Participant requirements successfully updated.", "passwordPolice.toast.error": "Password policies could not be saved.", "passwordPolice.toast.success": "Password policies successfully saved.", "pastInvoice.dateInfo": "From / Until", "pastInvoice.placeholder": "Search Invoice...", "pdfExport.toast.info": "PDF export started. Your download should be available in a few seconds.", "perceptions.financingApplicationSubmitted": "Financing application has been submitted", "perceptions.financingApplicationWithdrawn": "Financing application was withdrawn", "perceptions.financingApproved": "Financing approved", "perceptions.financingCaseCreated": "Financing case created", "perceptions.financingCaseJoined": "Financing case joined", "perceptions.financingDecisionPending": "Financing decision pending", "perceptions.financingRejected": "Financing was rejected", "perceptions.financingRepaid": "Financing was repaid", "perceptions.financingRepaidActive": "Financing was repaid", "perceptions.financingRepaidInactive": "Financing was repaid", "perceptions.financingSuccessfullyCompleted": "Financing successfully completed", "perceptions.fullEvaluationCarriedOut": "Full evaluation carried out", "perceptions.incorrectEntry": "Incorrect entry", "perceptions.objectSold": "Object was sold", "perceptions.other": "Other", "perceptions.structuringOrderIssued": "Structuring order issued", "perceptions.termSheetAccepted": "Term sheet accepted", "portalFields.actions.switch.label": "Visible", "privateBusinessCase.toast.error": "You have no access to the Financing case anymore, because it is not active. Entry is not possible.", "privateBusinessCase.toast.failedToLoad": "The URL you provided is not correct.", "privateBusinessCase.toast.genericError": "You no longer have access to this case", "privateBusinessCase.toast.ndaNotSignedByAllParties": "All signers from your organization must sign the NDA in order for you to access the case.", "realEstate.dashboard.exportExcel.finance.kpi.filename.suffix": "Finance - KPI", "realEstate.dashboard.exportExcel.portfolio.general.filename.suffix": "Portfolio - General", "realEstate.dashboard.exportExcel.portfolio.kpi.filename.suffix": "Portfolio - KPI", "refs.accordion.infoBadge.tooltip": "Synced with Data room", "refs.addFinancingBlockModal.alreadyUsedPurposeOfUse": "This purpose is already in use", "refs.addFinancingBlockModal.title": "Add building block", "refs.financingBlockModal.financingBuildingBlock": "Financing building block", "refs.financingBlockModal.reason": "Enter a reason for creating the financing building block", "refs.financingBlockModal.selectFromList": "Please select a financing building block from the List", "refs.stats.requestedFundingAmount": "Requested funding amount", "refs.stats.sumOfFinancingBuildingBlocks": "Sum of financing building blocks", "refs.stats.tooltip.buildingBlocks": "Financing building blocks", "resend.label": "Re-send", "restrictedAccess.text": "Participants in the case from your organisation are:", "restrictedAccess.title": "Restricted access to case {$INTERPOLATION}.", "revisions.hours.oClock": "O'Clock", "search": "Search", "search.autocomplete.noResults": "No result found. Try different keywords or check the spelling", "search.financingStructure.typeahead.hint": "Enter 3 letters, 1 digit, or 1 special character to begin search.", "search.resultCount": "{$INTERPOLATION} Results", "search.resultMessage": "result", "search.resultsMessage": "results", "searchFilter.placeholder": "Search...", "sectionApplication.exceedFinancialVolumeText": "The amount entered exceeds the requested financing amount", "sectionApplication.header.label.financingCase": "Finance structure", "sectionApplication.header.label.financingCase.participationData": "Edit participation amount", "sectionApplication.header.label.passingCase": "Application", "sectionApplication.header.label.total": "Total participation", "sectionApplication.outOfRangeContribution": "The amount you entered is higher than the maximum investment amount.", "sectionApplication.rangeErrorText": "The amount you entered is lower than the minimum investment amount.", "sectionApplication.submitApplication": "Submit application", "sectionApplication.submitted.text": "Your application was successfully sent and is now reviewed by the case owner.", "sectionInvitation.label": "Invitation", "select.initialPlaceHolder": "None selected", "select.noSelected": "None selected", "select.notFoundText": "No results found ", "select.notfoundText": "No results found", "select.status.label": "Select status", "select.typeToSearchText": "Enter text to search", "selectExternal.title": "End sharing", "selectPartnerUsers.partnerUsersFieldRequest.infoMessage": " As soon as you click Request, the selected users of this participant will be notified. ", "selectPeriod.customRange": "Custom range", "selectPeriod.last30Days": "Last 30 days", "selectPeriod.last3Months": "Last 3 months", "selectPeriod.last7Days": "Last 7 days", "selectPeriod.sinceYesterday": "Since yesterday", "selectUsers.contentHeader.fileName": "Name", "selectUsers.contentHeader.landlineNumber": "Telephone", "selectUsers.contentHeader.mobile": "Mobile", "selectUsers.contentHeader.name": "E-mail", "serModel.title.Ing.": "Ing.", "shareCadr.modal.CustomerExplicitShare.title": "Directly shared", "shareCard.modal.title": "Share Data Room", "shareCardModal.button.label": "Share", "shareCardModal.errorMessage": "The company-related Data Room has already been shared with this organisation.", "shareCardModal.title.sharedVia": "Shared via Finance Case", "shareCardNodal.title": "Choose organisations", "sharedCompany.data.rooms.button.label.removed": "Remove", "sharedCompany.data.rooms.placeholder": "Select a Data Room shared with me", "sharedCompany.data.rooms.text": " No Data Room has been shared with you yet. As soon as another organization shares its Data Room related to this company with you, it will be displayed here. ", "sharedCompany.dataRooms.direct": "Direct", "sharedCompany.dataRooms.viaCase": "via Case", "sharingModal.information": "Control visibility, request information, and monitor status updates for specific areas within your financing structure.", "sideNav.button.add": "Add new group", "sidebar.caseRelated": "Case related", "sidebar.companyRelated": "Company related", "sidebar.noTemplates": "No templates yet.", "sidenav.companiesPage": "Companies", "sidenav.companiesPage2": "Corporate clients", "sidenav.items.accountManagement.customers": "Customers", "sidenav.items.accountManagement.usageContracts": "User agreements", "sidenav.items.accountManagement.users": "User", "sidenav.items.apps": "Apps", "sidenav.items.businessCaseTemplateManagement": "Templates", "sidenav.items.cases": "Cases", "sidenav.items.customerMasterData": "Master data", "sidenav.items.customerOrganisationData": "Organisation data", "sidenav.items.dashboard": "Dashboard", "sidenav.items.demos": "Snapshots", "sidenav.items.documents": "Contracts", "sidenav.items.receipt": "Billing", "sidenav.items.signature": "Signature overview", "sidenav.items.userManagement": "User management", "singleFileUpload.button.choose": "Upload file", "singleFileUpload.button.delete": "Delete file", "singleFileUpload.button.upload": "Drag-and-Drop", "singleFileUpload.heading": "File", "singleSelectInput.placeholder": "Select organisations", "snapshot.actions.copy": "Copy", "snapshot.actions.copy.modalError": "Choose an option to continue", "snapshot.actions.copy.modalHeading": "Select the state of the deployed snapshot to copy", "snapshot.actions.copy.modalOptions.modifiedSnapshot.description": "Includes all the information added to the environment after the snapshot was created.", "snapshot.actions.copy.modalOptions.modifiedSnapshot.title": "Modified snapshot", "snapshot.actions.copy.modalOptions.unmodifiedSnapshot.description": "Includes all the information at the time the snapshot was created.", "snapshot.actions.copy.modalOptions.unmodifiedSnapshot.title": "Unmodified snapshot", "snapshot.actions.delete": "Delete", "snapshot.actions.delete.modalHeading": "Deleting permanently snapshot \"{$INTERPOLATION}\" can interrupt active client demos. Are you sure?", "snapshot.actions.deploy": "Deploy", "snapshot.actions.deploy.errorMessage": "Error while deploying snapshot", "snapshot.actions.deploy.modalHeading": "Snapshot deployment on the environment enables customers' users to sign in and use the platform.", "snapshot.actions.deploy.modalText": "Please note that the deployment process may take some time.", "snapshot.actions.deploy.successMessage": "Snapshot successfully deployed.", "snapshot.actions.deploy.tooltip": "Deploying will allow customer sign-ins.", "snapshot.actions.recall": "Recall", "snapshot.actions.recall.confirmButton": "Recall", "snapshot.actions.recall.errorMessage": "Error while recalling snapshot. \r\nPlease try again.", "snapshot.actions.recall.modalHeading": "Recalling the snapshot can interrupt active client demos.", "snapshot.actions.recall.modalText": "Please note that any information added to the environment after the snapshot was created will be lost. Do you want to continue? \r\nThis may take some time. Please wait.", "snapshot.actions.recall.successMessage": "<PERSON><PERSON><PERSON> is being recalled", "snapshot.actions.recall.tooltip": "Undeploys snapshot from the environment  and reverts it to its initial state.", "snapshot.actions.reset": "Reset", "snapshot.actions.reset.confirmButton": "Reset", "snapshot.actions.reset.errorMessage": "Error while resetting snapshot. Please try again.", "snapshot.actions.reset.modalHeading": "Resetting the snapshot can interrupt active client demos.", "snapshot.actions.reset.modalText": "Please note that any information added to the environment after the snapshot was created will be lost. \r\nDo you want to continue? This may take some time. Please wait.", "snapshot.actions.reset.successMessage": "Snapshot is resetting", "snapshot.actions.reset.tooltip": "Resetting restores the snapshot to its initial state for immediate use.", "snapshot.actions.view": "View", "snapshot.copy.title": "Copy snapshot", "snapshot.create.autoDeletionDateInfoMessage": "Auto deletion date cannot be set when type is Master.", "snapshot.create.changeCompanyLabel": "Change company", "snapshot.create.companiesAndCases": "Companies and business cases", "snapshot.create.companyInformation.businessCases": "Business cases", "snapshot.create.customerInCollaborationWith": "In partnership with:", "snapshot.create.customerUsers": "Customer users", "snapshot.create.deployErrorToast": "Error while deploying snapshot", "snapshot.create.emptyCompanies": "No added companies and business cases", "snapshot.create.emptyUsers": "No added customer users", "snapshot.create.errorToast": "Error while snapshot creation. Please try again.", "snapshot.create.noSelectedCustomersError": "You must select at least one customer to continue.", "snapshot.create.selectCustomersLimitMessage": "Selected customers (up to {$INTERPOLATION}):", "snapshot.create.stepper.titles.step1": "General snapshot information", "snapshot.create.stepper.titles.step2": "Select customers", "snapshot.create.stepper.titles.step3": "Detailed customers information", "snapshot.create.stepper.titles.step4": "Snapshot summary", "snapshot.create.successToast": "Snapshot created and is now deploying", "snapshot.deleteModal.errorToast": "Error while snapshot deletion.\r\nPlease try again.", "snapshot.deleteModal.successToast": "Snapshot successfully deleted", "snapshot.details.headingBackButton": "Snapshots", "snapshot.details.labels.autoDeletionDate": "Auto deletion date", "snapshot.details.labels.cases": "Cases", "snapshot.details.labels.comment": "Comment", "snapshot.details.labels.company": "Company", "snapshot.details.labels.createdBy": "Created by", "snapshot.details.labels.customer": "Customer", "snapshot.details.labels.customerCollaborators": "Organisation partnering with:", "snapshot.details.labels.dateCreated": "Date created", "snapshot.details.labels.dateDeployed": "Date deployed", "snapshot.details.labels.dateRecalled": "Date recalled", "snapshot.details.labels.dateUpdated": "Date updated", "snapshot.details.labels.deployedBy": "Deployed by", "snapshot.details.labels.id": "ID", "snapshot.details.labels.lastUsed": "Last used", "snapshot.details.labels.linkedSnapshots": "Linked snapshots", "snapshot.details.labels.name": "Name", "snapshot.details.labels.noCases": "No cases", "snapshot.details.labels.noCompanies": "No companies", "snapshot.details.labels.noPartnerships": "No partnerships", "snapshot.details.labels.potentialCustomerName": "Potential customer name", "snapshot.details.labels.recalledBy": "Recalled by", "snapshot.details.labels.snapshotCopies": "Copies of current snapshot", "snapshot.details.labels.sourceSnapshot": "Source snapshot", "snapshot.details.labels.status": "Status", "snapshot.details.labels.type": "Type", "snapshot.details.labels.updatedBy": "Updated by", "snapshot.details.labels.version": "Version", "snapshot.details.switchViewState.modified": "Modified", "snapshot.details.switchViewState.modified.tooltip": "Includes all the information added to the environment after the snapshot was created.", "snapshot.details.switchViewState.unmodified": "Unmodified", "snapshot.details.switchViewState.unmodified.tooltip": "Includes all the information at the time the snapshot was created.", "snapshot.details.titles.companiesAndCases": "Companies and business cases", "snapshot.details.titles.generalInformation": "General information", "snapshot.snapshot.createButton": "Create snapshot", "snapshot.snapshot.demoTypes.customerDemo": "Customer demo", "snapshot.snapshot.demoTypes.customerTemplate": "Customer template", "snapshot.snapshot.demoTypes.master": "Master", "snapshot.snapshot.demoTypes.pitchDemo": "Pitch demo", "snapshot.snapshot.demoTypes.pitchTemplate": "Pitch template", "snapshot.snapshot.noResult.description": "Created snapshots will appear here", "snapshot.snapshot.noResult.title": "No snapshots created", "snapshot.snapshot.notFound.title": "No snapshots found", "snapshot.snapshot.table.action": "Action", "snapshot.snapshot.table.autoDeletionDate": "Auto deletion date", "snapshot.snapshot.table.createdBy": "Created by", "snapshot.snapshot.table.dateDeployed": "Date deployed", "snapshot.snapshot.table.name": "Name", "snapshot.snapshot.table.potentialCustomerName": "Potential customer", "snapshot.snapshot.table.status": "Status", "snapshot.snapshot.table.type": "Type", "snapshot.status.created": "Created", "snapshot.status.deployed": "Deployed", "snapshot.status.deploying": "Deploying", "snapshot.status.failed": "Failed", "snapshot.status.recalled": "Recalled", "snapshot.status.recalling": "Recalling", "snapshot.status.resetting": "Resetting", "sortableList.label.addNew": "Add", "sortableList.label.checkbox": "Security", "spinner.loadingMessage": "Loading", "state.branch": "Branch", "state.branch2": "Branch", "state.branchOffice": "Branch office", "state.filial": "Filial", "state.headOffice": "Head office", "stateModal.confirmation.text.conclude": "Conclude", "stateModal.confirmation.text.interrupt": "Interrupt", "statistic.tooltip.case": "Cases", "successfully.invited.guest-users.details": "The invited users will be notified, and they will gain access once they accept", "successfully.invited.guest-users.message": "Invitation sent successfully", "successfully.invited.organization.message": "You have successfully invited the following organization", "support.canceledInvite.button.label": "Inquire now", "support.canceledInvite.hint": " For additonal help, please", "support.canceledInvite.info": " Your invitation has been cancelled. ", "support.canceledInvite.info2": " Get your organization now to ", "support.canceledInvite.ticket": "create a ticket", "support.canceledInvite.ticket2": "and our support team will get back to you!", "support.contactSupport.button.label.submit": "Send inquiry", "support.contactSupport.errorMessage.mail": "Invalid email address", "support.contactSupport.errorMessage.phoneNumber": "Phone number format is incorrect", "support.contactSupport.fieldsList.label": " Email address ", "support.contactSupport.heading": " Set up an account for your organization ", "support.contactSupport.label": " Contact ", "support.contactSupport.label.phone": " Phone number ", "support.contactSupport.subHeading": " Please leave your contact details so that our collaboration managers can get in touch with you to discuss further details. ", "support.contactSupport.textInput.placeholder": "Email address", "support.contactSupport.textInput.placeholder.firstName": "First name", "support.contactSupport.textInput.placeholder.lastName": "Last name", "support.contactSupport.textInput.placeholder.phone": "+49 89 *********", "table.footer.message": "Showing {$INTERPOLATION} - {$INTERPOLATION_1} of {$INTERPOLATION_2} entries", "table.noDataAvailable.message": "No data available", "tableColumns.invoiceNumber": "Invoice number", "tableField.modal.table": "Table", "tableField.modal.title": "Number of columns", "tableField.modal.title2": "Number of rows", "targetCompanyField.select.placeholder": "Search company", "teaserConfigModal.content": "Please select which information should be displayed in the export.", "teaserConfigModal.message": "Select the organization and its corresponding groups to export. Note that groups without data are disabled and cannot be exported. ", "teaserConfigModal.noGroups": "You have no active sections to download.", "teaserConfigModal.settingName": "Private Information", "teaserConfigModal.settingName.TOC": "Table of contents", "teaserConfigModal.settingName.dataRoomGroups": "Data Room groups", "teaserConfigModal.settingName.generalDetails": "General details", "teaserConfigModal.settingName.isRepresentingLeadPartner": "Information visible for interested parties ", "teaserConfigModal.settingName.mainPage": "Title page", "teaserConfigModal.settingName2": "Information visible for participants", "teaserConfigModal.settingName3": "Distribution of financing volume", "teaserConfigModal.settingName4": "Case owner information ", "teaserConfigModal.settingName5": "Firm information", "teaserConfigModal.settingName6": "Print friendly ", "teaserConfigModal.title": "Export to PDF", "teaserDownload.toast.info": "Teaser export started. Your download should be available in a few seconds.", "template.model.lastModifiedDate": "Last modified", "template.model.status": "State", "template.model.title": "Template title", "templateCard.switch.draft": "Draft", "templateCard.switch.published": "Published", "templateEditor.toast.error.cannotEndWith": "File or folder names can't end with: . ", "templateEditor.toast.error.fieldNotInFieldList": "Field key {$PH} is missing from the fields list but exists in {$PH_1} ", "templateEditor.toast.error.fieldNotLinked": "Field {$PH} isn’t linked to a group. Please remove or assign to another group", "templateEditor.toast.error.fieldsNotDefinedAsDocumentFields": "Folders in group {$PH} contain non-document fields", "templateEditor.toast.error.folderDuplicatedName": "Document field from group {$PH} is used multiple times in folders", "templateEditor.toast.error.folderExceedsMaxDepth": "The maximum number of subfolders must not exceed 10", "templateEditor.toast.error.folderExistsInStructure": "Folder {$PH} with ID {$PH_1} is already used", "templateEditor.toast.error.folderFileExist": "A folder with the same name already exists. Please change the name", "templateEditor.toast.error.folderStructureChanged": "Folder structure has been changed while moving / deleting the document or folder", "templateEditor.toast.error.invalidCharacter": "Invalid symbol", "templateEditor.toast.error.message.ifNoErrorDisplayed": "Template could not be updated. Please check your template.", "templateEditor.toast.error.message.ifNoErrorDisplayed2": "Template could not be created. Please check your template.", "templateEditor.toast.error.missingDocumentFieldsInFolderStructure": "Document fields from group {$PH} are missing under its folders", "templateEditor.toast.error.missingFolderStructureFields": "Document fields are missing from group {$PH}", "templateEditor.toast.error.nameMaxLengthReached": "The folder name must contain less than 100 characters", "templateEditor.toast.error.newTemplate.prefix": "Template could not be created. The following problem has been detected:", "templateEditor.toast.error.newTemplateExistingName": "A template with same name already exists.", "templateEditor.toast.error.prefix": "Failed to update template. The following problem has been identified:", "templateEditor.toast.error.wrongOrdinalValue": "The ordinal is missing for folder {$PH} or has incorrect value", "templateEditor.toast.success.suffix": "successfully created.", "templateEditor.toast.success.update.suffix": "updated successfully.", "templateField.composite": "You can only change the value here until you have sent an invitation or received an application.", "templateField.dropdownComponent.warningMessage": "Please set at least 2 dropdown options", "templateField.mirrored-field.participant": "Only the case owner can edit this field", "templateField.showOnMap": "Show on map", "termsAndConditions.acceptTerms.button.label": "Accept", "termsAndConditions.acceptTerms.info": " Please agree to the terms of use. ", "termsAndConditions.acceptTerms.read": " Please read the ", "termsAndConditions.firstPart": "General terms and conditions", "termsAndConditions.secondPart": ", before you accept them", "test": "Test", "text.copied": "<PERSON>pied", "textArea.editField.enterInfo": "to insert a new line", "textArea.editField.enterInfo2": "to insert a line", "thankYou.title": "Thank you very much for your support! ", "themedModal.participantsHint": "Please enable the \"Can see other participants\" setting from \"My partners\" in order to add participants to the chat.", "themedModal.theme": "Theme", "to.signature.overview.tab.text": "To signature overview", "toast.message.error": "An error occurred", "toast.message.reloaded": "Page is being reloaded", "toast.message.success": "Action successfully executed", "toast.message.warning": "Incorrect data", "toastMessage.error.wrongDate": "Wrong date", "toaster.message.success": "Case successfully created.", "todo.status.archived": "Archived", "todo.status.cancel": "Cancelled", "todo.status.completed": "Completed", "todo.status.pending": "Pending", "todoItem.completed.message": "To do item completed", "todoList": "To-do list", "todoList.summary.noNewTasks": "No new tasks\r\n", "todoModal.update.button.label": "Update", "todos.badge.provideData": "Provide data", "todos.badge.reviewContract": "Review contract", "todos.button.openCase": "Open case", "todos.closedBy.system": "System", "todos.manualTodo.cancel": "", "todos.noDueDate": "No Due Date", "todosList.openTodoPage.button": "Open To-do page", "todosManagement.cancelToDo.toast.success": "To Do item canceled successfully", "todosManagement.cancelToModal.label": "Do you want to cancel this To do item?", "todosManagement.closed": "Closed", "todosManagement.completeToDo.toast.success": "To Do item completed successfully", "todosManagement.completed": "Completed", "todosManagement.createToDoModal.controlLabel.assignee": "Assignee*", "todosManagement.createToDoModal.controlLabel.case": "Case*", "todosManagement.createToDoModal.controlLabel.description": "Description*", "todosManagement.createToDoModal.controlPlaceholder.assignee": "Select assignees", "todosManagement.createToDoModal.controlPlaceholder.case": "Select Case ID", "todosManagement.createToDoModal.controlPlaceholder.company": "Select Company", "todosManagement.createToDoModal.error.invalidDate.message": "Invalid date.", "todosManagement.createToDoModal.error.minDate.message": "Date cannot be in the past.", "todosManagement.createToDoModal.headerLabel": "Create To do item", "todosManagement.createToDoModal.toast.success": "To Do item successfully created", "todosManagement.delegated": "Delegated", "todosManagement.dropdown.prefix": "Showing:", "todosManagement.filters.sortBy": "Sort by", "todosManagement.loadMoreButton": "Show more {$START_TAG_FIN_ICON}{$CLOSE_TAG_FIN_ICON}", "todosManagement.myTasks": "My tasks", "todosManagement.noCaseRelated": "Not case-related", "todosManagement.noTasks.header.message.closed": "No Closed tasks", "todosManagement.noTasks.header.message.completed": "No Completed Tasks", "todosManagement.noTasks.header.message.emptySearch": "No Results ", "todosManagement.noTasks.header.message.pending": "No pending tasks", "todosManagement.noTasks.regular.message.closed": "Your closed tasks will appear here", "todosManagement.noTasks.regular.message.completed": "Your completed tasks will appear here.", "todosManagement.noTasks.regular.message.emptySearch": "There are no results for the applied criteria.", "todosManagement.noTasks.regular.message.pending": "Your pending tasks will appear here.", "todosManagement.pending": "Pending", "todosManagement.showTask": "Show task", "todosManagement.sortingOptions.asc": "Oldest First", "todosManagement.sortingOptions.desc": "Newest First", "todosManagement.summary.description.manualAssignment": "Handle request", "todosManagement.summary.description.provideData": "Provide information in the Data Room for the {$START_TAG_SPAN}{$INTERPOLATION}{$CLOSE_TAG_SPAN} field.", "todosManagement.summary.description.reviewApplicationFrom": "Review application from", "todosManagement.summary.description.reviewContract": " Review contract {$START_TAG_SPAN}“{$INTERPOLATION}”.{$CLOSE_TAG_SPAN}", "todosManagement.summary.dueDate": " Due date: {$START_TAG_SPAN}{$INTERPOLATION}{$CLOSE_TAG_SPAN}", "todosManagement.summary.tasksCount": "{$PH} Task", "todosManagement.summary.tasksCounts": "{VAR_PLURAL, plural, one {1 Task} other {{INTERPOLATION} Tasks}}", "todosManagement.table.columns.assignedBy": "Assigned By", "todosManagement.table.columns.assignedTo": "Assigned To", "todosManagement.table.columns.closedBy": "Closed By", "todosManagement.table.columns.closedOn": "Closed On", "todosManagement.table.columns.completedBy": "Completed By", "todosManagement.table.columns.completedOn": "Completed On", "todosManagement.table.columns.description": "Description", "todosManagement.table.columns.dueDate": "Due Date", "todosManagement.table.columns.status": "Status", "todosManagement.table.columns.type": "To do type", "todosManagement.table.description.reviewContract.prefix": "Review contract {$START_TAG_SPAN}\"{$INTERPOLATION} / {$INTERPOLATION_1}\"{$CLOSE_TAG_SPAN} and respond accordingly.", "todosManagement.tasks": "Tasks", "todosManagement.toDo.toast.error": "An error occurred. Please try again", "todosManagement.toDo.toast.success": "To Do item updated successfully", "todosManagement.toDo.update.toast.error": "An error occurred", "todosManagement.tooltip.archivedAfter60days": "Tasks are archived automatically in 60 days.", "todosManagement.updateToDoModal.error.minDate.message": "Date cannot be in the past", "todosManagement.updateToDoModal.headerLabel": " Edit To do item", "todosManagement.viewToDoModal.headerLabel": "View to do item", "topic.pipe.miscellaneous": "Other change", "topic.pipe.visibilityChanged": "Visibility changed", "typeToSearchText": "Search", "uboAssigneModal.content": "As the ownership is now above 25%, this person has been added to the UBO list.", "uboAssigneModal.title": "UBO assigned", "uboRemovedModal.content": "As the ownership is now below 25%, this person has been removed from the UBO list and reclassified as an ordinary shareholder.", "uboRemovedModal.title": "UBO removed", "unregistered.user.error": "The user you are trying to invite does not exist in the neoshare platform.", "upload.file.size.error": "The file is too large. The maximum file size is 15 MB", "uploadOrganizationLogoModal.dropZoneTitle": "Select a PNG file", "uploadOrganizationLogoModal.title": "Upload logo", "usageContract.preview.modal.voidContract": "Void", "usageContract.previewModal.title": "Usage contract data", "usageContractCard.text": "Date of signature", "usageContractList.createAgreements": "Create Agreements", "usageContracts.button.sign": "Sign", "usageContracts.description": "Please note that this action will void the whole document, even if it was already signed by any other parties.", "usageContracts.title": "Do you wish to reject signing the document?", "user-menu.option1": "My Account", "userAuthentication.accountIsLockedText": "Your account is currently locked. Check your email for further instructions.", "userAuthentication.message": "Still haven’t received the security code? Try to send a new one after", "userAuthentication.message2": ".", "userAuthentication.text": "You need go through the two factor authentication process.", "userAuthentication.timeOutMessage": "Still haven't received the security code?", "userAuthentication.timeOutMessage2": "Send a new one.", "userAuthentication.wrongCodeText": "Wrong code. You have {$INTERPOLATION} attempts left.", "userAuthentication.wrongCodeTextSingle": "Wrong code. You have 1 attempt left.", "userAuthentication.wrongCodeTextSingular": "Wrong code. You have 1 attempt left.", "userComponentMessage": "Warning! There is currently no user with the role of contact person. Invitations to financing projects will thus be lost.", "userComponentSearchEmptyState": "We could not find any users for the criteria applied.", "userData.toast.error": "User data could not be saved. Please check your entry.", "userData.toast.success": "User data successfully saved.", "userDataForm.accountSecurity.authenticatedDevices": "Authenticated devices", "userDataForm.accountSecurity.code.description": "When logging in, a 6-digit code will be sent to your registered email in neoshare.", "userDataForm.accountSecurity.description": "For enhanced protection against unauthorized access, users must provide two or more authentication methods. We recommend keeping this security measure enabled.", "userDataForm.accountSecurity.enabled": "Enable", "userDataForm.accountSecurity.header": "Multi-factor authentication", "userDataForm.accountSecurity.password.description": "To change your password you will be redirected to neoshare login and from there you can reset it with new one.", "userDataForm.accountSecurity.settings": "Authentication options", "userDataForm.authenticatedDevices.label.current": "Current device", "userDataForm.button.label.leaveForLater": "Proceed later", "userDataForm.button.label.logOut": "Log out", "userDataForm.control.label": "Salutation", "userDataForm.control.label.language": "Language", "userDataForm.control.label.lastName": "Last name*", "userDataForm.control.label.location": "Location*", "userDataForm.control.label.mobile": "Mobile phone number", "userDataForm.control.label.multiFactorAuthentication": "Multi-factor authentication", "userDataForm.control.label.name": "First name*", "userDataForm.control.label.title": "Title", "userDataForm.dialog.delete": "Yes, I consent", "userDataForm.dialog.title": "Are you sure you want to remove {$PH} from your authenticated device list?", "userDataForm.email": "Email address", "userDataForm.errorMessage.mobileNumber": "Add number in this field", "userDataForm.errorMessage.multiFactorSettings": "You can’t use the SMS option without a phone.", "userDataForm.errorMessage.phoneNumber": "The phone number must contain a country code, e.g. '+**********'.", "userDataForm.errorMessage.requiredField4": "The phone number must contain a country code, e.g. '+**********'.", "userDataForm.errorMessage.unacceptableSymbol": "Invalid symbol", "userDataForm.errorMessage.unacceptableSymbol2": "Invalid symbol", "userDataForm.errorMessage.wrongMail": "Invalid email address", "userDataForm.label.department": "Department", "userDataForm.label.position": "Position", "userDataForm.label.tel": "Landline phone number", "userDataForm.modal.title": "Changes saved! You have chosen {$PH} for primary authentication. Please log out to apply this.", "userDataForm.tabs.accountSecurity": "Account security", "userDataForm.tabs.personalInformation": "Personal information", "userDataForm.tabs.preferences": "Preferences", "userDataForm.text.pleaseEnter": "Please only use \"+\" and numbers", "userDataForm.title": "Personal information", "userDataForm.toast.error.message": "Add a mobile number or change your multi-factor authentication option.", "userDataForm.toast.success.logOut": "Changes are saved. You will be logged out in 10 seconds", "userDataForm.toolTipContent2": "Please only use \"+\" and numbers", "userEditor.createdUserMessage": "User successfully created.", "userEditor.creatingUserError": "Error while creating the user.", "userEditor.mailExistsError": "The email address is already registered.", "userEditor.title": "User Information", "userEditor.title.new": "Create new user", "userEditor.title.newUser": "Create new user", "userEditor.updatedUserMessage": "User updated successfully.", "userEditor.updatingUserError": "Error while updating the user.", "userEditorDeleteUser.message": "You hereby agree to the pseudonymization of the user and the deletion of his account. The deletion is carried out independently by the data or user responsible.", "userEditorDeleteUser.permanentDeleteEmailMatch": "Email address of the user", "userEditorDeleteUser.permanentDeleteEmailMatchError": "Invalid email address", "userEditorDeleteUser.title": "Do you want to permanently delete this user?", "userExperience.feedback.invite.text": "Can you please share your opinion with us? Are you satisfied with the answer?", "userExperience.feedback.submit.button.label": "Send feedback", "userImport.toaster.message.success": "Users successfully imported.", "userList.createUser": "Create user", "userManagement.list.title": "User management", "userManagement.tabs.accountLogs": "Account logs", "userManagement.tabs.multiFactorAuth": "Multi-factor authentication", "userManagement.tabs.password": "Password guidelines", "userManagement.tabs.signer": "User", "userManagement.tabs.userInformation": "User information", "userManagement.tooltip.delete": "Another administrator role must be assigned first, since there must be at least two administrators to be able to delete one.", "userManagement.user.column.firstName": "First name", "userManagement.user.column.lastName": "Last name", "userManagement.user.column.mail": "Email address", "userManagement.user.column.status": "Status", "userManagement.user.delete.success": "User was successfully deleted", "userManagement.user.label.export": "Export user", "userManagement.user.label.import": "Import user", "userManagement.user.label.openCreate": "Create user", "userManagement.user.label.placeholder": "Search user...", "userManagement.user.label.rework": "edit", "userManagement.user.userEditor.caseParticipation.soloCase": "Sole case participant: {$INTERPOLATION}", "userManagement.user.userEditor.caseParticipation.title": "Case participation", "userManagement.user.userEditor.caseParticipation.totalCases": "Total cases: {$INTERPOLATION}", "userManagement.user.userEditor.checkbox.admin": "Administrator", "userManagement.user.userEditor.checkbox.backOffice": "Platform user", "userManagement.user.userEditor.checkbox.contact": "Contact person", "userManagement.user.userEditor.checkbox.legalOfficer": "Legal Representative", "userManagement.user.userEditor.checkbox.manager": "Collaboration Manager", "userManagement.user.userEditor.checkbox.platformManager": "Platform manager", "userManagement.user.userEditor.checkbox.usageContractSigner": "User agreement signer", "userManagement.user.userEditor.delete": "Delete", "userManagement.user.userEditor.deleteSolePlatformManagerTooltip": "This user cannot be deleted as your organisation currently lacks a platform manager.", "userManagement.user.userEditor.label": "Active", "userManagement.user.userEditor.label.academicTitle": "Title", "userManagement.user.userEditor.label.accessLevel": " Access authorizations ", "userManagement.user.userEditor.label.department": "Department", "userManagement.user.userEditor.label.firstName": "First name", "userManagement.user.userEditor.label.landLineNumber": "Landline phone number", "userManagement.user.userEditor.label.lastName": "Last name", "userManagement.user.userEditor.label.mail": "Email address", "userManagement.user.userEditor.label.mobileNumber": "Cell/mobile phone number", "userManagement.user.userEditor.label.name": "Salutation", "userManagement.user.userEditor.label.organisation": "Organisation", "userManagement.user.userEditor.label.position": "Position", "userManagement.user.userEditor.multiFactor.label.active": "Multi-factor authentication active", "userManagement.user.userEditor.password.label.UpperCase": "At least one capital letter", "userManagement.user.userEditor.password.label.digits": "At least one number", "userManagement.user.userEditor.password.label.expiration": " Password expires (days) ", "userManagement.user.userEditor.password.label.length": "Minimum length (8 characters)", "userManagement.user.userEditor.password.label.lowerCase": "At least one lowercase letter", "userManagement.user.userEditor.password.label.notUserName": "Must not contain the email address", "userManagement.user.userEditor.password.label.specialChars": "At least one special character", "userManagement.user.userEditor.platformUserSolePlatformManagerTooltip": "The user role cannot be changed because your organization currently lacks a platform manager.", "userManagement.user.userEditor.solePlatformManagerTooltipForDeleteButton": "This user cannot be deleted as your organisation currently lacks a platform manager.", "userManagement.user.userEditor.solePlatformManagerTooltipForPlatformUserCheckbox": "The user role cannot be changed because your organization currently lacks a platform manager.", "userManagement.user.userEditor.solePlatformManagerTooltipForUserStatus": "The user cannot be deactivated as your organization currently lacks a platform manager.", "userManagement.user.userEditor.status.description": "Please note: Deactivated users do not have access to neoshare.", "userManagement.user.userEditor.status.title": "User status", "userManagement.user.userEditor.statusSolePlatformManagerTooltip": "The user cannot be deactivated as your organization currently lacks a platform manager.", "userManagement.user.userEditor.svgDeleteAction.message": " Click \"Delete\" to permanently delete the user. This action cannot be undone. ", "userManagement.user.userEditor.svgDeleteAction.title": "Permanent delete", "userManagement.user.userEditor.unregistered": "Unregistered", "userManagement.userImportModal.button.label.import": "Import user", "userManagement.userImportModal.modalSubTitle": " Load new user list ", "userManagement.userImportModal.modalTitle": " Import user ", "userManagement.userImportModal.singleFileUpload.placeholder": "Enter last name", "userManagement.users.noData": "Load data...", "userModel.title.Ddr": "Dr.", "userModel.title.Dipl.-Ing.oderDI": "Dipl. -Ing. or  DI2", "userModel.title.Dkfm": "Dkfm.", "userModel.title.Dr.": "Dr.", "userModel.title.Ing.": "Ing.", "userModel.title.MMag.": "MMag.", "userModel.title.Mag": "Mag.", "userModel.title.Prof.": "Prof.", "userModel.title.Prof. Dr.": "Prof. Dr.", "userModels.language.english": "English", "userModels.language.german": "German", "userModels.salutations.1": "Ddr.", "userModels.salutations.2": "Dipl. Ing. or DI", "userModels.salutations.3": "Dkfm.", "userModels.salutations.4": "Dr.", "userModels.salutations.5": "Ing.", "userModels.salutations.6": "Mag.", "userModels.salutations.7": "MMag.", "userModels.salutations.8": "Prof.", "userModels.salutations.9": "Prof. Dr.", "userModels.salutations.mister": "Mr.", "userModels.salutations.mrs": "Mrs.", "userSettings.button.label.logout": "Sign out", "userSettings.button.label.updatePass": "Change password", "userSettings.deletePhoto.tooltip": "Delete picture", "userSettings.feedback": "<PERSON><PERSON><PERSON>", "userSettings.label.notVerfied": "Not verified", "userSettings.label.verfied": "Verified", "userSettings.language.english": "English", "userSettings.language.german": "German", "userSettings.preferences.regionalSettings.currencyFormat": "Currency format:", "userSettings.preferences.regionalSettings.dateFormat": "Date format:", "userSettings.preferences.regionalSettings.numberFormat": "Numbers format:", "userSettings.preferences.regionalSettings.percentageFormat": "Percentage format:", "userSettings.preferences.regionalSettings.region.germany": "Germany", "userSettings.preferences.regionalSettings.region.unitedKingdom": "United Kingdom", "userSettings.preferences.regionalSettings.regionSelectLabel": "Select your preferred region:", "userSettings.preferences.regionalSettings.title": "Language & Region", "userSettings.preferences.termsOfUse.GDPR.content": "The legal basis for data processing is your consent in accordance with Art. 6 para. 1 cl. 1 lit. a GDPR. {$LINE_BREAK}You can find further information on data processing in the {$START_LINK}Privacy policy{$CLOSE_LINK}.", "userSettings.preferences.termsOfUse.GDPR.title": "GDPR", "userSettings.preferences.termsOfUse.analysisPanel.content": "I agree that my interactions with the neoshare platform will be analyzed improve and further develop the platform. You can revoke your consent to the recording of platform interactions at any time in the “Preferences” settings in “My Account.", "userSettings.preferences.termsOfUse.analysisPanel.contentTitle": "Usability data and analysis", "userSettings.preferences.termsOfUse.analysisPanel.title": "Usability data and analysis", "userSettings.preferences.termsOfUse.marketingPanel.content": "I agree that neoshare AG uses my data (title, first name, last name, work email address, company, position in the company, country) to send me targeted advertising by email and to inform me about their products, services, offers, and events. I agree that the data, as well as information about whether I have received the emails and the extent to which I have interacted with the content, may be used to measure the success of email campaigns. \r\n\r\nConsent to receive marketing emails and to have your interactions with them evaluated can be revoked at any time using the unsubscribe link in the emails or in the “Preferences” settings in “My Account.”", "userSettings.preferences.termsOfUse.marketingPanel.contentTitle": "Marketing emails", "userSettings.preferences.termsOfUse.marketingPanel.title": "Marketing emails", "userSettings.preferences.termsOfUse.organisationsWarning": "Please note that the Declaration of Consent applies exclusively to the organisations associated with your login.", "userSettings.preferences.termsOfUse.subtitle": "For a seamless and secure experience.", "userSettings.preferences.termsOfUse.title": "Declaration of Consent", "userSettings.preferences.termsOfUse.warning": "Please accept our terms of use", "userSettings.resetPasswordModal.label": " You will be logged out automatically in 15 seconds. Please log in again to set your new password. ", "userSettings.resetPasswordModal.title": " The password change has been initiated. ", "userSettings.sessionForm.feedback": "Show feedback button", "userSettings.settingsSection.authMode": " Authenticator App ", "userSettings.settingsSection.button.label.code": "Resend code", "userSettings.settingsSection.button.label.verify": "Verify", "userSettings.settingsSection.header": " Multi-factor authentication (MFA) ", "userSettings.settingsSection.mail": " Email", "userSettings.settingsSection.page.header": "My Account", "userSettings.settingsSection.phoneHint": " Please enter your cell/mobile number starting with the country code for Germany (+49): ", "userSettings.settingsSection.placeholder.phone": "Cell/mobile number (+49...)", "userSettings.settingsSection.subTitle": " Authentication method ", "userSettings.settingsSection.textInput.placeholder": "SMS verification code", "userSettings.settingsection.button.label": "Save changes", "userSettings.title": " Edit user data ", "userSettings.toast.error": "The password could not be reset.", "userSettings.toast.error.multiFactor": "The multifactor authentication method could not be updated.", "userSettings.toast.error.resendSMS": "No SMS could be sent to you.", "userSettings.toast.success": "The multifactor authentication method has been updated.", "userSettings.toast.success.resendSMS": "SMS has been sent to you again.", "userSettings.toast.warning": "Your input is incorrect. Please correct your input.", "userSettings.toastMessage.error.mobileNumber": "Your mobile number could not be verified.", "userSettings.toastMessage.error.wrongSMSCOde": "Wrong SMS code. Please check your entry.", "userSettings.toastMessage.success": "Your cell/mobile number has been successfully verified!", "userSettings.uploadPhoto.tooltip": "Upload profile photo", "userSettings.uploadPhotoModal.dropZoneTitle": "Select a JPG, JPEG or PNG file", "userSettings.uploadPhotoModal.dropZoneTitle2": "or drag & drop it here", "userSettings.uploadPhotoModal.fileTooLarge": "The file is too large. The maximum file size is 10 MB.", "userSettings.uploadPhotoModal.title": "Upload profile photo", "userSettings.userRoles.roles": "Assigned roles", "userSettings.verificationCode": " We sent an SMS to the number entered for verification purposes. Please enter the code: ", "userSigning.asideLayout.logoTitle": "Next generation-security powered by AI", "userSigning.asideLayout.title": "Welcome to", "userSigning.language.label": "Select language:", "userSigning.leftLayout.title": "Welcome to", "userSigning.mainPage.eightSectionText": " You can always reject signing the document by clicking the Reject button. Please note that this  action will void the whole document even if it was already signed by any other parties.", "userSigning.mainPage.fifthSectionText": " created in a way that allows the signatory to retain control;", "userSigning.mainPage.firstSectionText": "Digital signatures are like electronic “fingerprints.” They are a specific type of electronic signature (e-signature). In the next steps you will be able to review and sign your document using the ", "userSigning.mainPage.firstSectionText2": "a standard regulated by", "userSigning.mainPage.firstSectionText3": " European Union's regulatory framework for electronic signatures that is adopted by all EU member states. ", "userSigning.mainPage.firstSectionText4": "Advanced Electronic Signature (AdES), ", "userSigning.mainPage.fourthSectionText": " uniquely linked to and capable of identifying the signatory;", "userSigning.mainPage.secondSectionText3": "The", "userSigning.mainPage.secondSectionText4": "The Advanced Electronic Signature (AdES)", "userSigning.mainPage.seventhSectionText": " By clicking ", "userSigning.mainPage.seventhSectionText2": "Next", "userSigning.mainPage.seventhSectionText3": ", you will receive a signature authentication code to your email. Please use the code to verify your identity, review and sign the document in the next step.", "userSigning.mainPage.sixtSectionText": " linked to the document in a way that any subsequent change of the data is detectable.", "userSigning.mainPage.thirdSectionText": " is an electronic signature which adds the following key features on top of the ", "userSigning.mainPage.thirdSectionText2": "'Simple' electronic signature:", "userSigning.mainPage.thirdSectionText3": "has the following main features:", "userSigning.mainPage.title": "Sign legally binding documents digitally using neoshare, powered by ", "userSigning.reject.description": "All involved parties were notified.", "userSigning.reject.title": "You have rejected signing the document.", "userSigning.sessionExpired.title": "Oops... Your session has expired.", "userSigning.signLater.description": "When you are ready to sign the document, please restart the process from the initial email.", "userSigning.signLater.title": "See you later.", "userSigning.success.description": "Once all parties sign, you'll get the final copy by email.", "userSigning.success.title": "You have signed the document.", "userSigning.tryAgain": "Try again", "userSigning.voided.description": "Please contact the user who requested signing the document for more details. This information can be found in the initial email invitation.", "userSigning.voided.title": "The document signing was voided.", "userTable.columns.createdOn": "Created on", "userTable.columns.name": "Name", "userTable.noRole": "No role assigned", "userValidation.currentPasswordIncorrect": "Wrong password!", "userValidation.migrated-users-warning": "Please notice: We have sent you a new password via email as part of our security upgrade. Please use this new password to log in and continue with the neoshare platform.", "userValidation.remainingAttempts": "You have {$INTERPOLATION} attempts left.", "userValidation.remainingAttemptsSingular": "You have 1 attempt left.", "userValidation.wrongPassword": "Invalid password. You have {$INTERPOLATION} attempts left.", "userValidation.wrongPasswordSingular": "Invalid password. You have 1 attempt left.", "users.removeUser.tooltipMessage": "Another administrator role must be assigned first, since there must be at least two administrators to be able to delete one.", "users.roles.disabledPlatformUser": "Platform user cannot be unassigned if Contact person or Platform manager is selected.", "users.usersEdit.errorMessage.emailTaken": "This e-mail address is taken", "validationError.ascendingOrder": "The range field values should be in ascending order", "validationError.atleastOneUserSelected": "At least one user must be selected.", "validationError.invalidDate": "Invalid date", "validationError.invalidSymbol": "Invalid symbol", "validationError.mailTaken": "The email address is already registered.", "validationError.message": "The email address is already registered.", "validationError.nameNotUnique": "This group name is already in use.", "validationError.requiredField": "Mandatory field", "validationError.wrongEmailAddress": "Invalid email address", "volksbankGroups.annualFinancialStatements": "Annual financial statements / P&L calculations", "volksbankGroups.articlesOfAssociationLegal": "Articles of association (legal relationships)", "volksbankGroups.articlesOfAssociationLegitimation": "Articles of association (legitimation)", "volksbankGroups.authorizationOfEasements": "Authorization of easements", "volksbankGroups.buildingEncumbrances": "Building encumbrances/register of contaminated sites ", "volksbankGroups.buldingController": "Building controller", "volksbankGroups.bwa": "BWA", "volksbankGroups.collateralManagementLoan": "Collateral management loan", "volksbankGroups.collateralManagementLoanOther": "Collateral management loan Other", "volksbankGroups.contracts": "Contracts", "volksbankGroups.contractsDeeds": "Contracts / deeds", "volksbankGroups.creditworthiness": "Creditworthiness", "volksbankGroups.economicConditionsLoan": "Economic conditions loan", "volksbankGroups.economicConditionsLoanOther": "Economic conditions loan Other", "volksbankGroups.enforceableCopies": "Enforceable copies", "volksbankGroups.floorPlansParcelMaps": "Floor plans, parcel maps", "volksbankGroups.generalInformation": "General information", "volksbankGroups.gsDeclarationsOfPurpose": "GS declarations of purpose", "volksbankGroups.hrExtractsShareholdersLegal": "HR extracts, shareholders (legal relationships)", "volksbankGroups.hrExtractsShareholdersLegitimation": "HR extracts, shareholders (legitimation)", "volksbankGroups.income": "Income", "volksbankGroups.incomeTaxDocuments": "Income tax documents", "volksbankGroups.industryInformation": "Industry information", "volksbankGroups.ioBalanceSheet": "I/O balance sheet", "volksbankGroups.kwgDocumentation": "§18 KWG documentation", "volksbankGroups.landChargeLetters": "Land charge letters", "volksbankGroups.landChargeLoan": "Land charge loan", "volksbankGroups.landChargeLoanOther": "Land charge loan Other", "volksbankGroups.landRegisterExtracts": "Land register extracts", "volksbankGroups.leaseholdContracts": "Leasehold contracts", "volksbankGroups.liquidityCalculations": "Liquidity calculations", "volksbankGroups.otherCreditworthinessDocuments": "Other creditworthiness documents", "volksbankGroups.otherInformation": "Other information", "volksbankGroups.partitionDeclarations": "Partition declarations", "volksbankGroups.personGeneralInformation": "Person General information", "volksbankGroups.personLegalRelationships": "Person legal relationships", "volksbankGroups.personLegalRelationshipsOther": "Person legal relationships Other", "volksbankGroups.personLegitimation": "Person legitimation", "volksbankGroups.personLegitimationOther": "Person legitimation Other", "volksbankGroups.projectDocuments": "Project documents", "volksbankGroups.propertyDocuments": "Property documents", "volksbankGroups.propertyPictures": "Property pictures", "volksbankGroups.purchaseContracts": "Purchase contracts", "volksbankGroups.realEstateManagementLoan": "Real estate management loan", "volksbankGroups.realEstateManagementLoanOther": "Real estate management loan Other", "volksbankGroups.rentalIncome": "Rental income", "volksbankGroups.rentalLeaseAgreements": "Rental/lease agreements", "volksbankGroups.salaryStatement": "Salary statement", "volksbankGroups.schufaInformation": "Schufa information", "volksbankGroups.selfInformation": "Self information", "volksbankGroups.singleValuationDeclarations": "Single valuation declarations", "volksbankGroups.statementOfAssets": "Statement of assets", "volksbankGroups.statementOfAssetsSelfDeclaration": "Statement of assets /self-declaration", "volksbankGroups.supplementaryValuationDocuments": "Supplementary valuation documents", "volksbankGroups.taxAssessmentNotice": "Tax assessment notice", "volksbankGroups.taxConsultantPowerOfAttorney": "Tax consultant power of attorney", "volksbankGroups.taxReturn": "Tax return", "volksbankGroups.valuationsAppraisals": "Valuations / appraisals", "warning.field.totalEquity": "The sum of the own capital building blocks differs from the total amount.", "watchlisttabs.header": "All results", "write.email.label": "Write email"}}