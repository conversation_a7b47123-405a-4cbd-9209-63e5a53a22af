// styles from components library
@use 'styles/src/lib/styles';
@use 'styles/src/lib/common';
@import 'node_modules/@siemens/ngx-datatable/index.scss';
@import 'node_modules/@siemens/ngx-datatable/themes/material.scss';
@import 'node_modules/@siemens/ngx-datatable/assets/icons.css';
@import '~jointjs-plus/joint-plus.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

canvas.hiddenCanvasElement {
  @apply tw-hidden;
}

.accordion {
  --bs-accordion-color: theme('colors.color-text-primary');
}

.pointer-events-on-document-section {
  ui-document-field {
    pointer-events: all !important;
  }

  fin-document,
  fin-directory {
    background: transparent !important;
  }
}

.outline-2-interactive {
  outline: 0.2rem solid theme('colors.color-text-interactive') !important;
}

.ng-option.ng-option-marked {
  background-color: theme('colors.color-surface-hover') !important;
}

// classes for SVGs
.color-surface-hover-fill {
  fill: theme('colors.color-background-tertiary-minimal');
}

.color-surface-hover-stroke {
  stroke: theme('colors.color-background-tertiary-minimal');
}

.color-surface-tertiary-fill {
  fill: theme('colors.color-surface-tertiary');
}

.color-icons-primary-fill {
  fill: theme('colors.color-icons-primary');
}

.color-icons-primary-stroke {
  stroke: theme('colors.color-icons-primary');
}

.color-icons-disabled-fill {
  fill: theme('colors.color-icons-disabled');
}

.color-icons-disabled-stroke {
  stroke: theme('colors.color-icons-disabled');
}

.color-icons-success-fill {
  fill: theme('colors.color-icons-success');
}

.color-icons-error-fill {
  fill: theme('colors.color-icons-error');
}

.color-icons-error-stroke {
  stroke: theme('colors.color-icons-error');
}

.color-icons-brand-primary-fill {
  fill: theme('colors.color-icons-brand-primary');
}

.color-icons-brand-primary-stroke {
  stroke: theme('colors.color-icons-brand-primary');
}

.color-icons-informative-fill {
  fill: theme('colors.color-icons-informative');
}

.color-icons-informative-stroke {
  stroke: theme('colors.color-icons-informative');
}

.color-icons-secondary-fill {
  fill: theme('colors.color-icons-secondary');
}

.color-icons-secondary-stroke {
  stroke: theme('colors.color-icons-secondary');
}

.color-icons-warning-fill {
  fill: theme('colors.color-icons-warning');
}

.color-background-tertiary-subtle-fill {
  fill: theme('colors.color-background-tertiary-subtle');
}

.color-background-neutral-minimal-fill {
  fill: theme('colors.color-background-neutral-minimal');
}

.color-background-neutral-minimal-stroke {
  stroke: theme('colors.color-background-neutral-minimal');
}

.color-hover-tertiary-fill {
  fill: theme('colors.color-hover-tertiary');
}

.color-icons-light-fill {
  fill: theme('colors.color-icons-light');
}

.color-icons-light-stroke {
  stroke: theme('colors.color-icons-light');
}

.color-background-dark-subtle-fill {
  fill: theme('colors.color-background-dark-subtle');
}

.color-background-tertiary-minimal-fill {
  fill: theme('colors.color-background-tertiary-minimal');
}

.color-icons-tertiary-fill {
  fill: theme('colors.color-icons-tertiary');
}

.color-icons-tertiary-stroke {
  stroke: theme('colors.color-icons-tertiary');
}

.color-background-secondary-minimal-fill {
  fill: theme('colors.color-background-secondary-minimal');
}

.color-background-attention-subtle-fill {
  fill: theme('colors.color-background-attention-subtle');
}

.color-background-warning-subtle-fill {
  fill: theme('colors.color-background-warning-subtle');
}

.color-background-warning-subtle-fill {
  fill: theme('colors.color-background-warning-subtle');
}

.color-surface-dark-strong-fill {
  fill: theme('colors.color-surface-dark-strong');
}

.color-brand-secondary-fill {
  fill: theme('colors.color-brand-secondary');
}

.color-surface-secondary-fill {
  fill: theme('colors.color-surface-secondary');
}

.color-surface-secondary-stroke {
  stroke: theme('colors.color-surface-secondary');
}

.color-background-primary-strong-fill {
  fill: theme('colors.color-background-primary-strong');
}

.color-background-primary-strong-stroke {
  stroke: theme('colors.color-background-primary-strong');
}

//classes for SVGs - end

.user-import-modal {
  .modal-dialog {
    max-width: 34rem;
  }
}

ui-icon {
  -webkit-user-select: none;
  user-select: none;
}

.teaser-export-modal {
  .modal-dialog {
    width: 56.5rem;
    max-width: unset;
  }
}

// TODO - create-contract-modal is used in 1 more place in this file, with diff sizes
.create-contract-modal {
  .modal-dialog {
    max-width: 35.6rem;
    max-height: 65.3rem;
  }
}

.copy-information-modal-cadr {
  .modal-dialog {
    max-width: 50rem;
  }
}

// copy-information-modal-cadr is used 1 more time
.contract-details-contract,
.create-usage-contract-modal,
.account-management-customers-modal {
  .modal-dialog {
    .modal-content {
      max-height: 75rem;
    }

    max-width: 60rem;
  }
}

.edit-customer-user-snapshot-modal {
  .modal-dialog {
    .modal-content {
      max-height: 80rem;
    }

    max-width: 60rem;
  }
}

// TODO - clean?  `account-management-customers-modal` is used 2 more times in this file(with different modal content sizes)
.account-management-customers-modal {
  .modal-dialog {
    .modal-content {
      max-height: 75rem;
    }

    max-width: 100.6rem;
  }
}

// TODO - account-management-users-modal is used 1 more time with different modal-content size
.account-management-users-modal {
  .modal-dialog {
    .modal-content {
      max-height: 75rem;
    }

    max-width: 99rem;
  }
}

@include common.for-smaller-height-than(800) {
  .create-usage-contract-modal,
  .account-management-customers-modal,
  .account-management-users-modal,
  .contract-details-contract {
    .modal-dialog {
      .modal-content {
        max-height: 98%;
      }
    }
  }
}

.information-modal {
  .modal-dialog {
    min-width: 100rem;

    .modal-content {
      overflow: unset;

      .modal-header {
        padding: 1rem 3.5rem;
      }

      .modal-body {
        padding: 0;
        background: theme('colors.color-surface-primary');
      }
    }
  }
}

.groups-sharing-modal {
  --bs-modal-width: 64.5rem;
}

.add-financing-block-modal {
  .modal-dialog {
    width: 41rem;
  }
}

.duplicate-case-user-modal {
  .modal-dialog {
    min-width: 90rem;

    .inner-container {
      height: 51rem;
    }

    .modal-content {
      overflow: unset;

      .modal-header {
        padding: 1rem 3.5rem;
      }

      .modal-body {
        padding: 0;
        background: theme('colors.color-surface-primary');
      }
    }
  }
}

.application-modal,
.application-warning-modal {
  .modal-content {
    min-width: 66rem;
    height: 51rem;
  }
}

.segment-modal {
  .modal-dialog {
    min-width: 40rem;

    .modal-header {
      background: theme('colors.color-surface-primary');
    }
  }
}

.confirmation-of-participation-withdraw {
  .modal-content {
    max-width: 40rem;
    height: 34rem;
  }
}

.upload-photo {
  .modal-content {
    max-width: 45rem;
    max-height: 53rem;
  }
}

.group-portal-actions-modal,
.group-visibility-actions-modal,
.manage-participants-access-rights {
  .modal-dialog {
    width: 63rem;
    min-width: 63rem;
  }
}

.rerequest-modal {
  .modal-dialog {
    width: 80rem;
    min-width: 80rem;
  }
}

.document-preview-modal {
  .modal-dialog {
    max-width: 90rem;
    width: 90rem;
    height: 70rem;
  }

  .modal-content {
    overflow: unset;
  }

  .modal-header {
    display: none;
  }

  .modal-footer {
    height: 8rem;
    border: none;
  }

  .modal-body {
    height: 62rem;
    position: unset;
    padding: unset;
  }
}

.rounded-modal {
  .modal-content {
    border-radius: 1rem;
  }
}

.topmost-modal ~ .modal-backdrop {
  // z-index: common.getElevation(topmost-modal);
}

.topmost-modal {
  // z-index: common.getElevation(topmost-modal);
}

.cdk-overlay-container {
  // z-index: common.getElevation(overlay-pane);

  .onboarding-overlay {
    background-color: theme('colors.color-brand-dark');
    opacity: 0.9;
  }
}

.large-document-preview-modal {
  // Set 1000 explicitly to match with the fin modal service z-index until we migrate the document preview modals from ngb modal to fin modal
  // Fixes a problem with Toast messages appearing under the modal
  z-index: 1000;

  .modal-dialog {
    max-width: 95vw;
  }

  .modal-content {
    border-radius: 0.8rem;
    overflow: hidden;
    border: none;
  }
}

.advanced-pdf-edit-modal {
  // z-index: common.getElevation(popups);

  .modal-dialog {
    width: 98vw;
    max-width: 160rem;
  }
}

.collaboration-invitations-result-modal {
  .modal-dialog {
    max-width: 56rem;
    height: 60rem;
  }
}

.confirmation-modal-medium {
  .modal-dialog {
    max-width: 55rem;
  }
}

.graph-extend-modal,
.graph-collapse-modal {
  max-width: 41rem;
}

.graph-review-updates-modal {
  max-width: 63.2rem;
}

.customer-user-info-modal {
  .modal-dialog {
    max-width: 60rem;
  }
}

.select-version-for-copy-modal {
  .modal-dialog {
    max-width: 41rem;
  }
}

.page-container {
  .ng-scroll-content,
  .scroll-content {
    overflow-anchor: none;
  }
}

ng-scrollbar.ng-scrollbar {
  --scrollbar-thumb-color: theme('colors.color-background-dark-moderate');
  --scrollbar-track-color: theme('colors.color-background-tertiary-minimal');
}

.custom-scrollbar {
  --scrollbar-thumb-hover-color: theme('colors.color-brand-dark') !important;
  --scrollbar-size: 2px !important;

  scrollbar-y {
    .ng-scrollbar-thumb {
      background-color: theme('colors.color-brand-dark');
    }

    .ng-scrollbar-track {
      background-color: theme('colors.color-surface-primary');
    }
  }
}

.scrollbar-outside-element {
  .ng-scrollbar-wrapper[deactivated='false'][dir='ltr']
    > scrollbar-y.scrollbar-control {
    right: -1.5rem;
  }
}

.create-contract-modal {
  // z-index: common.getElevation(popups);

  .modal-dialog {
    max-width: 60rem;
  }
}

.table-field-modal {
  .modal-dialog {
    max-width: 45rem;
  }
}

.add-circle {
  @include common.flex-justify();
  border-radius: 50%;
  width: 2.4rem;
  height: 2.4rem;
  margin-right: 2rem;
  background-color: theme('colors.color-brand-secondary');

  ui-icon {
    color: theme('colors.color-surface-primary');
  }
}

.common-gray {
  color: theme('colors.color-background-dark-moderate');
}

.createUserModalClass .modal-dialog {
  max-width: 80rem;
}

.data-grid-full-screen-modal {
  // z-index: common.getElevation(popups);

  .modal-dialog {
    width: 98vw;
    height: 60vh;
    max-width: 160rem;

    .modal-content {
      overflow: unset;
    }
  }
}

.case-reassignment-modal {
  .modal-content {
    width: 48rem;
  }
}

ngx-datatable ::-webkit-scrollbar-thumb {
  background-color: theme('colors.color-background-dark-moderate');
}

::-webkit-scrollbar-thumb {
  background-color: theme('colors.color-background-dark-moderate');
}

.content:has(app-company-branch-filters),  .content:has(app-business-case-data-room-filters){
  @apply tw-h-full;
}
